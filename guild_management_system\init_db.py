#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import hashlib
from datetime import datetime

# 数据库配置
DB_CONFIGS = [
    {
        'host': 'sh-cdb-r92w8slq.sql.tencentcdb.com',
        'port': 25366,
        'user': 'root',
        'password': 'Dorothy0423@',
        'charset': 'utf8mb4',
        'autocommit': True
    },
    {
        'host': 'sh-cdb-r92w8slq.sql.tencentcdb.com',
        'port': 25366,
        'user': 'root',
        'password': 'Dorothy0423',  # 备用密码
        'charset': 'utf8mb4',
        'autocommit': True
    }
]

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def test_connection():
    """测试数据库连接"""
    print("正在测试数据库连接...")
    
    for i, config in enumerate(DB_CONFIGS):
        try:
            print(f"尝试配置 {i+1}: 密码 {config['password']}")
            connection = pymysql.connect(**config)
            print("✅ 数据库连接成功！")
            
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"MySQL 版本: {version[0]}")
            
            connection.close()
            return config
            
        except Exception as e:
            print(f"❌ 配置 {i+1} 连接失败: {e}")
    
    print("❌ 所有配置都连接失败")
    return None

def init_database(db_config):
    """初始化数据库"""
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("正在创建数据库...")
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS guild_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ 数据库 guild_management 创建成功")
        
        # 使用数据库
        cursor.execute("USE guild_management")
        
        # 创建用户表
        print("正在创建用户表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                email VARCHAR(100),
                role ENUM('super_admin', 'guild_leader', 'user', 'guest') DEFAULT 'user',
                guild_id VARCHAR(50),
                real_name VARCHAR(100),
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
                INDEX idx_username (username),
                INDEX idx_guild_id (guild_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 用户表创建成功")
        
        # 创建帮会表
        print("正在创建帮会表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guilds (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                leader_username VARCHAR(50) NOT NULL,
                description TEXT,
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive') DEFAULT 'active',
                members_count INT DEFAULT 0,
                max_members INT DEFAULT 100,
                INDEX idx_leader (leader_username)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 帮会表创建成功")
        
        # 创建帮会申请表
        print("正在创建帮会申请表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_applications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                guild_id VARCHAR(50),
                application_type ENUM('create_guild', 'join_guild') NOT NULL,
                guild_name VARCHAR(100),
                description TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                processed_time DATETIME,
                processed_by VARCHAR(50),
                INDEX idx_username (username),
                INDEX idx_guild_id (guild_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 帮会申请表创建成功")
        
        # 创建帮会成员表
        print("正在创建帮会成员表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_members (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                name VARCHAR(100) NOT NULL,
                profession VARCHAR(50) NOT NULL,
                main_group VARCHAR(50),
                sub_team VARCHAR(50),
                squad VARCHAR(50),
                position VARCHAR(100),
                status ENUM('主力', '替补', '请假', '帮外') DEFAULT '主力',
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_guild_id (guild_id),
                INDEX idx_name (name),
                INDEX idx_profession (profession)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 帮会成员表创建成功")
        
        # 创建战斗记录表
        print("正在创建战斗记录表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS battle_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                battle_id VARCHAR(100) UNIQUE NOT NULL,
                our_guild VARCHAR(100) NOT NULL,
                enemy_guild VARCHAR(100) NOT NULL,
                battle_data JSON,
                member_performance JSON,
                upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                uploaded_by VARCHAR(50),
                INDEX idx_guild_id (guild_id),
                INDEX idx_battle_id (battle_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ 战斗记录表创建成功")
        
        # 插入初始用户数据
        print("正在插入初始用户数据...")
        
        # 超级管理员
        cursor.execute("""
            INSERT IGNORE INTO users (username, password_hash, role, real_name, status) 
            VALUES (%s, %s, %s, %s, %s)
        """, ('RaphaelL', hash_password('Dorothy0423@'), 'super_admin', '超级管理员', 'active'))
        
        # 大当家
        cursor.execute("""
            INSERT IGNORE INTO users (username, password_hash, role, real_name, guild_id, status) 
            VALUES (%s, %s, %s, %s, %s, %s)
        """, ('zhiluo19', hash_password('zhiluo19'), 'guild_leader', '大当家', 'zhiluoyunyan', 'active'))
        
        print("✅ 初始用户数据插入成功")
        
        # 插入默认帮会
        print("正在插入默认帮会数据...")
        cursor.execute("""
            INSERT IGNORE INTO guilds (id, name, leader_username, description, status) 
            VALUES (%s, %s, %s, %s, %s)
        """, ('zhiluoyunyan', '纸落云烟', 'zhiluo19', '默认帮会', 'active'))
        
        print("✅ 默认帮会数据插入成功")
        
        print("\n🎉 数据库初始化完成！")
        print("=" * 50)
        print("初始账号信息:")
        print("超级管理员: RaphaelL / Dorothy0423@")
        print("大当家: zhiluo19 / zhiluo19")
        print("默认帮会: 纸落云烟 (zhiluoyunyan)")
        print("=" * 50)
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 开始初始化数据库...")
    print("=" * 50)
    
    # 测试连接
    working_config = test_connection()
    if not working_config:
        print("❌ 无法连接到数据库，请检查配置")
        exit(1)
    
    # 初始化数据库
    if init_database(working_config):
        print("\n✅ 数据库初始化成功！现在可以启动应用了。")
        
        # 保存工作配置到文件
        with open('db_config.txt', 'w') as f:
            f.write(f"工作的数据库配置:\n")
            f.write(f"密码: {working_config['password']}\n")
            f.write(f"测试时间: {datetime.now()}\n")
        
    else:
        print("❌ 数据库初始化失败")
        exit(1)
