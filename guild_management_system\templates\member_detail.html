{% extends "base.html" %}

{% block title %}{{ member.name }} - 成员详情 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<!-- 游客注册引导横幅 -->
{% if get_current_user() and get_current_user().role == 'guest' %}
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 25px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="font-size: 48px;">🎮</div>
        <div style="flex: 1;">
            <h3 style="margin: 0 0 8px 0; font-size: 22px;">想要查看完整的战斗数据分析？</h3>
            <p style="margin: 0 0 15px 0; font-size: 16px; opacity: 0.9;">注册账号即可解锁详细的战斗表现、能力雷达图、成长趋势等强大功能</p>
            <div style="display: flex; gap: 15px;">
                <a href="{{ url_for('register') }}" style="background: rgba(255, 255, 255, 0.2); border: 2px solid rgba(255, 255, 255, 0.3); color: white; padding: 10px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 8px;">
                    <i class="fas fa-user-plus"></i> 立即注册
                </a>
                <a href="{{ url_for('login') }}" style="background: transparent; border: 2px solid rgba(255, 255, 255, 0.3); color: white; padding: 10px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 8px;">
                    <i class="fas fa-sign-in-alt"></i> 已有账号
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 返回按钮 -->
<div style="margin-bottom: 20px;">
    <button onclick="history.back()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
        ← 返回成员列表
    </button>
</div>

<!-- 成员基本信息卡片 -->
<div style="position: relative; margin-bottom: 30px; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.15);">
    <!-- 背景渐变 -->
    <div style="
        background: linear-gradient(135deg,
            {% if member.profession == '素问' %}#ff6b9d, #c44569{% elif member.profession == '九灵' %}#8e44ad, #9b59b6{% elif member.profession == '潮光' %}#3498db, #2980b9{% elif member.profession == '血河' %}#e74c3c, #c0392b{% elif member.profession == '神相' %}#2980b9, #3498db{% elif member.profession == '玄机' %}#f39c12, #e67e22{% elif member.profession == '铁衣' %}#e67e22, #d35400{% elif member.profession == '龙吟' %}#27ae60, #2ecc71{% elif member.profession == '碎梦' %}#16a085, #1abc9c{% else %}#6c5ce7, #a29bfe{% endif %}
        );
        padding: 30px;
        color: white;
        position: relative;
    ">
        <!-- 装饰性图案 -->
        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.2;"></div>

        <!-- 头部标题 -->
        <div style="position: relative; z-index: 2;">
            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 25px;">
                <div style="
                    background: rgba(255,255,255,0.2);
                    padding: 12px 20px;
                    border-radius: 25px;
                    font-size: 16px;
                    font-weight: bold;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.3);
                ">
                    {{ member.profession }}
                </div>
                <div style="flex: 1;">
                    <h1 style="margin: 0; font-size: 28px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                        {{ member.name }}
                    </h1>
                    <div style="font-size: 16px; opacity: 0.9; margin-top: 5px;">
                        {{ member.guild_name or '逆水寒' }}帮会成员档案
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细信息区域 -->
    <div style="background: white; padding: 30px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
            <div>
                <h4 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 18px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📋 基本信息
                </h4>
                <div style="font-size: 15px; line-height: 2; background: #f8f9fa; padding: 20px; border-radius: 12px;">
                    {% if member.guild_name %}
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">所属帮会：</strong>
                        <span class="profession-badge" style="background: #667eea; color: white; font-size: 13px;">{{ member.guild_name }}</span>
                    </div>
                    {% endif %}
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">职业：</strong>
                        <span class="profession-badge profession-{{ member.profession }}" style="font-size: 13px;">{{ member.profession }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">主团：</strong>
                        <span class="profession-badge" style="background: {% if member.get('main_group') == '进攻团' %}#28a745{% elif member.get('main_group') == '防守团' %}#dc3545{% else %}#6c757d{% endif %}; color: white; font-size: 13px;">{{ member.get('main_group', '其他团') }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">子团：</strong>
                        <span class="profession-badge" style="background: #667eea; color: white; font-size: 13px;">{% set sub_team = member.get('sub_team', '一团') %}{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">小队：</strong>
                        <span class="profession-badge" style="background: #17a2b8; color: white; font-size: 13px;">{{ member.get('squad', '1队') }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">位置：</strong>
                        <span class="profession-badge" style="background: #6c757d; color: white; font-size: 13px;">{{ member.position }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <strong style="color: #495057;">状态：</strong>
                        <span class="profession-badge" style="background: {% if member.status == '主力' %}#007bff{% elif member.status == '替补' %}#ffc107{% else %}#6c757d{% endif %}; color: white; font-size: 13px;">{{ member.status or '主力' }}</span>
                    </div>
                </div>
            </div>
        
            {% if battle_history %}
            <div>
                <h4 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 18px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📊 战斗数据概览
                </h4>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 12px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #667eea;">{{ battle_history|length }}</div>
                            <div style="font-size: 12px; color: #6c757d;">参战次数</div>
                        </div>
                        {% if battle_history %}
                        {% set avg_score = (battle_history|map(attribute='score')|sum / battle_history|length) %}
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: {% if avg_score >= 70 %}#28a745{% elif avg_score >= 50 %}#ffc107{% else %}#dc3545{% endif %};">{{ "%.1f"|format(avg_score) }}</div>
                            <div style="font-size: 12px; color: #6c757d;">平均评分</div>
                        </div>
                        {% set max_score = battle_history|map(attribute='score')|max %}
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #f39c12;">{{ "%.1f"|format(max_score) }}</div>
                            <div style="font-size: 12px; color: #6c757d;">最高评分</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% else %}
            <div>
                <h4 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 18px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📊 战斗数据
                </h4>
                <div style="background: #f8f9fa; padding: 30px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;">📊</div>
                    <h5 style="margin: 0 0 10px 0; color: #6c757d;">暂无战斗数据</h5>
                    <p style="margin: 0; color: #6c757d; font-size: 14px;">该成员还未参与帮战或数据尚未上传</p>
                </div>
            </div>
            {% endif %}


        </div>
    </div>
</div>

<!-- 多维度分析 -->
{% if battle_history %}
<div class="stat-card" style="margin-bottom: 20px;">
    <!-- 多维度表现分析 -->
    <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 6px 20px rgba(0,0,0,0.1); margin-bottom: 25px;">
        <div style="text-align: center; margin-bottom: 25px;">
            <h3 style="margin: 0; color: #2c3e50; font-size: 20px; font-weight: bold;">📊 多维度表现分析</h3>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <!-- 评分表现 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 12px; color: white;">
                <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📈 评分表现
                </h4>
                {% set scores = battle_history|map(attribute='score')|list %}
                {% if scores|length >= 1 %}
                    {% set latest_score = scores[-1] %}
                    {% set avg_score = (scores|sum / scores|length) %}
                    {% set max_score = scores|max %}
                    {% set min_score = scores|min %}

                    {% if scores|length >= 2 %}
                        {% set previous_score = scores[-2] %}
                        {% set trend = latest_score - previous_score %}
                    {% else %}
                        {% set trend = 0 %}
                    {% endif %}

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 13px;">
                        <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                            <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">当前评分</div>
                            <div style="font-size: 16px; font-weight: bold;">{{ "%.1f"|format(latest_score) }}分</div>
                        </div>
                        <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                            <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">平均评分</div>
                            <div style="font-size: 16px; font-weight: bold;">{{ "%.1f"|format(avg_score) }}分</div>
                        </div>
                        <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                            <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">
                                {% if scores|length >= 2 %}最佳表现{% else %}首次表现{% endif %}
                            </div>
                            <div style="font-size: 16px; font-weight: bold;">{{ "%.1f"|format(max_score) }}分</div>
                        </div>
                        <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                            <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">近期趋势</div>
                            <div style="font-size: 14px; font-weight: bold;">
                                {% if scores|length < 2 %}
                                    🆕 首次参战
                                {% elif trend > 0 %}
                                    📈 +{{ "%.1f"|format(trend) }}
                                {% elif trend < 0 %}
                                    📉 {{ "%.1f"|format(trend) }}
                                {% else %}
                                    ➡️ 持平
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% else %}
                    <!-- 没有战斗记录时的显示 -->
                    <div style="text-align: center; padding: 20px; opacity: 0.8;">
                        <div style="font-size: 24px; margin-bottom: 10px;">📊</div>
                        <div style="font-size: 14px;">暂无战斗记录</div>
                        <div style="font-size: 12px; margin-top: 5px;">参与帮战后将显示评分数据</div>
                    </div>
                {% endif %}
            </div>

            <!-- 职责表现 -->
            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 20px; border-radius: 12px; color: white;">
                <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    🎯 职责表现
                </h4>
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 13px; opacity: 0.9;">主要职责</span>
                        <span style="background: rgba(255,255,255,0.3); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">{{ member.position }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-size: 13px; opacity: 0.9;">职业</span>
                        <span style="background: rgba(255,255,255,0.3); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">{{ member.profession }}</span>
                    </div>
                </div>

                {% if battle_history %}
                    {% set latest_battle = battle_history[-1] %}
                    {% if latest_battle.bonus_items %}
                        <div style="margin-bottom: 12px;">
                            <div style="font-size: 12px; opacity: 0.9; margin-bottom: 6px;">✨ 优势表现：</div>
                            {% for bonus in latest_battle.bonus_items[:3] %}
                                <div style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 6px; font-size: 11px; margin-bottom: 3px;">
                                    ✓ {{ bonus }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                    {% if latest_battle.penalty_items %}
                        <div>
                            <div style="font-size: 12px; opacity: 0.9; margin-bottom: 6px;">💡 改进空间：</div>
                            {% for penalty in latest_battle.penalty_items[:2] %}
                                <div style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 6px; font-size: 11px; margin-bottom: 3px;">
                                    ⚠️ {{ penalty }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

        <!-- 能力雷达图 -->
        <div style="background: #f8f9fa; padding: 25px; border-radius: 12px; grid-column: 1 / -1; margin-top: 20px;">
            <h4 style="margin: 0 0 25px 0; color: #495057; text-align: center; font-size: 18px;">🎯 能力雷达图</h4>

            <!-- 主要内容区域 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: start;">

                <!-- 左侧：雷达图 -->
                <div style="display: flex; flex-direction: column; align-items: center;">
                    <svg width="380" height="380" viewBox="0 0 500 500" style="background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); margin-bottom: 15px; max-width: 100%;">
                    <!-- 背景网格 -->
                    <defs>
                        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e9ecef" stroke-width="1"/>
                        </pattern>
                    </defs>

                    <!-- 雷达图背景圆圈 -->
                    <g transform="translate(250, 250)">
                        <!-- 同心圆 -->
                        <circle cx="0" cy="0" r="35" fill="none" stroke="#e9ecef" stroke-width="1"/>
                        <circle cx="0" cy="0" r="70" fill="none" stroke="#e9ecef" stroke-width="1"/>
                        <circle cx="0" cy="0" r="105" fill="none" stroke="#e9ecef" stroke-width="1"/>
                        <circle cx="0" cy="0" r="140" fill="none" stroke="#e9ecef" stroke-width="1"/>
                        <circle cx="0" cy="0" r="175" fill="none" stroke="#e9ecef" stroke-width="2"/>
                        <!-- 100%参考线（团队平均水平） -->
                        <circle cx="0" cy="0" r="87.5" fill="none" stroke="#6c757d" stroke-width="2" stroke-dasharray="3,3" opacity="0.8"/>

                        <!-- 数值标记（百分比） -->
                        <text x="8" y="-30" font-size="11" fill="#6c757d" font-weight="bold">40%</text>
                        <text x="8" y="-65" font-size="11" fill="#6c757d" font-weight="bold">80%</text>
                        <text x="8" y="-82" font-size="12" fill="#6c757d" font-weight="bold" style="text-decoration: underline;">100%</text>
                        <text x="8" y="-100" font-size="11" fill="#6c757d" font-weight="bold">120%</text>
                        <text x="8" y="-135" font-size="11" fill="#6c757d" font-weight="bold">160%</text>
                        <text x="8" y="-170" font-size="11" fill="#6c757d" font-weight="bold">200%</text>

                        <!-- 轴线 -->
                        <line x1="0" y1="0" x2="0" y2="-175" stroke="#dee2e6" stroke-width="1"/>
                        <line x1="0" y1="0" x2="151.6" y2="-87.5" stroke="#dee2e6" stroke-width="1"/>
                        <line x1="0" y1="0" x2="151.6" y2="87.5" stroke="#dee2e6" stroke-width="1"/>
                        <line x1="0" y1="0" x2="0" y2="175" stroke="#dee2e6" stroke-width="1"/>
                        <line x1="0" y1="0" x2="-151.6" y2="87.5" stroke="#dee2e6" stroke-width="1"/>
                        <line x1="0" y1="0" x2="-151.6" y2="-87.5" stroke="#dee2e6" stroke-width="1"/>

                        {% if battle_history %}
                            {% set latest_battle = battle_history[-1] %}
                            {% set battle_data = latest_battle.battle_data %}

                            <!-- 检查是否有有效的战斗数据 -->
                            {% set has_valid_data = battle_data and (
                                (battle_data.player_damage and battle_data.player_damage > 0) or
                                (battle_data.kills and battle_data.kills > 0) or
                                (battle_data.resources and battle_data.resources > 0) or
                                (battle_data.healing and battle_data.healing > 0) or
                                (battle_data.building_damage and battle_data.building_damage > 0)
                            ) %}

                            {% if has_valid_data %}
                            <!-- 使用团队平均值进行对比（根据职业和职责设定合理的团队平均值） -->
                            {% if member.profession == '素问' %}
                                {% set team_avg_player_damage = 15000000 %}
                                {% set team_avg_healing = 80000000 %}
                                {% set team_avg_kills = 3 %}
                                {% set team_avg_resources = 50 %}
                                {% set team_avg_heavy_injuries = 2 %}
                                {% set team_avg_building_damage = 8000000 %}
                            {% elif member.profession == '铁衣' %}
                                {% set team_avg_player_damage = 25000000 %}
                                {% set team_avg_healing = 15000000 %}
                                {% set team_avg_kills = 8 %}
                                {% set team_avg_resources = 80 %}
                                {% set team_avg_heavy_injuries = 1 %}
                                {% set team_avg_building_damage = 15000000 %}
                            {% elif member.profession == '潮光' %}
                                {% set team_avg_player_damage = 45000000 %}
                                {% set team_avg_healing = 8000000 %}
                                {% set team_avg_kills = 12 %}
                                {% set team_avg_resources = 120 %}
                                {% set team_avg_heavy_injuries = 3 %}
                                {% set team_avg_building_damage = 25000000 %}
                            {% else %}
                                <!-- 输出职业：九灵、血河、玄机、龙吟、神相、碎梦 -->
                                {% set team_avg_player_damage = 60000000 %}
                                {% set team_avg_healing = 5000000 %}
                                {% set team_avg_kills = 18 %}
                                {% set team_avg_resources = 150 %}
                                {% set team_avg_heavy_injuries = 4 %}
                                {% set team_avg_building_damage = 35000000 %}
                            {% endif %}

                            <!-- 计算相对表现百分比（当前值相对于团队平均值的表现） -->
                            {% set damage_ratio = (battle_data.player_damage / team_avg_player_damage * 100)|round|int if battle_data.player_damage and team_avg_player_damage > 0 else 0 %}
                            {% set healing_ratio = (battle_data.healing / team_avg_healing * 100)|round|int if battle_data.healing and team_avg_healing > 0 else 0 %}
                            {% set kill_ratio = (battle_data.kills / team_avg_kills * 100)|round|int if battle_data.kills and team_avg_kills > 0 else 0 %}
                            {% set resource_ratio = (battle_data.resources / team_avg_resources * 100)|round|int if battle_data.resources and team_avg_resources > 0 else 0 %}
                            {% set survival_ratio = ((2 * team_avg_heavy_injuries - battle_data.heavy_injuries) / team_avg_heavy_injuries * 100)|round|int if battle_data.heavy_injuries is defined and team_avg_heavy_injuries > 0 else 100 %}
                            {% set building_ratio = (battle_data.building_damage / team_avg_building_damage * 100)|round|int if battle_data.building_damage and team_avg_building_damage > 0 else 0 %}

                            <!-- 限制合理范围 -->
                            {% if damage_ratio > 200 %}
                                {% set damage_ratio = 200 %}
                            {% elif damage_ratio < 0 %}
                                {% set damage_ratio = 0 %}
                            {% endif %}

                            {% if healing_ratio > 200 %}
                                {% set healing_ratio = 200 %}
                            {% elif healing_ratio < 0 %}
                                {% set healing_ratio = 0 %}
                            {% endif %}

                            {% if kill_ratio > 200 %}
                                {% set kill_ratio = 200 %}
                            {% elif kill_ratio < 0 %}
                                {% set kill_ratio = 0 %}
                            {% endif %}

                            {% if resource_ratio > 200 %}
                                {% set resource_ratio = 200 %}
                            {% elif resource_ratio < 0 %}
                                {% set resource_ratio = 0 %}
                            {% endif %}

                            {% if survival_ratio > 200 %}
                                {% set survival_ratio = 200 %}
                            {% elif survival_ratio < 0 %}
                                {% set survival_ratio = 0 %}
                            {% endif %}

                            {% if building_ratio > 200 %}
                                {% set building_ratio = 200 %}
                            {% elif building_ratio < 0 %}
                                {% set building_ratio = 0 %}
                            {% endif %}

                            <!-- 计算雷达图坐标（实际数据，缩放到合适比例） -->
                            <!-- 将200%作为最大半径175，所以比例系数是175/200=0.875 -->
                            {% set scale_factor = 0.875 %}
                            {% set damage_x = 0 %}
                            {% set damage_y = -(damage_ratio * scale_factor) %}
                            {% set kill_x = (kill_ratio * scale_factor * 0.866) %}
                            {% set kill_y = -(kill_ratio * scale_factor * 0.5) %}
                            {% set resource_x = (resource_ratio * scale_factor * 0.866) %}
                            {% set resource_y = (resource_ratio * scale_factor * 0.5) %}
                            {% set survival_x = 0 %}
                            {% set survival_y = (survival_ratio * scale_factor) %}
                            {% set healing_x = -(healing_ratio * scale_factor * 0.866) %}
                            {% set healing_y = (healing_ratio * scale_factor * 0.5) %}
                            {% set building_x = -(building_ratio * scale_factor * 0.866) %}
                            {% set building_y = -(building_ratio * scale_factor * 0.5) %}

                            <!-- 计算平均参考线坐标（100%为基准线） -->
                            {% set avg_damage_x = 0 %}
                            {% set avg_damage_y = -(100 * scale_factor) %}
                            {% set avg_kill_x = (100 * scale_factor * 0.866) %}
                            {% set avg_kill_y = -(100 * scale_factor * 0.5) %}
                            {% set avg_resource_x = (100 * scale_factor * 0.866) %}
                            {% set avg_resource_y = (100 * scale_factor * 0.5) %}
                            {% set avg_survival_x = 0 %}
                            {% set avg_survival_y = (100 * scale_factor) %}
                            {% set avg_healing_x = -(100 * scale_factor * 0.866) %}
                            {% set avg_healing_y = (100 * scale_factor * 0.5) %}
                            {% set avg_building_x = -(100 * scale_factor * 0.866) %}
                            {% set avg_building_y = -(100 * scale_factor * 0.5) %}

                            <!-- 根据职业设置颜色 -->
                            {% if member.profession == '素问' %}
                                {% set radar_color = 'rgba(255, 182, 193, 0.3)' %}
                                {% set radar_stroke = 'rgba(255, 105, 180, 1)' %}
                                {% set point_color = '#ff69b4' %}
                            {% elif member.profession == '九灵' %}
                                {% set radar_color = 'rgba(138, 43, 226, 0.3)' %}
                                {% set radar_stroke = 'rgba(138, 43, 226, 1)' %}
                                {% set point_color = '#8a2be2' %}
                            {% elif member.profession == '潮光' %}
                                {% set radar_color = 'rgba(173, 216, 230, 0.3)' %}
                                {% set radar_stroke = 'rgba(135, 206, 235, 1)' %}
                                {% set point_color = '#87ceeb' %}
                            {% elif member.profession == '血河' %}
                                {% set radar_color = 'rgba(255, 99, 71, 0.3)' %}
                                {% set radar_stroke = 'rgba(255, 69, 0, 1)' %}
                                {% set point_color = '#ff4500' %}
                            {% elif member.profession == '神相' %}
                                {% set radar_color = 'rgba(70, 130, 180, 0.3)' %}
                                {% set radar_stroke = 'rgba(70, 130, 180, 1)' %}
                                {% set point_color = '#4682b4' %}
                            {% elif member.profession == '玄机' %}
                                {% set radar_color = 'rgba(255, 215, 0, 0.3)' %}
                                {% set radar_stroke = 'rgba(255, 215, 0, 1)' %}
                                {% set point_color = '#ffd700' %}
                            {% elif member.profession == '铁衣' %}
                                {% set radar_color = 'rgba(255, 140, 0, 0.3)' %}
                                {% set radar_stroke = 'rgba(255, 140, 0, 1)' %}
                                {% set point_color = '#ff8c00' %}
                            {% elif member.profession == '龙吟' %}
                                {% set radar_color = 'rgba(34, 139, 34, 0.3)' %}
                                {% set radar_stroke = 'rgba(34, 139, 34, 1)' %}
                                {% set point_color = '#228b22' %}
                            {% elif member.profession == '碎梦' %}
                                {% set radar_color = 'rgba(0, 100, 0, 0.3)' %}
                                {% set radar_stroke = 'rgba(0, 100, 0, 1)' %}
                                {% set point_color = '#006400' %}
                            {% else %}
                                {% set radar_color = 'rgba(54, 162, 235, 0.3)' %}
                                {% set radar_stroke = 'rgba(54, 162, 235, 1)' %}
                                {% set point_color = '#36a2eb' %}
                            {% endif %}

                            <!-- 平均参考线（虚线多边形） -->
                            <polygon points="{{ avg_damage_x }},{{ avg_damage_y }} {{ avg_kill_x }},{{ avg_kill_y }} {{ avg_resource_x }},{{ avg_resource_y }} {{ avg_survival_x }},{{ avg_survival_y }} {{ avg_healing_x }},{{ avg_healing_y }} {{ avg_building_x }},{{ avg_building_y }}"
                                     fill="none"
                                     stroke="#6c757d"
                                     stroke-width="2"
                                     stroke-dasharray="5,5"
                                     opacity="0.7"/>

                            <!-- 实际数据多边形 -->
                            <polygon points="{{ damage_x }},{{ damage_y }} {{ kill_x }},{{ kill_y }} {{ resource_x }},{{ resource_y }} {{ survival_x }},{{ survival_y }} {{ healing_x }},{{ healing_y }} {{ building_x }},{{ building_y }}"
                                     fill="{{ radar_color }}"
                                     stroke="{{ radar_stroke }}"
                                     stroke-width="3"/>

                            <!-- 平均参考点 -->
                            <circle cx="{{ avg_damage_x }}" cy="{{ avg_damage_y }}" r="3" fill="#6c757d" opacity="0.7"/>
                            <circle cx="{{ avg_kill_x }}" cy="{{ avg_kill_y }}" r="3" fill="#6c757d" opacity="0.7"/>
                            <circle cx="{{ avg_resource_x }}" cy="{{ avg_resource_y }}" r="3" fill="#6c757d" opacity="0.7"/>
                            <circle cx="{{ avg_survival_x }}" cy="{{ avg_survival_y }}" r="3" fill="#6c757d" opacity="0.7"/>
                            <circle cx="{{ avg_healing_x }}" cy="{{ avg_healing_y }}" r="3" fill="#6c757d" opacity="0.7"/>
                            <circle cx="{{ avg_building_x }}" cy="{{ avg_building_y }}" r="3" fill="#6c757d" opacity="0.7"/>

                            <!-- 实际数据点 -->
                            <circle cx="{{ damage_x }}" cy="{{ damage_y }}" r="5" fill="{{ point_color }}" stroke="white" stroke-width="2"/>
                            <circle cx="{{ kill_x }}" cy="{{ kill_y }}" r="5" fill="{{ point_color }}" stroke="white" stroke-width="2"/>
                            <circle cx="{{ resource_x }}" cy="{{ resource_y }}" r="5" fill="{{ point_color }}" stroke="white" stroke-width="2"/>
                            <circle cx="{{ survival_x }}" cy="{{ survival_y }}" r="5" fill="{{ point_color }}" stroke="white" stroke-width="2"/>
                            <circle cx="{{ healing_x }}" cy="{{ healing_y }}" r="5" fill="{{ point_color }}" stroke="white" stroke-width="2"/>
                            <circle cx="{{ building_x }}" cy="{{ building_y }}" r="5" fill="{{ point_color }}" stroke="white" stroke-width="2"/>
                            {% else %}
                            <!-- 未参与战斗的显示 -->
                            <text x="0" y="0" text-anchor="middle" font-size="16" font-weight="bold" fill="#6c757d">
                                未参与战斗
                            </text>
                            <text x="0" y="20" text-anchor="middle" font-size="12" fill="#6c757d">
                                该成员在最近一次战斗中未产生有效数据
                            </text>
                            {% endif %}
                        {% endif %}

                        <!-- 标签 -->
                        <text x="0" y="-195" text-anchor="middle" font-size="13" font-weight="bold" fill="#495057">伤害输出</text>
                        <text x="170" y="-80" text-anchor="middle" font-size="13" font-weight="bold" fill="#495057">击杀能力</text>
                        <text x="170" y="100" text-anchor="middle" font-size="13" font-weight="bold" fill="#495057">资源贡献</text>
                        <text x="0" y="205" text-anchor="middle" font-size="13" font-weight="bold" fill="#495057">生存能力</text>
                        <text x="-170" y="100" text-anchor="middle" font-size="13" font-weight="bold" fill="#495057">治疗支援</text>
                        <text x="-170" y="-80" text-anchor="middle" font-size="13" font-weight="bold" fill="#495057">建筑破坏</text>
                    </g>
                </svg>

                <!-- 雷达图说明 -->
                <div style="text-align: center; font-size: 12px; color: #6c757d; max-width: 400px;">
                    <div style="margin-bottom: 8px; font-weight: 500;">能力雷达图说明</div>
                    <div style="line-height: 1.4;">
                        实线区域表示最近一次战斗表现，虚线表示同职业团队平均水平。<br>
                        100%为团队平均线，超过100%表示超越团队平均，低于100%表示有待提升。
                    </div>
                </div>
                </div>

                <!-- 右侧：数据对比和综合评价 -->
                <div style="display: flex; flex-direction: column; gap: 20px;">
                    <!-- 数据对比卡片 -->
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                        <h5 style="margin: 0 0 20px 0; color: #495057; text-align: center; font-size: 16px; font-weight: bold;">📊 数据对比</h5>

                        {% if battle_history %}
                            {% set latest_battle = battle_history[-1] %}
                            {% set battle_data = latest_battle.battle_data %}

                            {% if has_valid_data %}
                            <!-- 图例 -->
                            <div style="display: flex; justify-content: center; gap: 25px; margin-bottom: 25px; font-size: 13px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 24px; height: 4px; background: {{ radar_stroke }}; border-radius: 2px;"></div>
                                    <span style="font-weight: 500;">本次表现</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <div style="width: 24px; height: 4px; background: #6c757d; border-radius: 2px; border: 1px dashed #6c757d;"></div>
                                    <span style="font-weight: 500;">团队均值</span>
                                </div>
                            </div>

                            <!-- 详细数据对比 -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                                        <span style="font-size: 14px; font-weight: bold;">🗡️ 伤害输出</span>
                                        <span style="font-size: 13px; font-weight: bold; color: {% if damage_ratio > 100 %}#28a745{% elif damage_ratio < 100 %}#dc3545{% else %}#6c757d{% endif %};">
                                            {{ damage_ratio }}% {% if damage_ratio > 100 %}↗️{% elif damage_ratio < 100 %}↘️{% else %}➡️{% endif %}
                                        </span>
                                    </div>
                                    <div style="font-size: 11px; color: #6c757d;">
                                        本次: {{ "{:,}".format(battle_data.player_damage|default(0)) }} | 团队均值: {{ "{:,}".format(team_avg_player_damage) }}
                                    </div>
                                </div>

                                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                                        <span style="font-size: 14px; font-weight: bold;">⚔️ 击杀能力</span>
                                        <span style="font-size: 13px; font-weight: bold; color: {% if kill_ratio > 100 %}#28a745{% elif kill_ratio < 100 %}#dc3545{% else %}#6c757d{% endif %};">
                                            {{ kill_ratio }}% {% if kill_ratio > 100 %}↗️{% elif kill_ratio < 100 %}↘️{% else %}➡️{% endif %}
                                        </span>
                                    </div>
                                    <div style="font-size: 11px; color: #6c757d;">
                                        本次: {{ battle_data.kills|default(0) }} | 团队均值: {{ team_avg_kills }}
                                    </div>
                                </div>

                                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                                        <span style="font-size: 14px; font-weight: bold;">💎 资源贡献</span>
                                        <span style="font-size: 13px; font-weight: bold; color: {% if resource_ratio > 100 %}#28a745{% elif resource_ratio < 100 %}#dc3545{% else %}#6c757d{% endif %};">
                                            {{ resource_ratio }}% {% if resource_ratio > 100 %}↗️{% elif resource_ratio < 100 %}↘️{% else %}➡️{% endif %}
                                        </span>
                                    </div>
                                    <div style="font-size: 11px; color: #6c757d;">
                                        本次: {{ battle_data.resources|default(0) }} | 团队均值: {{ team_avg_resources }}
                                    </div>
                                </div>

                                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                                        <span style="font-size: 14px; font-weight: bold;">🛡️ 生存能力</span>
                                        <span style="font-size: 13px; font-weight: bold; color: {% if survival_ratio > 100 %}#28a745{% elif survival_ratio < 100 %}#dc3545{% else %}#6c757d{% endif %};">
                                            {{ survival_ratio }}% {% if survival_ratio > 100 %}↗️{% elif survival_ratio < 100 %}↘️{% else %}➡️{% endif %}
                                        </span>
                                    </div>
                                    <div style="font-size: 11px; color: #6c757d;">
                                        本次重伤: {{ battle_data.heavy_injuries|default(0) }} | 团队均值: {{ team_avg_heavy_injuries }}
                                    </div>
                                </div>

                                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                                        <span style="font-size: 14px; font-weight: bold;">💚 治疗支援</span>
                                        <span style="font-size: 13px; font-weight: bold; color: {% if healing_ratio > 100 %}#28a745{% elif healing_ratio < 100 %}#dc3545{% else %}#6c757d{% endif %};">
                                            {{ healing_ratio }}% {% if healing_ratio > 100 %}↗️{% elif healing_ratio < 100 %}↘️{% else %}➡️{% endif %}
                                        </span>
                                    </div>
                                    <div style="font-size: 11px; color: #6c757d;">
                                        本次: {{ "{:,}".format(battle_data.healing|default(0)) }} | 团队均值: {{ "{:,}".format(team_avg_healing) }}
                                    </div>
                                </div>

                                <div style="padding: 12px; background: #f8f9fa; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
                                        <span style="font-size: 14px; font-weight: bold;">🏗️ 建筑破坏</span>
                                        <span style="font-size: 13px; font-weight: bold; color: {% if building_ratio > 100 %}#28a745{% elif building_ratio < 100 %}#dc3545{% else %}#6c757d{% endif %};">
                                            {{ building_ratio }}% {% if building_ratio > 100 %}↗️{% elif building_ratio < 100 %}↘️{% else %}➡️{% endif %}
                                        </span>
                                    </div>
                                    <div style="font-size: 11px; color: #6c757d;">
                                        本次: {{ "{:,}".format(battle_data.building_damage|default(0)) }} | 团队均值: {{ "{:,}".format(team_avg_building_damage) }}
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <!-- 未参与战斗的数据对比显示 -->
                            <div style="text-align: center; padding: 40px; color: #6c757d;">
                                <div style="font-size: 48px; margin-bottom: 15px;">😴</div>
                                <h4 style="margin: 0 0 10px 0; color: #495057;">未参与战斗</h4>
                                <p style="margin: 0; font-size: 14px; line-height: 1.5;">
                                    该成员在最近一次战斗中未产生有效数据<br>
                                    可能是请假、替补或其他原因未参与
                                </p>
                            </div>
                            {% endif %}

                        {% endif %}
                    </div>

                    <!-- 综合评价卡片 -->
                    {% if battle_history %}
                        {% set latest_battle = battle_history[-1] %}
                        {% set battle_data = latest_battle.battle_data %}

                        {% if has_valid_data %}
                        {% set total_ratio = damage_ratio + kill_ratio + resource_ratio + survival_ratio + healing_ratio + building_ratio %}
                        {% set avg_total = 600 %}  <!-- 6个指标 × 100% = 600% -->
                        {% set performance_ratio = (total_ratio / avg_total * 100)|round if avg_total > 0 else 100 %}

                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center;">
                            <h5 style="margin: 0 0 20px 0; color: #495057; font-size: 16px; font-weight: bold;">🏆 综合评价</h5>

                            <div style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">
                                <span style="color: {% if performance_ratio >= 110 %}#28a745{% elif performance_ratio >= 90 %}#ffc107{% else %}#dc3545{% endif %};">
                                    {{ performance_ratio }}%
                                </span>
                            </div>

                            <div style="font-size: 14px; color: #6c757d; margin-bottom: 15px;">
                                {% if performance_ratio >= 110 %}
                                    🌟 表现优秀，超越平均水平
                                {% elif performance_ratio >= 90 %}
                                    👍 表现良好，接近平均水平
                                {% else %}
                                    💪 还有提升空间，继续努力
                                {% endif %}
                            </div>

                            <!-- 进度条 -->
                            <div style="width: 100%; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin-bottom: 10px;">
                                <div style="height: 100%; background: {% if performance_ratio >= 110 %}#28a745{% elif performance_ratio >= 90 %}#ffc107{% else %}#dc3545{% endif %}; width: {{ performance_ratio if performance_ratio <= 100 else 100 }}%; transition: width 0.3s ease;"></div>
                            </div>

                            <div style="font-size: 12px; color: #6c757d;">
                                基于最近一次战斗数据分析
                            </div>
                        </div>
                        {% else %}
                        <!-- 未参与战斗的综合评价显示 -->
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center;">
                            <h5 style="margin: 0 0 20px 0; color: #495057; font-size: 16px; font-weight: bold;">🏆 综合评价</h5>

                            <div style="font-size: 48px; margin-bottom: 15px;">🚫</div>

                            <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #6c757d;">
                                暂无评分
                            </div>

                            <div style="font-size: 14px; color: #6c757d; margin-bottom: 15px;">
                                该成员未参与最近一次战斗
                            </div>

                            <div style="font-size: 12px; color: #6c757d;">
                                请等待下次战斗数据更新
                            </div>
                        </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 稳定性分析 -->
        {% if battle_history|length >= 3 %}
        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">稳定性分析</h4>
            {% set scores = battle_history|map(attribute='score')|list %}
            {% set avg_score = (scores|sum / scores|length) %}
            {% set variance = ((scores|map('float')|map('pow', 2)|sum) / scores|length) - (avg_score ** 2) %}
            {% set std_dev = variance ** 0.5 %}

            <div style="font-size: 14px; line-height: 1.6;">
                <div><strong>表现稳定性：</strong>
                    <span style="color: {% if std_dev < 10 %}#28a745{% elif std_dev < 20 %}#ffc107{% else %}#dc3545{% endif %}; font-weight: bold;">
                        {% if std_dev < 10 %}
                            🟢 非常稳定
                        {% elif std_dev < 20 %}
                            🟡 较为稳定
                        {% else %}
                            🔴 波动较大
                        {% endif %}
                    </span>
                </div>
                <div><strong>标准差：</strong>{{ "%.1f"|format(std_dev) }}</div>
                <div><strong>一致性：</strong>
                    {% set consistency = 100 - (std_dev / avg_score * 100) if avg_score > 0 else 0 %}
                    {{ "%.0f"|format(consistency) }}%
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<!-- 战斗历史 -->
{% if battle_history %}
<div class="stat-card">
    <h3 style="margin: 0 0 20px 0; color: #2c3e50;">📊 战斗历史记录</h3>
    
    <!-- 评分趋势图 -->
    <div style="margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
        <h4 style="margin: 0 0 15px 0; color: #495057;">评分趋势</h4>
        <div id="scoreChart" style="height: 200px; position: relative;">
            <!-- 简单的评分趋势可视化 -->
            <div style="display: flex; align-items: end; height: 100%; gap: 5px;">
                {% for battle in battle_history %}
                <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                    <div style="background: {% if battle.score >= 60 %}#28a745{% elif battle.score >= 40 %}#ffc107{% else %}#dc3545{% endif %}; 
                                width: 100%; 
                                height: {{ (battle.score / 100 * 150)|int }}px; 
                                min-height: 10px;
                                border-radius: 3px 3px 0 0;
                                position: relative;">
                        <div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); 
                                    font-size: 10px; color: #495057; white-space: nowrap;">
                            {{ "%.0f"|format(battle.score) }}
                        </div>
                    </div>
                    <div style="font-size: 10px; color: #6c757d; margin-top: 5px; text-align: center;">
                        {{ battle.battle_date }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- 详细战斗记录表格 -->
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 12px; text-align: left; font-weight: bold; color: #495057;">战斗日期</th>
                    <th style="padding: 12px; text-align: left; font-weight: bold; color: #495057;">对手</th>
                    <th style="padding: 12px; text-align: center; font-weight: bold; color: #495057;">评分</th>
                    <th style="padding: 12px; text-align: center; font-weight: bold; color: #495057;">等级</th>
                    <th style="padding: 12px; text-align: left; font-weight: bold; color: #495057;">主要表现</th>
                    <th style="padding: 12px; text-align: center; font-weight: bold; color: #495057;">详细数据</th>
                </tr>
            </thead>
            <tbody>
                {% for battle in battle_history|reverse %}
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 12px;">{{ battle.battle_date }}</td>
                    <td style="padding: 12px;">{{ battle.enemy_guild }}</td>
                    <td style="padding: 12px; text-align: center;">
                        <span style="color: {% if battle.score >= 60 %}#28a745{% elif battle.score >= 40 %}#ffc107{% else %}#dc3545{% endif %}; font-weight: bold;">
                            {{ "%.1f"|format(battle.score) }}
                        </span>
                    </td>
                    <td style="padding: 12px; text-align: center;">
                        <span class="profession-badge" style="background: {% if battle.rating == 'S' %}#6f42c1{% elif battle.rating == 'A' %}#28a745{% elif battle.rating == 'B' %}#007bff{% elif battle.rating == 'C' %}#ffc107{% else %}#dc3545{% endif %}; color: white;">
                            {{ battle.rating }}
                        </span>
                    </td>
                    <td style="padding: 12px; font-size: 12px;">
                        {% if battle.bonus_items %}
                            {% for bonus in battle.bonus_items[:2] %}
                                <div style="color: #28a745; margin: 2px 0;">✓ {{ bonus }}</div>
                            {% endfor %}
                        {% endif %}
                        {% if battle.penalty_items %}
                            {% for penalty in battle.penalty_items[:2] %}
                                <div style="color: #dc3545; margin: 2px 0;">✗ {{ penalty }}</div>
                            {% endfor %}
                        {% endif %}
                    </td>
                    <td style="padding: 12px; text-align: center;">
                        <button onclick="toggleBattleDetail('{{ loop.index0 }}')" style="background: #17a2b8; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px;">
                            查看
                        </button>
                    </td>
                </tr>
                <!-- 详细数据行（默认隐藏） -->
                <tr id="detail-{{ loop.index0 }}" style="display: none; background: #f8f9fa;">
                    <td colspan="6" style="padding: 15px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            {% if battle.battle_data %}
                            <div>
                                <h5 style="margin: 0 0 10px 0; color: #495057;">战斗数据</h5>
                                <div style="font-size: 12px; line-height: 1.4;">
                                    <div>击杀: {{ battle.battle_data.kills|default(0) }}</div>
                                    <div>助攻: {{ battle.battle_data.assists|default(0) }}</div>
                                    <div>资源: {{ battle.battle_data.resources|default(0) }}</div>
                                    <div>重伤: {{ battle.battle_data.heavy_injuries|default(0) }}</div>
                                </div>
                            </div>
                            <div>
                                <h5 style="margin: 0 0 10px 0; color: #495057;">伤害数据</h5>
                                <div style="font-size: 12px; line-height: 1.4;">
                                    <div>玩家伤害: {{ "{:,}".format(battle.battle_data.player_damage|default(0)) }}</div>
                                    <div>建筑伤害: {{ "{:,}".format(battle.battle_data.building_damage|default(0)) }}</div>
                                    <div>治疗量: {{ "{:,}".format(battle.battle_data.healing|default(0)) }}</div>
                                    <div>承受伤害: {{ "{:,}".format(battle.battle_data.damage_taken|default(0)) }}</div>
                                </div>
                            </div>
                            <div>
                                <h5 style="margin: 0 0 10px 0; color: #495057;">特殊数据</h5>
                                <div style="font-size: 12px; line-height: 1.4;">
                                    {% if battle.battle_data.resurrections and battle.battle_data.resurrections > 0 %}
                                    <div>羽化: {{ battle.battle_data.resurrections }}</div>
                                    {% endif %}
                                    {% if battle.battle_data.qingquan and battle.battle_data.qingquan > 0 %}
                                    <div>清泉: {{ battle.battle_data.qingquan }}</div>
                                    {% endif %}
                                    {% if battle.battle_data.demolitions and battle.battle_data.demolitions > 0 %}
                                    <div>拆迁: {{ battle.battle_data.demolitions }}</div>
                                    {% endif %}
                                    {% if battle.battle_data.fenggu and battle.battle_data.fenggu > 0 %}
                                    <div>焚骨: {{ battle.battle_data.fenggu }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            {% else %}
                            <div style="text-align: center; padding: 20px; color: #6c757d;">
                                <p>该战斗暂无详细数据</p>
                            </div>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% else %}
<div class="stat-card">
    <div style="text-align: center; padding: 40px; color: #6c757d;">
        <h3>暂无战斗记录</h3>
        <p>该成员还没有参与过任何帮战</p>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function toggleBattleDetail(index) {
    const detailRow = document.getElementById('detail-' + index);
    if (detailRow.style.display === 'none') {
        detailRow.style.display = 'table-row';
    } else {
        detailRow.style.display = 'none';
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('成员详情页面加载完成');
});
</script>
{% endblock %}
