#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索功能
"""

import json
import os

def test_search():
    """测试搜索功能"""
    print("测试搜索功能...")
    
    # 加载成员数据
    members_file = 'data/guild_members.json'
    if not os.path.exists(members_file):
        print(f"❌ 成员数据文件不存在: {members_file}")
        return
    
    try:
        with open(members_file, 'r', encoding='utf-8') as f:
            members = json.load(f)
        
        print(f"✅ 成功加载 {len(members)} 个成员")
        
        # 测试搜索"金叶叶"
        query = "金叶叶"
        results = []
        
        for member in members:
            if (query in member['name'] or 
                query in member['profession'] or 
                (member.get('main_group') and query in member['main_group']) or
                (member.get('sub_team') and query in member['sub_team'])):
                results.append(member)
        
        print(f"搜索 '{query}' 找到 {len(results)} 个结果:")
        for result in results:
            print(f"  - {result['name']} ({result['profession']}) - {result.get('main_group', '未分配')} {result.get('sub_team', '未分配')}")
        
        # 测试搜索"碎梦"
        query = "碎梦"
        results = []
        
        for member in members:
            if (query in member['name'] or 
                query in member['profession'] or 
                (member.get('main_group') and query in member['main_group']) or
                (member.get('sub_team') and query in member['sub_team'])):
                results.append(member)
        
        print(f"\n搜索 '{query}' 找到 {len(results)} 个结果:")
        for i, result in enumerate(results[:5]):  # 只显示前5个
            print(f"  - {result['name']} ({result['profession']}) - {result.get('main_group', '未分配')} {result.get('sub_team', '未分配')}")
        if len(results) > 5:
            print(f"  ... 还有 {len(results) - 5} 个结果")
            
    except Exception as e:
        print(f"❌ 加载成员数据失败: {e}")

if __name__ == '__main__':
    test_search()
