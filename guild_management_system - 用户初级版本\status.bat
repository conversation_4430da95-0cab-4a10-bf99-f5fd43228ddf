@echo off
chcp 65001 >nul

echo 📊 纸落云烟帮会管理系统 - 服务状态
echo ==================================================

echo 🔍 端口占用情况:
echo    端口 5888 (管理后台):
netstat -an | find ":5888" >nul
if not errorlevel 1 (
    echo    ✅ 正在运行
    netstat -an | find ":5888"
) else (
    echo    ❌ 未运行
)

echo    端口 5002 (API服务):
netstat -an | find ":5002" >nul
if not errorlevel 1 (
    echo    ✅ 正在运行
    netstat -an | find ":5002"
) else (
    echo    ❌ 未运行
)

echo    端口 5000 (成员访问):
netstat -an | find ":5000" >nul
if not errorlevel 1 (
    echo    ✅ 正在运行
    netstat -an | find ":5000"
) else (
    echo    ❌ 未运行
)

echo.
echo 🔍 Python进程状态:
tasklist | find "python.exe"

echo.
echo 📁 日志文件:
if exist logs (
    dir logs\*.log /b 2>nul
    if errorlevel 1 (
        echo    ⚠️ 未找到日志文件
    )
) else (
    echo    ⚠️ 未找到日志目录
)

echo.
echo 🌐 访问地址:
echo    🔧 管理后台: http://localhost:5888
echo    🔌 API服务:  http://localhost:5002
echo    👥 成员访问: http://localhost:5000
echo ==================================================
pause
