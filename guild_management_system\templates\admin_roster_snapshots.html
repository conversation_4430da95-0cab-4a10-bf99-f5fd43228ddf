{% extends "base.html" %}

{% block title %}排表快照管理{% endblock %}

{% block content %}
<style>
    .snapshots-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .snapshots-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .snapshots-header h1 {
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .actions-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        font-size: 14px;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
    }
    
    .btn-success {
        background: #27ae60;
        color: white;
    }
    
    .btn-success:hover {
        background: #229954;
    }
    
    .snapshot-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #3498db;
        transition: all 0.3s ease;
    }
    
    .snapshot-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .snapshot-card.auto {
        border-left-color: #27ae60;
    }
    
    .snapshot-card.manual {
        border-left-color: #f39c12;
    }
    
    .snapshot-card.battle_start {
        border-left-color: #e74c3c;
    }
    
    .snapshot-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .snapshot-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .snapshot-type {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .type-weekly_auto {
        background: #d5f4e6;
        color: #27ae60;
    }
    
    .type-manual {
        background: #fef9e7;
        color: #f39c12;
    }
    
    .type-battle_start {
        background: #fadbd8;
        color: #e74c3c;
    }
    
    .snapshot-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .info-item {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
    }
    
    .info-label {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .info-value {
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
    }
    
    .team-stats {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .team-stat {
        background: #e9ecef;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
    }
    
    .snapshot-notes {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        font-style: italic;
        color: #6c757d;
        margin-top: 10px;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 20px;
        display: block;
    }
</style>

<div class="snapshots-container">
    <div class="snapshots-header">
        <h1>📸 排表快照管理</h1>
        <p style="color: #6c757d;">管理和查看历史排表快照</p>
    </div>
    
    <div class="actions-bar">
        <div>
            <span style="color: #6c757d;">快照总数：{{ snapshots|length }}</span>
        </div>
        <div>
            <a href="{{ url_for('create_manual_roster_snapshot') }}" class="btn btn-success">
                📸 手动创建快照
            </a>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                返回首页
            </a>
        </div>
    </div>
    
    {% if snapshots %}
        {% for snapshot in snapshots %}
        <div class="snapshot-card {{ snapshot.snapshot_type }}">
            <div class="snapshot-header">
                <h3 class="snapshot-title">
                    {% if snapshot.snapshot_type == 'weekly_auto' %}
                        🕐 每周自动快照
                    {% elif snapshot.snapshot_type == 'manual' %}
                        👤 手动创建快照
                    {% elif snapshot.snapshot_type == 'battle_start' %}
                        ⚔️ 战斗开始快照
                    {% endif %}
                </h3>
                <span class="snapshot-type type-{{ snapshot.snapshot_type }}">
                    {{ snapshot.snapshot_type }}
                </span>
            </div>
            
            <div class="snapshot-info">
                <div class="info-item">
                    <div class="info-label">快照时间</div>
                    <div class="info-value">
                        {{ snapshot.snapshot_time.strftime('%Y-%m-%d %H:%M:%S') if snapshot.snapshot_time else '未知' }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">周期开始</div>
                    <div class="info-value">
                        {{ snapshot.week_start_date.strftime('%Y-%m-%d') if snapshot.week_start_date else '未知' }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">成员总数</div>
                    <div class="info-value">{{ snapshot.member_count }} 人</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">创建者</div>
                    <div class="info-value">
                        {{ snapshot.creator_name or '系统自动' }}
                    </div>
                </div>
            </div>
            
            {% if snapshot.team_stats %}
            <div class="info-item">
                <div class="info-label">团队分布</div>
                <div class="team-stats">
                    {% for team, count in snapshot.team_stats.items() %}
                        <span class="team-stat">{{ team }}: {{ count }}人</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            {% if snapshot.notes %}
            <div class="snapshot-notes">
                📝 {{ snapshot.notes }}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
    {% else %}
        <div class="empty-state">
            <i class="fas fa-camera"></i>
            <h3>暂无快照</h3>
            <p>还没有创建任何排表快照，点击上方按钮创建第一个快照</p>
        </div>
    {% endif %}
</div>
{% endblock %}
