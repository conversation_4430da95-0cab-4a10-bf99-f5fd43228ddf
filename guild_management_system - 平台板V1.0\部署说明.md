# 纸落云烟公会管理系统 - 部署说明

## 🚀 端口配置

### 应用端口分配：
- **管理后台**：5888端口
- **API服务**：5002端口
- **前端界面**：通过API服务提供

## 🔧 腾讯云轻量服务器配置

### 1. 防火墙规则配置
在腾讯云控制台 → 轻量应用服务器 → 防火墙中添加：

```
应用类型：自定义
协议：TCP
端口：5888
来源：0.0.0.0/0
备注：公会管理后台

应用类型：自定义
协议：TCP
端口：5002
来源：0.0.0.0/0
备注：公会API服务
```

### 2. 服务器内部防火墙
```bash
# Ubuntu系统
sudo ufw allow 5888
sudo ufw allow 5002

# CentOS系统
sudo firewall-cmd --permanent --add-port=5888/tcp
sudo firewall-cmd --permanent --add-port=5002/tcp
sudo firewall-cmd --reload
```

## 📦 部署步骤

### 1. 上传文件
```bash
# 上传整个项目文件夹到服务器
scp -r guild_management_system/ root@服务器IP:/root/
```

### 2. 安装依赖
```bash
cd /root/guild_management_system/
pip3 install -r requirements.txt
```

### 3. 启动服务

#### 方法1：使用启动脚本
```bash
chmod +x start.sh
./start.sh
```

#### 方法2：手动启动
```bash
# 启动管理后台（5888端口）
nohup python3 app.py > backend.log 2>&1 &

# 启动API服务（5002端口）
nohup python3 api_app.py > api.log 2>&1 &
```

### 4. 验证部署
```bash
# 检查进程
ps aux | grep python3

# 检查端口监听
netstat -tlnp | grep :5888
netstat -tlnp | grep :5002
```

## 🌐 访问地址

### 本地测试：
- 管理后台：http://localhost:5888
- API服务：http://localhost:5002

### 服务器访问：
- 管理后台：http://服务器IP:5888
- API服务：http://服务器IP:5002
- 前端界面：访问 frontend/index.html

## 🔐 认证配置

### API服务认证方式：
1. **简单密码模式**（默认）
   - 管理员密码：admin123
   - 干部密码：officer456
   - 成员密码：member123

2. **邀请码模式**
   - 管理员生成邀请码
   - 成员使用邀请码登录

## 📝 环境变量

可以通过环境变量自定义配置：

```bash
# 管理后台
export PORT=5888          # 端口号
export HOST=0.0.0.0       # 监听地址
export FLASK_ENV=production  # 环境模式

# API服务
export API_PORT=5002      # API端口
```

## 🛠️ 进程管理

### 使用Screen管理：
```bash
# 启动管理后台
screen -S guild_backend
python3 app.py
# Ctrl+A+D 退出

# 启动API服务
screen -S guild_api  
python3 api_app.py
# Ctrl+A+D 退出

# 查看会话
screen -ls

# 恢复会话
screen -r guild_backend
screen -r guild_api
```

### 设置开机自启：
```bash
# 创建systemd服务
sudo nano /etc/systemd/system/guild-backend.service
sudo nano /etc/systemd/system/guild-api.service

# 启用服务
sudo systemctl enable guild-backend
sudo systemctl enable guild-api
sudo systemctl start guild-backend
sudo systemctl start guild-api
```

## 🔍 故障排查

### 常见问题：
1. **端口被占用**
   ```bash
   lsof -i :5888
   lsof -i :5002
   ```

2. **防火墙阻止**
   ```bash
   sudo ufw status
   telnet 服务器IP 5888
   ```

3. **应用启动失败**
   ```bash
   tail -f backend.log
   tail -f api.log
   ```

## 📞 技术支持

如有问题，请检查：
1. 防火墙规则是否正确配置
2. 端口是否被其他程序占用
3. Python依赖是否安装完整
4. 日志文件中的错误信息
