# 雷达图功能说明

## 功能概述

我们为成员详情页面添加了**多边形雷达图**功能，用于直观展示每个成员的六维能力分布。这个雷达图能够清晰地显示成员在不同方面的强弱项，帮助更好地了解成员特点。

## 雷达图特性

### 六维能力评估
1. **伤害输出** - 玩家伤害数据
2. **击杀能力** - 击杀数据
3. **资源贡献** - 资源收集数据
4. **生存能力** - 基于重伤次数的生存评估
5. **治疗支援** - 治疗量数据
6. **建筑破坏** - 建筑伤害数据

### 职业特色设计

#### 颜色编码
- **素问**：粉色系 (rgba(255, 182, 193, 0.3))
- **九灵**：紫色系 (rgba(138, 43, 226, 0.3))
- **潮光**：淡蓝色系 (rgba(173, 216, 230, 0.3))
- **血河**：红色系 (rgba(255, 99, 71, 0.3))
- **神相**：蓝色系 (rgba(70, 130, 180, 0.3))
- **玄机**：黄色系 (rgba(255, 215, 0, 0.3))
- **铁衣**：橙色系 (rgba(255, 140, 0, 0.3))
- **龙吟**：绿色系 (rgba(34, 139, 34, 0.3))
- **碎梦**：墨绿色系 (rgba(0, 100, 0, 0.3))

#### 职业特化评估标准

**治疗职业 - 素问**
- 伤害输出：较低标准 (5,000,000)
- 治疗支援：高标准 (150,000,000)
- 击杀能力：较低标准 (5)
- 生存能力：中等标准 (30)

**坦克职业 - 铁衣**
- 伤害输出：中等标准 (20,000,000)
- 生存能力：高标准 (10 - 重伤越少越好)
- 治疗支援：中等标准 (10,000,000)
- 击杀能力：较低标准 (10)

**辅助职业 - 潮光**
- 伤害输出：中等标准 (50,000,000)
- 建筑破坏：高标准 (30,000,000)
- 击杀能力：中等标准 (15)
- 治疗支援：较低标准 (5,000,000)

**输出职业 - 九灵、血河、玄机、龙吟、神相、碎梦**
- 伤害输出：高标准 (100,000,000)
- 击杀能力：高标准 (30)
- 建筑破坏：高标准 (50,000,000)
- 生存能力：中等标准 (40)

### 技术实现

#### SVG绘制
- 使用纯SVG技术绘制，无需外部图表库
- 响应式设计，适配不同屏幕尺寸
- 300x250像素的标准尺寸

#### 数据标准化
- 所有数据标准化到0-100范围
- 根据职业特点调整评估基准
- 自动限制最大值为100，避免图形溢出

#### 坐标计算
- 六边形布局，每个轴间隔60度
- 使用三角函数计算各点坐标
- 中心点为(150, 125)，最大半径100像素

#### 视觉元素
- **同心圆**：5层背景圆圈，间隔20像素
- **轴线**：6条从中心向外的参考线
- **多边形**：半透明填充的能力区域
- **数据点**：6个实心圆点标记具体数值
- **标签**：6个维度的文字说明

### 数据解读

#### 图形特征
- **面积大小**：整体能力强弱
- **形状特征**：能力分布特点
- **突出方向**：主要优势领域
- **凹陷区域**：需要改进的方面

#### 职业典型模式
- **治疗型**：治疗支援方向突出，其他相对均衡
- **输出型**：伤害输出和击杀能力突出
- **坦克型**：生存能力突出，伤害相对较低
- **辅助型**：多维度相对均衡，建筑破坏较强

### 使用建议

#### 个人发展
1. **识别优势**：查看雷达图中突出的维度
2. **发现短板**：关注数值较低的维度
3. **职业匹配**：对比职业典型模式
4. **改进方向**：针对性提升薄弱环节

#### 团队配置
1. **能力互补**：组合不同特长的成员
2. **职责分工**：根据雷达图分配任务
3. **战术调整**：基于成员特点制定策略
4. **培养计划**：制定个性化提升方案

### 数据来源

#### 战斗数据
- 基于最新一场战斗的实际数据
- 自动从战斗记录中提取
- 实时反映当前能力状态

#### 更新机制
- 每次上传新战斗数据后自动更新
- 始终显示最新战斗表现
- 历史数据保留在详细记录中

### 未来扩展

#### 可能的改进
1. **历史对比**：显示多场战斗的雷达图变化
2. **团队雷达**：整个小队的综合能力图
3. **目标设定**：设置理想能力目标线
4. **同职业对比**：与同职业平均水平对比
5. **动画效果**：数据变化的动态展示

#### 数据丰富
- 随着战斗数据增加，评估标准会更精确
- 职业特化标准可以根据实际数据调整
- 可以添加更多维度的能力评估

## 总结

雷达图功能为成员分析提供了直观的可视化工具，通过多边形图形清晰展示每个成员的能力分布。结合职业特色的颜色编码和评估标准，能够帮助更好地理解成员特点，制定针对性的发展策略。

这个功能不仅美观实用，还体现了数据驱动的管理理念，为帮会成员的成长和团队配置提供了科学依据。
