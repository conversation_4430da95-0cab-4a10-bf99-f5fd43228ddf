{% extends "base.html" %}

{% block title %}系统设置 - 超级管理员{% endblock %}

{% block content %}
<style>
    .settings-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .settings-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .settings-header h1 {
        color: #2c3e50;
        font-size: 2.5em;
        margin-bottom: 10px;
    }

    .settings-header p {
        color: #7f8c8d;
        font-size: 1.2em;
    }

    .stats-section {
        margin-bottom: 40px;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.8em;
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 3px solid #667eea;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5em;
        font-weight: bold;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1em;
        opacity: 0.9;
    }

    .actions-section {
        margin-bottom: 40px;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .action-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 25px;
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .action-card h4 {
        color: #2c3e50;
        margin: 0 0 15px 0;
        font-size: 1.3em;
    }

    .action-card p {
        color: #666;
        margin: 0 0 20px 0;
        line-height: 1.5;
    }

    .action-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        transition: background 0.3s ease;
        width: 100%;
    }

    .action-btn:hover {
        background: #5a6fd8;
    }

    .action-btn.danger {
        background: #dc3545;
    }

    .action-btn.danger:hover {
        background: #c82333;
    }

    .action-btn.warning {
        background: #ffc107;
        color: #212529;
    }

    .action-btn.warning:hover {
        background: #e0a800;
    }

    .info-section {
        background: #e7f3ff;
        border-radius: 12px;
        padding: 25px;
        border-left: 5px solid #007bff;
        margin-bottom: 30px;
    }

    .info-section h4 {
        color: #004085;
        margin: 0 0 15px 0;
    }

    .info-section p {
        color: #004085;
        margin: 5px 0;
        line-height: 1.5;
    }

    .cache-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .cache-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
    }

    .cache-item h5 {
        margin: 0 0 10px 0;
        color: #2c3e50;
    }

    .cache-item p {
        margin: 0;
        font-size: 1.5em;
        font-weight: bold;
        color: #667eea;
    }

    .flash-messages {
        margin-bottom: 20px;
    }

    .flash-message {
        padding: 12px 20px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .flash-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .flash-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .flash-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
</style>

<!-- Flash消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="flash-messages">
            {% for category, message in messages %}
                <div class="flash-message flash-{{ category }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="settings-container">
    <div class="settings-header">
        <h1>⚙️ 系统设置</h1>
        <p>逆水寒帮会辅助管理系统 - 超级管理员控制面板</p>
    </div>

    <!-- 系统统计 -->
    <div class="stats-section">
        <h2 class="section-title">
            📊 系统统计
        </h2>
        <div class="stats-grid">
            <div class="stat-card">
                <h3>{{ system_stats.total_users }}</h3>
                <p>👥 总用户数</p>
            </div>
            <div class="stat-card">
                <h3>{{ system_stats.active_users }}</h3>
                <p>✅ 活跃用户</p>
            </div>
            <div class="stat-card">
                <h3>{{ system_stats.total_guilds }}</h3>
                <p>🏰 总帮会数</p>
            </div>
            <div class="stat-card">
                <h3>{{ system_stats.pending_applications }}</h3>
                <p>⏳ 待审申请</p>
            </div>
            <div class="stat-card">
                <h3>{{ system_stats.banned_users }}</h3>
                <p>🚫 封禁用户</p>
            </div>
            <div class="stat-card">
                <h3>{{ system_stats.total_applications }}</h3>
                <p>📋 总申请数</p>
            </div>
        </div>
    </div>

    <!-- 缓存信息 -->
    <div class="stats-section">
        <h2 class="section-title">
            🚀 缓存状态
        </h2>
        <div class="info-section">
            <h4>💾 缓存系统信息</h4>
            <p>缓存系统正在运行，提供高性能数据访问</p>
            <p>缓存过期时间: 5分钟</p>
            <p>缓存类型: 内存缓存 (线程安全)</p>
            
            <div class="cache-info">
                <div class="cache-item">
                    <h5>缓存项目</h5>
                    <p>{{ cache_stats.cached_items }}</p>
                </div>
                <div class="cache-item">
                    <h5>缓存命中</h5>
                    <p>{{ cache_stats.cache_hits }}</p>
                </div>
                <div class="cache-item">
                    <h5>缓存大小</h5>
                    <p>{{ cache_stats.cache_size }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统操作 -->
    <div class="actions-section">
        <h2 class="section-title">
            🛠️ 系统操作
        </h2>
        <div class="actions-grid">
            <div class="action-card">
                <h4>🗑️ 清除缓存</h4>
                <p>清除所有缓存数据，强制从数据库重新加载。适用于数据不一致时。</p>
                <button class="action-btn warning" onclick="clearCache()">
                    清除缓存
                </button>
            </div>
            
            <div class="action-card">
                <h4>📊 导出数据</h4>
                <p>导出系统数据用于备份或分析。包括用户、帮会和申请数据。</p>
                <button class="action-btn" onclick="exportData()">
                    导出数据
                </button>
            </div>
            
            <div class="action-card">
                <h4>🔄 重启缓存</h4>
                <p>重新初始化缓存系统，预热常用数据。</p>
                <button class="action-btn" onclick="restartCache()">
                    重启缓存
                </button>
            </div>
            
            <div class="action-card">
                <h4>📈 系统报告</h4>
                <p>生成详细的系统使用报告，包括用户活跃度和帮会统计。</p>
                <button class="action-btn" onclick="generateReport()">
                    生成报告
                </button>
            </div>
            
            <div class="action-card">
                <h4>🔧 数据库维护</h4>
                <p>执行数据库优化和清理操作，提升系统性能。</p>
                <button class="action-btn" onclick="maintainDatabase()">
                    数据库维护
                </button>
            </div>
            
            <div class="action-card">
                <h4>⚠️ 系统重置</h4>
                <p>危险操作：重置系统设置到默认状态。请谨慎使用。</p>
                <button class="action-btn danger" onclick="resetSystem()">
                    系统重置
                </button>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="stats-section">
        <h2 class="section-title">
            ℹ️ 系统信息
        </h2>
        <div class="info-section">
            <h4>🖥️ 运行环境</h4>
            <p><strong>系统版本:</strong> 逆水寒帮会辅助管理系统 v2.0</p>
            <p><strong>Python版本:</strong> {{ python_version() if python_version else 'Unknown' }}</p>
            <p><strong>数据库:</strong> MySQL 5.7 (腾讯云)</p>
            <p><strong>缓存系统:</strong> 内存缓存 + 连接池</p>
            <p><strong>部署模式:</strong> 开发模式 (建议生产环境使用 Gunicorn)</p>
        </div>
    </div>
</div>

<script>
    function clearCache() {
        if (confirm('确定要清除所有缓存吗？这将导致下次访问时重新加载数据。')) {
            // 这里可以添加清除缓存的API调用
            alert('缓存清除功能开发中...');
        }
    }

    function exportData() {
        alert('数据导出功能开发中...');
    }

    function restartCache() {
        if (confirm('确定要重启缓存系统吗？')) {
            alert('缓存重启功能开发中...');
        }
    }

    function generateReport() {
        alert('系统报告生成功能开发中...');
    }

    function maintainDatabase() {
        if (confirm('确定要执行数据库维护吗？这可能需要一些时间。')) {
            alert('数据库维护功能开发中...');
        }
    }

    function resetSystem() {
        if (confirm('⚠️ 警告：这将重置所有系统设置！确定要继续吗？')) {
            if (confirm('请再次确认系统重置操作！此操作不可恢复！')) {
                alert('系统重置功能开发中...');
            }
        }
    }
</script>
{% endblock %}
