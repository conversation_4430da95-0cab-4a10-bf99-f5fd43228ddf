#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的评分系统
"""

def test_chaoguan_springs():
    """测试潮光清泉数据修复"""
    print("=" * 60)
    print("🧪 测试潮光清泉数据修复")
    print("=" * 60)
    
    # 模拟潮光战斗数据
    chaoguan_data = [
        {'profession': '潮光', 'qingquan': 5},
        {'profession': '潮光', 'qingquan': 8},
        {'profession': '潮光', 'qingquan': 3},
        {'profession': '潮光', 'qingquan': 7}
    ]
    
    # 计算潮光专项清泉平均值
    if chaoguan_data:
        chaoguan_springs_avg = sum(b.get('qingquan', 0) for b in chaoguan_data) / len(chaoguan_data)
        print(f"✅ 潮光清泉专项平均值: {chaoguan_springs_avg:.1f} (样本数:{len(chaoguan_data)})")
    else:
        chaoguan_springs_avg = 0
        print("❌ 没有潮光数据")
    
    # 测试评分计算
    test_member = {'profession': '潮光', 'qingquan': 6}
    if chaoguan_springs_avg > 1:
        springs_ratio = test_member['qingquan'] / chaoguan_springs_avg
        springs_score = (springs_ratio - 1) * 25
        print(f"✅ 测试潮光评分: 清泉值={test_member['qingquan']}, 比率={springs_ratio:.2f}, 得分={springs_score:.1f}")
    else:
        print("❌ 平均值无效")

def test_suwen_scoring():
    """测试素问评分简化"""
    print("\n" + "=" * 60)
    print("🧪 测试素问评分简化")
    print("=" * 60)
    
    # 模拟素问数据
    suwen_data = {
        'profession': '素问',
        'resurrections': 8,  # 羽化数据
        'heavy_injuries': 5,  # 重伤数据
        'healing': 50000000,  # 治疗量（不应该影响评分）
        'damage_taken': 20000000  # 受伤（不应该影响评分）
    }
    
    print(f"✅ 素问数据: 羽化={suwen_data['resurrections']}, 重伤={suwen_data['heavy_injuries']}")
    print(f"🚫 不考虑: 治疗量={suwen_data['healing']}, 受伤={suwen_data['damage_taken']}")
    
    # 模拟评分计算
    resurrections_avg = 6.0
    heavy_avg = 4.0
    
    # 羽化评分
    resurrections_ratio = suwen_data['resurrections'] / resurrections_avg
    resurrections_score = (resurrections_ratio - 1) * 30
    
    # 重伤扣分
    heavy_ratio = suwen_data['heavy_injuries'] / heavy_avg
    heavy_penalty = (heavy_ratio - 1) * 15 if heavy_ratio > 1 else 0
    
    final_score = 50 + resurrections_score - heavy_penalty
    
    print(f"✅ 羽化评分: +{resurrections_score:.1f}分")
    print(f"✅ 重伤扣分: -{heavy_penalty:.1f}分")
    print(f"✅ 最终评分: {final_score:.1f}分")

def test_damage_validation():
    """测试伤害数据验证"""
    print("\n" + "=" * 60)
    print("🧪 测试伤害数据验证")
    print("=" * 60)
    
    # 测试各种平均值
    test_cases = [
        {'name': '建筑伤害', 'avg': 1, 'threshold': 1000, 'should_skip': True},
        {'name': '建筑伤害', 'avg': 5000000, 'threshold': 1000, 'should_skip': False},
        {'name': '玩家伤害', 'avg': 1, 'threshold': 1000, 'should_skip': True},
        {'name': '玩家伤害', 'avg': 30000000, 'threshold': 1000, 'should_skip': False},
    ]
    
    for case in test_cases:
        if case['avg'] > case['threshold']:
            print(f"✅ {case['name']}: 平均值={case['avg']}, 通过验证")
        else:
            print(f"🚫 {case['name']}: 平均值={case['avg']}, 跳过评分（避免异常分数）")

if __name__ == '__main__':
    test_chaoguan_springs()
    test_suwen_scoring()
    test_damage_validation()
    print("\n🎉 测试完成！")
