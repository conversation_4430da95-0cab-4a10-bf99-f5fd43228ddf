#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('.')

# 测试CSV解析功能
def test_csv_parsing():
    from app import parse_csv_battle_data
    
    # 创建测试CSV内容 - 模拟真实格式
    test_csv_content = """"不周山","30"
"玩家名字","职业","击败","助攻","资源","对玩家伤害","对建筑伤害","治疗值","承受伤害","重伤","化羽/清泉","焚骨"
"测试玩家1","素问","0","15","0","500000","100000","10000000","2000000","5","3","0"
"测试玩家2","铁衣","2","8","0","800000","200000","0","8000000","3","0","0"
"测试玩家3","潮光","1","12","0","1200000","800000","0","1500000","2","5","1"


"❤清韵音舞❤","25"
"玩家名字","职业","击败","助攻","资源","对玩家伤害","对建筑伤害","治疗值","承受伤害","重伤","化羽/清泉","焚骨"
"对手玩家1","碎梦","3","10","0","1500000","300000","0","2000000","4","0","0"
"对手玩家2","神相","1","6","0","900000","1200000","0","1800000","2","0","2"
"""
    
    # 保存测试文件
    test_file = 'test_buzhoushan.csv'
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_csv_content)
    
    print(f"创建测试文件: {test_file}")
    print("文件内容:")
    print(test_csv_content)
    print("\n" + "="*50)
    
    # 测试解析
    try:
        battle_data = parse_csv_battle_data(test_file)
        print(f"解析结果: {len(battle_data)} 条战斗数据")
        
        if battle_data:
            print("\n解析到的数据:")
            for i, data in enumerate(battle_data):
                print(f"  {i+1}. {data.get('name', '未知')} ({data.get('guild', '未知帮会')}) - {data.get('profession', '未知职业')}")
                print(f"     击杀: {data.get('kills', 0)}, 伤害: {data.get('player_damage', 0)}")
        else:
            print("❌ 没有解析到任何数据!")
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n清理测试文件: {test_file}")

if __name__ == "__main__":
    test_csv_parsing()
