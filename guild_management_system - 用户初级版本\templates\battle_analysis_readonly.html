{% extends "base.html" %}

{% block title %}战斗分析 - 纸落云烟{% endblock %}

{% block content %}
<style>
    .readonly-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .readonly-notice {
        background: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
        text-align: center;
        border: 1px solid #c3e6cb;
    }
    
    .battle-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .battle-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 2px solid transparent;
    }
    
    .battle-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        border-color: #28a745;
    }
    
    .battle-header {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 20px;
        text-align: center;
    }
    
    .battle-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .battle-date {
        font-size: 14px;
        opacity: 0.9;
    }
    
    .battle-content {
        padding: 20px;
    }
    
    .battle-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .stat-item {
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .stat-item:hover {
        border-color: #28a745;
    }
    
    .stat-number {
        font-size: 20px;
        font-weight: bold;
        color: #28a745;
    }
    
    .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
    
    .battle-actions {
        text-align: center;
        margin-top: 15px;
    }
    
    .view-detail-btn {
        padding: 10px 20px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    
    .view-detail-btn:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
        transform: translateY(-2px);
    }
    
    .battle-summary {
        background: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .summary-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
    
    .empty-state {
        text-align: center;
        padding: 50px;
        color: #666;
    }
    
    .empty-state i {
        font-size: 64px;
        margin-bottom: 20px;
        color: #dee2e6;
    }
    
    /* 战斗详情模态框 */
    .battle-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.7);
    }
    
    .battle-modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 20px;
        border-radius: 15px;
        width: 90%;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
    }
    
    .modal-close {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 24px;
        cursor: pointer;
        color: #666;
    }
    
    .modal-close:hover {
        color: #dc3545;
    }
    
    .performance-list {
        max-height: 400px;
        overflow-y: auto;
        margin-top: 20px;
    }
    
    .performance-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #eee;
        transition: background 0.3s ease;
    }
    
    .performance-item:hover {
        background: #f8f9fa;
    }
    
    .member-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .profession-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    
    .score-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        color: white;
    }
    
    .score-excellent { background: #28a745; }
    .score-good { background: #ffc107; color: #333; }
    .score-average { background: #6c757d; }
    .score-poor { background: #dc3545; }
</style>

<div class="readonly-header">
    <h2><i class="fas fa-chart-line"></i> 战斗分析</h2>
    <p>查看历史战斗记录和数据分析 | 只读模式</p>
</div>

<div class="readonly-notice">
    <i class="fas fa-info-circle"></i> 当前为只读模式，仅可查看战斗数据，无法上传新的战斗记录
</div>

<!-- 战斗统计摘要 -->
{% if battle_records %}
<div class="battle-summary">
    <h4 style="margin-bottom: 15px;">战斗统计总览</h4>
    <div class="summary-stats">
        <div class="stat-item">
            <div class="stat-number">{{ battle_records | length }}</div>
            <div class="stat-label">总战斗数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">
                {% set total_participants = 0 %}
                {% for record in battle_records %}
                    {% set participants = record.get('member_performance', {}).values() | selectattr('battle_data') | list | length %}
                    {% set total_participants = total_participants + participants %}
                {% endfor %}
                {{ (total_participants / battle_records | length) | round | int }}
            </div>
            <div class="stat-label">平均参战人数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">
                {% set total_score = 0 %}
                {% set score_count = 0 %}
                {% for record in battle_records %}
                    {% for perf in record.get('member_performance', {}).values() %}
                        {% if perf.get('final_score') %}
                            {% set total_score = total_score + perf.final_score %}
                            {% set score_count = score_count + 1 %}
                        {% endif %}
                    {% endfor %}
                {% endfor %}
                {{ (total_score / score_count) | round(1) if score_count > 0 else 0 }}
            </div>
            <div class="stat-label">平均评分</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ battle_records[-1].get('enemy_guild', '未知') if battle_records else '无' }}</div>
            <div class="stat-label">最近对手</div>
        </div>
    </div>
</div>
{% endif %}

<!-- 战斗记录列表 -->
{% if battle_records %}
<div class="battle-cards">
    {% for record in battle_records %}
    <div class="battle-card">
        <div class="battle-header">
            <div class="battle-title">
                {{ record.get('our_guild', '纸落云烟') }} VS {{ record.get('enemy_guild', '未知对手') }}
            </div>
            <div class="battle-date">
                {{ record.get('upload_time', '未知时间') }}
            </div>
        </div>
        
        <div class="battle-content">
            <div class="battle-stats">
                <div class="stat-item">
                    <div class="stat-number">
                        {{ record.get('member_performance', {}).values() | selectattr('battle_data') | list | length }}
                    </div>
                    <div class="stat-label">参战人数</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number">
                        {% set scores = record.get('member_performance', {}).values() | selectattr('final_score') | map(attribute='final_score') | list %}
                        {{ (scores | sum / scores | length) | round(1) if scores else 0 }}
                    </div>
                    <div class="stat-label">平均评分</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number">
                        {{ record.get('member_performance', {}).values() | rejectattr('is_roster_member') | list | length }}
                    </div>
                    <div class="stat-label">新增成员</div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-number">
                        {% set excellent_count = record.get('member_performance', {}).values() | selectattr('final_score') | selectattr('final_score', '>=', 80) | list | length %}
                        {{ excellent_count }}
                    </div>
                    <div class="stat-label">优秀表现</div>
                </div>
            </div>
            
            <div class="battle-actions">
                <button class="view-detail-btn" onclick="viewBattleDetail('{{ record.get('battle_id', '') }}')">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="empty-state">
    <i class="fas fa-chart-line"></i>
    <h3>暂无战斗记录</h3>
    <p>还没有上传任何战斗数据</p>
</div>
{% endif %}

<!-- 战斗详情模态框 -->
<div id="battleModal" class="battle-modal">
    <div class="battle-modal-content">
        <span class="modal-close" onclick="closeBattleModal()">&times;</span>
        <div id="battleDetailContent">
            <!-- 战斗详情内容将在这里动态加载 -->
        </div>
    </div>
</div>

<script>
// 查看战斗详情
async function viewBattleDetail(battleId) {
    if (!battleId) {
        alert('战斗ID无效');
        return;
    }
    
    try {
        const response = await fetch(`/api/battle/${battleId}`);
        const battle = await response.json();
        
        if (battle.error) {
            alert('加载战斗详情失败: ' + battle.error);
            return;
        }
        
        displayBattleDetail(battle);
        document.getElementById('battleModal').style.display = 'block';
        
    } catch (error) {
        alert('加载战斗详情失败: ' + error.message);
    }
}

// 显示战斗详情
function displayBattleDetail(battle) {
    const content = document.getElementById('battleDetailContent');
    
    const memberPerformance = battle.member_performance || {};
    const participants = Object.entries(memberPerformance).filter(([name, perf]) => perf.battle_data);
    
    // 按评分排序
    participants.sort((a, b) => (b[1].final_score || 0) - (a[1].final_score || 0));
    
    content.innerHTML = `
        <h3 style="text-align: center; margin-bottom: 20px;">
            ${battle.our_guild || '纸落云烟'} VS ${battle.enemy_guild || '未知对手'}
        </h3>
        
        <div style="text-align: center; margin-bottom: 20px; color: #666;">
            <i class="fas fa-clock"></i> ${battle.upload_time || '未知时间'}
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 20px;">
            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                <div style="font-size: 20px; font-weight: bold; color: #28a745;">${participants.length}</div>
                <div style="font-size: 12px; color: #666;">参战人数</div>
            </div>
            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                <div style="font-size: 20px; font-weight: bold; color: #28a745;">
                    ${participants.length > 0 ? (participants.reduce((sum, [name, perf]) => sum + (perf.final_score || 0), 0) / participants.length).toFixed(1) : 0}
                </div>
                <div style="font-size: 12px; color: #666;">平均评分</div>
            </div>
            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                <div style="font-size: 20px; font-weight: bold; color: #28a745;">
                    ${participants.filter(([name, perf]) => !perf.is_roster_member).length}
                </div>
                <div style="font-size: 12px; color: #666;">新增成员</div>
            </div>
        </div>
        
        <h4>成员表现排行</h4>
        <div class="performance-list">
            ${participants.map(([name, perf]) => {
                const score = perf.final_score || 0;
                let scoreClass = 'score-poor';
                if (score >= 80) scoreClass = 'score-excellent';
                else if (score >= 60) scoreClass = 'score-good';
                else if (score >= 40) scoreClass = 'score-average';
                
                return `
                    <div class="performance-item">
                        <div class="member-info">
                            <div class="profession-dot" style="background-color: ${getProfessionColor(perf.profession || '未知')};"></div>
                            <strong>${name}</strong>
                            <span style="font-size: 12px; color: #666;">${perf.profession || '未知'}</span>
                            ${!perf.is_roster_member ? '<span style="background: #ffc107; color: #333; padding: 2px 6px; border-radius: 10px; font-size: 10px;">新</span>' : ''}
                        </div>
                        <div class="score-badge ${scoreClass}">
                            ${score.toFixed(1)}分
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;
}

// 关闭战斗详情模态框
function closeBattleModal() {
    document.getElementById('battleModal').style.display = 'none';
}

// 职业颜色映射
function getProfessionColor(profession) {
    const colors = {
        '素问': '#ff69b4',
        '九灵': '#9370db', 
        '潮光': '#87ceeb',
        '血河': '#dc143c',
        '神相': '#4169e1',
        '玄机': '#ffd700',
        '铁衣': '#ff8c00',
        '龙吟': '#32cd32',
        '碎梦': '#2e8b57'
    };
    return colors[profession] || '#6c757d';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('battleModal');
    if (event.target === modal) {
        closeBattleModal();
    }
}

// 只读模式提示
console.log('🛡️ 战斗分析 - 只读模式');
</script>

{% endblock %}
