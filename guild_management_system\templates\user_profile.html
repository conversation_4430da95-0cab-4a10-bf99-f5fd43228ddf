{% extends "base.html" %}

{% block title %}个人设置{% endblock %}

{% block content %}
<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .profile-card {
        background: white;
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .profile-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .profile-header h1 {
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .profile-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;
    }
    
    .form-section h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 18px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
    }
    
    .form-group input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
    }

    .form-group select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        background: white;
        cursor: pointer;
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: #667eea;
        color: white;
    }
    
    .btn-primary:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
    }
    
    .alert {
        padding: 12px 16px;
        border-radius: 4px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .character-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .character-avatar {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }
    
    .warning-text {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }
</style>

<div class="profile-container">
    <div class="profile-card">
        <div class="profile-header">
            <h1>⚙️ 个人设置</h1>
            <p style="color: #6c757d;">管理您的账户信息和角色设置</p>
        </div>
        
        <!-- 显示消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <!-- 用户信息 -->
        <div class="profile-info">
            <div class="character-info">
                <div class="character-avatar">👤</div>
                <div>
                    <h3 style="margin: 0; font-size: 20px;">{{ user.name }}</h3>
                    {% if bound_character %}
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">
                            绑定角色：{{ bound_character }}
                            {% if character_info %}
                                <span style="margin-left: 10px; padding: 2px 6px; background: rgba(255,255,255,0.3); border-radius: 4px; font-size: 12px;">
                                    {{ character_info.profession }}
                                </span>
                            {% endif %}
                        </p>
                    {% else %}
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">未绑定角色</p>
                    {% endif %}
                    <p style="margin: 5px 0 0 0; opacity: 0.8; font-size: 14px;">
                        {% if user.role == 'super_admin' %}超级管理员
                        {% elif user.role == 'guild_leader' %}帮会大当家
                        {% else %}普通用户
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- 修改密码 -->
        <div class="form-section">
            <h3>🔒 修改密码</h3>
            <form method="POST">
                <input type="hidden" name="action" value="change_password">
                
                <div class="form-group">
                    <label for="current_password">当前密码</label>
                    <input type="password" id="current_password" name="current_password" required>
                </div>
                
                <div class="form-group">
                    <label for="new_password">新密码</label>
                    <input type="password" id="new_password" name="new_password" required minlength="6">
                    <div class="warning-text">密码长度至少6位</div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">确认新密码</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>
                
                <button type="submit" class="btn btn-primary">修改密码</button>
            </form>
        </div>
        
        <!-- 修改角色名 -->
        {% if bound_character %}
        <div class="form-section">
            <h3>🎮 修改角色名</h3>
            <form method="POST">
                <input type="hidden" name="action" value="change_character_name">
                
                <div class="form-group">
                    <label for="new_character_name">新角色名</label>
                    <input type="text" id="new_character_name" name="new_character_name" 
                           value="{{ bound_character }}" required>
                    <div class="warning-text">修改角色名会同时更新帮会成员列表中的名字</div>
                </div>
                
                <button type="submit" class="btn btn-primary" 
                        onclick="return confirm('确定要修改角色名吗？这会影响帮会成员列表中的显示。')">
                    修改角色名
                </button>
            </form>
        </div>

        <!-- 修改职业 -->
        <div class="form-section">
            <h3>⚔️ 修改职业</h3>
            <form method="POST">
                <input type="hidden" name="action" value="change_profession">

                <div class="form-group">
                    <label for="new_profession">选择新职业</label>
                    <select id="new_profession" name="new_profession" required>
                        <option value="">请选择职业</option>
                        <option value="素问" {% if character_info and character_info.profession == '素问' %}selected{% endif %}>素问</option>
                        <option value="潮光" {% if character_info and character_info.profession == '潮光' %}selected{% endif %}>潮光</option>
                        <option value="九灵" {% if character_info and character_info.profession == '九灵' %}selected{% endif %}>九灵</option>
                        <option value="铁衣" {% if character_info and character_info.profession == '铁衣' %}selected{% endif %}>铁衣</option>
                        <option value="玄机" {% if character_info and character_info.profession == '玄机' %}selected{% endif %}>玄机</option>
                        <option value="龙吟" {% if character_info and character_info.profession == '龙吟' %}selected{% endif %}>龙吟</option>
                        <option value="血河" {% if character_info and character_info.profession == '血河' %}selected{% endif %}>血河</option>
                        <option value="神相" {% if character_info and character_info.profession == '神相' %}selected{% endif %}>神相</option>
                        <option value="碎梦" {% if character_info and character_info.profession == '碎梦' %}selected{% endif %}>碎梦</option>
                        <option value="沧澜" {% if character_info and character_info.profession == '沧澜' %}selected{% endif %}>沧澜</option>
                    </select>
                    {% if character_info %}
                        <div class="warning-text">当前职业：{{ character_info.profession }}</div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-primary"
                        onclick="return confirm('确定要修改职业吗？这会影响战斗数据分析和个性化建议。')">
                    修改职业
                </button>
            </form>
        </div>
        {% else %}
        <div class="form-section">
            <h3>🎮 角色绑定</h3>
            <p style="color: #6c757d; margin-bottom: 15px;">您还没有绑定角色，请先绑定角色后才能修改角色信息。</p>
            <a href="{{ url_for('character_binding') }}" class="btn btn-primary">去绑定角色</a>
        </div>
        {% endif %}
        
        <!-- 返回按钮 -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="{{ url_for('index') }}" class="btn btn-secondary">返回首页</a>
        </div>
    </div>
</div>

<script>
    // 密码确认验证
    document.getElementById('confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = this.value;
        
        if (newPassword !== confirmPassword) {
            this.setCustomValidity('密码确认不匹配');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // 新密码输入时也要重新验证确认密码
    document.getElementById('new_password').addEventListener('input', function() {
        const confirmPassword = document.getElementById('confirm_password');
        if (confirmPassword.value) {
            confirmPassword.dispatchEvent(new Event('input'));
        }
    });
</script>
{% endblock %}
