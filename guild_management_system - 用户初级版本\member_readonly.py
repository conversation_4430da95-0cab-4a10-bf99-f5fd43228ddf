#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纸落云烟帮会管理系统 - 成员只读版本
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, make_response
from werkzeug.utils import secure_filename
import json
import os
from datetime import datetime

app = Flask(__name__)

# 配置只读模式
app.config["READONLY_MODE"] = True
app.jinja_env.globals["readonly_mode"] = True

# 数据文件路径
MEMBERS_FILE = 'data/guild_members.json'
BATTLE_RECORDS_FILE = 'data/battle_records.json'

def load_members():
    """加载成员数据"""
    try:
        with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_members(members):
    """保存成员数据"""
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    with open(MEMBERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)

def load_battle_records():
    """加载战斗记录"""
    try:
        with open(BATTLE_RECORDS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_battle_records(records):
    """保存战斗记录"""
    os.makedirs('data', exist_ok=True)
    with open(BATTLE_RECORDS_FILE, 'w', encoding='utf-8') as f:
        json.dump(records, f, ensure_ascii=False, indent=2)

def get_team_type_from_member(member_info):
    """从成员信息获取团队类型"""
    main_group = member_info.get('main_group', '')
    if '进攻' in main_group:
        return '进攻团'
    elif '防守' in main_group:
        return '防守团'
    else:
        return '其他团'

def infer_team_type_from_battle_data(battle_member, profession):
    """从战斗数据推断团队类型"""
    # 简单推断逻辑，可以根据需要扩展
    building_damage = battle_member.get('building_damage', 0)
    player_damage = battle_member.get('player_damage', 0)

    # 建筑伤害高的可能是进攻团
    if building_damage > player_damage * 2:
        return '进攻团'
    # 玩家伤害高的可能是防守团
    elif player_damage > building_damage:
        return '防守团'
    else:
        return '其他团'

def infer_enemy_guild_from_data(battle_data):
    """从战斗数据推断对手帮会名称"""
    try:
        # 获取所有非纸落云烟的帮会名称
        enemy_guilds = set()
        for member in battle_data:
            guild = member.get('guild', '')
            if guild and guild != '纸落云烟':
                enemy_guilds.add(guild)

        if len(enemy_guilds) == 1:
            return list(enemy_guilds)[0]
        elif len(enemy_guilds) > 1:
            # 如果有多个对手帮会，选择人数最多的
            guild_counts = {}
            for member in battle_data:
                guild = member.get('guild', '')
                if guild and guild != '纸落云烟':
                    guild_counts[guild] = guild_counts.get(guild, 0) + 1

            if guild_counts:
                return max(guild_counts.items(), key=lambda x: x[1])[0]

        return "未知对手"
    except Exception as e:
        print(f"推断对手帮会失败: {e}")
        return "未知对手"

# 从app.py复制get_organization_structure函数
def get_organization_structure(members):
    """获取三层级组织结构"""
    structure = {
        '进攻团': {},
        '防守团': {},
        '其他团': {}
    }

    for member in members:
        # 解析成员的组织信息
        main_group = member.get('main_group', '其他团')  # 进攻团/防守团/其他团
        sub_team = member.get('sub_team', '一团')        # 一团/二团/三团
        squad = member.get('squad', '1队')              # 1队/2队/3队/4队/5队

        # 初始化结构
        if main_group not in structure:
            structure[main_group] = {}
        if sub_team not in structure[main_group]:
            structure[main_group][sub_team] = {
                'count': 0,
                'squads': {},
                'members': [],
                'professions': {},
                'status': {'主力': 0, '替补': 0, '请假': 0}
            }
        if squad not in structure[main_group][sub_team]['squads']:
            structure[main_group][sub_team]['squads'][squad] = {
                'count': 0,
                'members': []
            }

        # 添加成员到结构中
        structure[main_group][sub_team]['count'] += 1
        structure[main_group][sub_team]['members'].append(member)
        structure[main_group][sub_team]['squads'][squad]['count'] += 1
        structure[main_group][sub_team]['squads'][squad]['members'].append(member)

        # 统计职业
        prof = member['profession']
        structure[main_group][sub_team]['professions'][prof] = \
            structure[main_group][sub_team]['professions'].get(prof, 0) + 1

        # 统计状态
        status = member.get('status', '主力')
        structure[main_group][sub_team]['status'][status] = \
            structure[main_group][sub_team]['status'].get(status, 0) + 1

    return structure

def get_top_performers(battle_records, limit=5):
    """获取表现最佳的成员"""
    try:
        member_scores = {}

        for record in battle_records:
            member_performance = record.get('member_performance', {})
            for member_name, performance in member_performance.items():
                score = performance.get('score', 0)
                if score > 0:
                    if member_name not in member_scores:
                        member_scores[member_name] = []
                    member_scores[member_name].append(score)

        # 计算平均分并排序
        top_performers = []
        for member_name, scores in member_scores.items():
            avg_score = sum(scores) / len(scores)
            top_performers.append({
                'name': member_name,
                'avg_score': avg_score,
                'battle_count': len(scores)
            })

        # 按平均分排序，取前N名
        top_performers.sort(key=lambda x: x['avg_score'], reverse=True)
        return top_performers[:limit]

    except Exception as e:
        print(f"获取顶级表现者失败: {e}")
        return []

def generate_recent_activities(battle_records, members):
    """生成最近活动列表"""
    try:
        activities = []

        # 添加最近的战斗记录
        for record in battle_records[-3:]:  # 最近3场战斗
            upload_time = record.get('upload_time', '')
            if upload_time:
                # 格式化时间
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(upload_time.replace('T', ' ').split('.')[0])
                    time_str = dt.strftime('%m-%d %H:%M')
                except:
                    time_str = upload_time[:16].replace('T', ' ')

                activities.append({
                    'type': 'battle',
                    'title': f'战斗: {record["our_guild"]} VS {record["enemy_guild"]}',
                    'time': time_str
                })

        # 按时间倒序排列
        activities.reverse()

        return activities

    except Exception as e:
        print(f"生成最近活动失败: {e}")
        return []

# 路由定义 - 完全复制app.py的路由

@app.route('/')
def index():
    """主页 - 重定向到成员详情页面"""
    return redirect(url_for('members_list'))

@app.route('/members')
def members_list():
    """成员列表页面"""
    members = load_members()

    # 获取三层级组织结构
    organization = get_organization_structure(members)

    # 为了兼容，也保留旧的team分组（用于统计）
    teams = {
        '1团': [],
        '2团': [],
        '3团': [],
        '4团': [],
        '未分配': []
    }

    for member in members:
        team = member.get('team', '未分配')
        if team not in teams:
            teams[team] = []
        teams[team].append(member)

    # 按名字排序
    for team in teams:
        teams[team].sort(key=lambda x: x['name'])

    # 创建响应并添加防缓存头
    response = make_response(render_template('members.html',
                                           members=members,
                                           teams=teams,
                                           organization=organization))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@app.route('/organization_chart')
def organization_chart():
    """组织架构图页面"""
    members = load_members()

    # 获取三层级组织结构
    organization = get_organization_structure(members)

    return render_template('organization_chart.html',
                         members=members,
                         organization=organization)

@app.route('/battle_analysis')
def battle_analysis():
    """战斗数据分析页面 - 显示所有战斗记录卡片"""
    # 加载所有战斗记录
    battle_records = load_battle_records()

    # 按时间倒序排列（最新的在前）
    battle_records.sort(key=lambda x: x['upload_time'], reverse=True)

    return render_template('battle_analysis.html',
                         battle_records=battle_records)

@app.route('/api/battle/<battle_id>')
def get_battle_detail(battle_id):
    """获取战斗详情API"""
    battle_records = load_battle_records()

    battle = None
    for record in battle_records:
        if record.get('battle_id') == battle_id:
            battle = record
            break

    if not battle:
        return jsonify({'error': '战斗记录不存在'}), 404

    return jsonify(battle)

@app.route('/member_detail/<member_name>')
def member_detail(member_name):
    """成员详情页面"""
    members = load_members()
    member = next((m for m in members if m['name'] == member_name), None)

    if not member:
        return redirect(url_for('members_list'))

    # 获取该成员的战斗历史
    battle_history = get_member_battle_history(member_name)

    return render_template('member_detail.html',
                         member=member,
                         battle_history=battle_history)

def get_member_battle_history(member_name):
    """获取成员的战斗历史记录"""
    try:
        battle_records = load_battle_records()
        member_battles = []

        for record in battle_records:
            member_performance = record.get('member_performance', {})
            if member_name in member_performance:
                performance = member_performance[member_name]
                battle_info = {
                    'battle_id': record.get('battle_id', ''),
                    'our_guild': record.get('our_guild', ''),
                    'enemy_guild': record.get('enemy_guild', ''),
                    'upload_time': record.get('upload_time', ''),
                    'score': performance.get('score', 0),
                    'battle_data': performance.get('battle_data', {}),
                    'bonus_items': performance.get('bonus_items', []),
                    'penalty_items': performance.get('penalty_items', [])
                }
                member_battles.append(battle_info)

        # 按时间排序
        member_battles.sort(key=lambda x: x['upload_time'])
        return member_battles

    except Exception as e:
        print(f"获取成员战斗历史失败: {e}")
        return []

@app.route('/api/members')
def api_members():
    """API: 获取所有成员"""
    members = load_members()
    return jsonify(members)

# 禁用编辑功能的路由（返回403错误）
@app.route('/api/members', methods=['POST'])
def add_member():
    """禁用：添加成员"""
    return jsonify({'error': '只读模式，无法添加成员'}), 403

@app.route('/api/members/<member_name>', methods=['PUT'])
def update_member(member_name):
    """禁用：更新成员"""
    return jsonify({'error': '只读模式，无法修改成员'}), 403

@app.route('/api/members/<member_name>', methods=['DELETE'])
def delete_member(member_name):
    """禁用：删除成员"""
    return jsonify({'error': '只读模式，无法删除成员'}), 403

@app.route('/upload_battle_data', methods=['POST'])
def upload_battle_data():
    """禁用：上传战斗数据"""
    return jsonify({'error': '只读模式，无法上传数据'}), 403

@app.route('/api/battle/<battle_id>', methods=['DELETE'])
def delete_battle_record(battle_id):
    """禁用：删除战斗记录"""
    return jsonify({'error': '只读模式，无法删除记录'}), 403

@app.route('/drag_board')
def drag_board():
    """禁用：拖拽排表"""
    return jsonify({'error': '只读模式，无法访问拖拽排表'}), 403

if __name__ == '__main__':
    # 确保必要目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('data', exist_ok=True)

    print("=" * 60)
    print("👥 纸落云烟帮会管理系统 - 成员只读版本启动中...")
    print("=" * 60)
    print("📊 功能包括:")
    print("   - 成员详情 (只读查看)")
    print("   - 组织架构 (只读查看)")
    print("   - 战斗分析 (只读查看)")
    print("   - 与管理后台数据实时同步")
    print()
    print("🌐 访问地址: http://localhost:5000")
    print("=" * 60)

    # 生产环境配置
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')

    app.run(debug=debug_mode, host=host, port=port)
