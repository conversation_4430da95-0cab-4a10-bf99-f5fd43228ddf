#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移工具 - 将JSON数据迁移到数据库
"""

import json
import os
import pymysql
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'sh-cdb-r92w8slq.sql.tencentcdb.com',
    'port': 25366,
    'user': 'root',
    'password': 'Dorothy0423@',
    'database': 'guild_management',
    'charset': 'utf8mb4',
    'autocommit': True
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def migrate_members():
    """迁移成员数据"""
    print("正在迁移成员数据...")
    
    # 读取JSON文件
    members_file = 'data/guild_members.json'
    if not os.path.exists(members_file):
        print(f"成员数据文件不存在: {members_file}")
        return False
    
    try:
        with open(members_file, 'r', encoding='utf-8') as f:
            members = json.load(f)
        
        print(f"找到 {len(members)} 个成员记录")
        
        connection = get_db_connection()
        if not connection:
            return False
        
        cursor = connection.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM guild_members WHERE guild_id = 'zhiluoyunyan'")
        print("清空现有成员数据")
        
        # 插入成员数据
        success_count = 0
        for member in members:
            try:
                cursor.execute("""
                    INSERT INTO guild_members 
                    (guild_id, name, profession, main_group, sub_team, squad, position, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    'zhiluoyunyan',  # 默认帮会ID
                    member.get('name', ''),
                    member.get('profession', ''),
                    member.get('main_group', ''),
                    member.get('sub_team', ''),
                    member.get('squad', ''),
                    member.get('position', ''),
                    member.get('status', '主力')
                ))
                success_count += 1
            except Exception as e:
                print(f"插入成员 {member.get('name', '未知')} 失败: {e}")
        
        # 更新帮会成员数量
        cursor.execute("""
            UPDATE guilds SET members_count = %s WHERE id = 'zhiluoyunyan'
        """, (success_count,))
        
        connection.close()
        print(f"✅ 成功迁移 {success_count} 个成员")
        return True
        
    except Exception as e:
        print(f"迁移成员数据失败: {e}")
        return False

def migrate_battle_records():
    """迁移战斗记录"""
    print("正在迁移战斗记录...")
    
    # 读取JSON文件
    battle_file = 'data/battle_records.json'
    if not os.path.exists(battle_file):
        print(f"战斗记录文件不存在: {battle_file}")
        return False
    
    try:
        with open(battle_file, 'r', encoding='utf-8') as f:
            battle_records = json.load(f)
        
        print(f"找到 {len(battle_records)} 条战斗记录")
        
        connection = get_db_connection()
        if not connection:
            return False
        
        cursor = connection.cursor()
        
        # 清空现有数据
        cursor.execute("DELETE FROM battle_records WHERE guild_id = 'zhiluoyunyan'")
        print("清空现有战斗记录")
        
        # 插入战斗记录
        success_count = 0
        for record in battle_records:
            try:
                cursor.execute("""
                    INSERT INTO battle_records 
                    (guild_id, battle_id, our_guild, enemy_guild, battle_data, member_performance, upload_time, uploaded_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    'zhiluoyunyan',  # 默认帮会ID
                    record.get('battle_id', ''),
                    record.get('our_guild', '纸落云烟'),
                    record.get('enemy_guild', ''),
                    json.dumps(record.get('battle_data', []), ensure_ascii=False),
                    json.dumps(record.get('member_performance', {}), ensure_ascii=False),
                    record.get('upload_time', datetime.now().isoformat()),
                    'system'  # 系统迁移
                ))
                success_count += 1
            except Exception as e:
                print(f"插入战斗记录 {record.get('battle_id', '未知')} 失败: {e}")
        
        connection.close()
        print(f"✅ 成功迁移 {success_count} 条战斗记录")
        return True
        
    except Exception as e:
        print(f"迁移战斗记录失败: {e}")
        return False

def verify_migration():
    """验证迁移结果"""
    print("正在验证迁移结果...")
    
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # 检查成员数量
        cursor.execute("SELECT COUNT(*) FROM guild_members WHERE guild_id = 'zhiluoyunyan'")
        member_count = cursor.fetchone()[0]
        print(f"数据库中成员数量: {member_count}")
        
        # 检查战斗记录数量
        cursor.execute("SELECT COUNT(*) FROM battle_records WHERE guild_id = 'zhiluoyunyan'")
        battle_count = cursor.fetchone()[0]
        print(f"数据库中战斗记录数量: {battle_count}")
        
        # 检查帮会信息
        cursor.execute("SELECT name, members_count FROM guilds WHERE id = 'zhiluoyunyan'")
        guild_info = cursor.fetchone()
        if guild_info:
            print(f"帮会信息: {guild_info[0]}, 成员数量: {guild_info[1]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"验证迁移结果失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始数据迁移...")
    print("=" * 50)
    
    # 检查数据库连接
    connection = get_db_connection()
    if not connection:
        print("❌ 无法连接到数据库")
        return
    connection.close()
    print("✅ 数据库连接正常")
    
    # 迁移数据
    print("\n开始迁移数据...")
    
    member_success = migrate_members()
    battle_success = migrate_battle_records()
    
    # 验证迁移结果
    print("\n验证迁移结果...")
    verify_migration()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 迁移总结:")
    print(f"成员数据迁移: {'✅ 成功' if member_success else '❌ 失败'}")
    print(f"战斗记录迁移: {'✅ 成功' if battle_success else '❌ 失败'}")
    
    if member_success and battle_success:
        print("\n🎉 数据迁移完成！")
        print("现在可以启动应用，数据将从数据库加载。")
    else:
        print("\n⚠️ 部分数据迁移失败，请检查错误信息。")

if __name__ == '__main__':
    main()
