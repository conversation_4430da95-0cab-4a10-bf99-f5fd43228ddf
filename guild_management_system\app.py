#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
纸落云烟帮会管理系统 - Web应用
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, make_response, session, flash
from werkzeug.utils import secure_filename
from functools import wraps, lru_cache
import json
import os
import csv
from datetime import datetime, timedelta
import pymysql
import hashlib
import threading
import time

app = Flask(__name__)

# 配置密钥用于session
app.secret_key = 'zhiluoyunyan_guild_management_2024'

# 数据库配置
DB_CONFIG = {
    'host': 'sh-cdb-r92w8slq.sql.tencentcdb.com',
    'port': 25366,
    'user': 'root',
    'password': 'Dorothy0423@',  # 如果密码不对会尝试其他密码
    'database': 'guild_management',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 缓存系统
class SimpleCache:
    def __init__(self):
        self._cache = {}
        self._timestamps = {}
        self._lock = threading.Lock()

    def get(self, key, default=None):
        with self._lock:
            if key in self._cache:
                # 检查是否过期（5分钟缓存）
                if time.time() - self._timestamps[key] < 300:
                    return self._cache[key]
                else:
                    # 过期删除
                    del self._cache[key]
                    del self._timestamps[key]
            return default

    def set(self, key, value):
        with self._lock:
            self._cache[key] = value
            self._timestamps[key] = time.time()

    def delete(self, key):
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                del self._timestamps[key]

    def clear(self):
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()

# 全局缓存实例
cache = SimpleCache()

# 数据库连接池（简单实现）
class ConnectionPool:
    def __init__(self, max_connections=5):
        self.max_connections = max_connections
        self.connections = []
        self.lock = threading.Lock()

    def get_connection(self):
        with self.lock:
            if self.connections:
                return self.connections.pop()
            else:
                try:
                    return pymysql.connect(**DB_CONFIG)
                except Exception as e:
                    print(f"创建数据库连接失败: {e}")
                    return None

    def return_connection(self, conn):
        with self.lock:
            if len(self.connections) < self.max_connections:
                try:
                    # 测试连接是否有效
                    conn.ping(reconnect=True)
                    self.connections.append(conn)
                except:
                    # 连接无效，关闭它
                    try:
                        conn.close()
                    except:
                        pass
            else:
                # 连接池已满，关闭连接
                try:
                    conn.close()
                except:
                    pass

# 全局连接池
db_pool = ConnectionPool()

# 用户配置 - 现在从数据库加载
SYSTEM_USERS = {
    'RaphaelL': {
        'password': 'Dorothy0423@',
        'role': 'super_admin',
        'name': '超级管理员',
        'guild_id': None
    },
    'zhiluo19': {
        'password': 'zhiluo19',
        'role': 'guild_leader',
        'name': '大当家',
        'guild_id': 'zhiluoyunyan'  # 默认帮会
    },
    'guest': {
        'password': '',
        'role': 'guest',
        'name': '游客',
        'guild_id': None
    }
}

# 职业颜色映射
def get_profession_color(profession):
    """获取职业对应的颜色"""
    color_map = {
        '素问': '#c62828',
        '铁衣': '#ef6c00',
        '潮光': '#0277bd',
        '九灵': '#7b1fa2',
        '龙吟': '#2e7d32',
        '血河': '#d32f2f',
        '碎梦': '#00695c',
        '玄机': '#f57f17',
        '神相': '#3f51b5',
        '沧澜': '#558b2f'
    }
    return color_map.get(profession, '#6c757d')

# 权限装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        user = get_current_user()
        if not user or user['role'] not in ['super_admin', 'guild_leader']:
            flash('需要管理员权限才能访问此功能', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

def guest_allowed(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 游客可以访问，但如果没有登录则自动以游客身份访问
        if 'user_id' not in session:
            session['user_id'] = 'guest'
        return f(*args, **kwargs)
    return decorated_function

def guest_search_only(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 游客只能使用搜索功能
        if 'user_id' not in session:
            session['user_id'] = 'guest'

        user = get_current_user()
        if user and user['role'] == 'guest':
            # 游客只能访问搜索相关功能
            allowed_endpoints = ['index', 'api_search_members', 'member_detail', 'login', 'register']
            if request.endpoint not in allowed_endpoints:
                flash('游客只能使用搜索功能，请注册登录以使用完整功能', 'info')
                return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

def get_current_user():
    """获取当前登录用户信息"""
    if 'user_id' in session:
        if session['user_id'] == 'guest':
            # 返回游客用户信息
            return {
                'username': 'guest',
                'name': '游客',
                'role': 'guest',
                'guild_id': None
            }
        users = load_users()
        return users.get(session['user_id'])
    return None

def is_super_admin():
    """检查当前用户是否为超级管理员"""
    user = get_current_user()
    return user and user['role'] == 'super_admin'

def is_guild_leader():
    """检查当前用户是否为帮会大当家"""
    user = get_current_user()
    return user and user['role'] == 'guild_leader'

def is_guest():
    """检查当前用户是否为游客"""
    user = get_current_user()
    return user and user['role'] == 'guest'

def get_user_bound_character_name():
    """获取当前用户绑定的角色名"""
    user = get_current_user()
    if not user or user['role'] == 'guest':
        return None

    try:
        # 获取用户ID
        connection = get_db_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT id FROM users WHERE username = %s", (session['user_id'],))
                result = cursor.fetchone()
                if result:
                    user_id = result[0]
                    bound_character = get_user_bound_character(user_id)
                    return bound_character
            finally:
                return_db_connection(connection)
    except Exception as e:
        print(f"获取用户绑定角色失败: {e}")

    return None

@app.context_processor
def inject_user_functions():
    """将用户相关函数注入到模板上下文中"""
    return dict(
        get_current_user=get_current_user,
        is_super_admin=is_super_admin,
        is_guild_leader=is_guild_leader,
        is_guest=is_guest,
        get_user_bound_character_name=get_user_bound_character_name
    )

def is_admin():
    """检查当前用户是否为管理员（超级管理员或帮会大当家）"""
    user = get_current_user()
    return user and user['role'] in ['super_admin', 'guild_leader']

def can_edit():
    """检查当前用户是否可以编辑"""
    user = get_current_user()
    return user and user['role'] in ['super_admin', 'guild_leader']

def get_user_guild():
    """获取当前用户的帮会ID"""
    user = get_current_user()
    if user:
        return user.get('guild_id')
    return None

# 数据库连接函数（使用连接池）
def get_db_connection():
    """获取数据库连接"""
    return db_pool.get_connection()

def return_db_connection(connection):
    """归还数据库连接到连接池"""
    if connection:
        db_pool.return_connection(connection)

def init_database():
    """初始化数据库和表结构"""
    connection = get_db_connection()
    if not connection:
        print("无法连接数据库，使用文件存储模式")
        return False

    try:
        cursor = connection.cursor()

        # 创建数据库（如果不存在）
        cursor.execute("CREATE DATABASE IF NOT EXISTS guild_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        cursor.execute("USE guild_management")

        # 创建用户表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                email VARCHAR(100),
                role ENUM('super_admin', 'guild_leader', 'user', 'guest') DEFAULT 'user',
                guild_id VARCHAR(50),
                real_name VARCHAR(100),
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
                INDEX idx_username (username),
                INDEX idx_guild_id (guild_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 创建帮会表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guilds (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                leader_username VARCHAR(50) NOT NULL,
                description TEXT,
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('active', 'inactive') DEFAULT 'active',
                members_count INT DEFAULT 0,
                max_members INT DEFAULT 100,
                INDEX idx_leader (leader_username),
                FOREIGN KEY (leader_username) REFERENCES users(username) ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 创建帮会申请表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_applications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                guild_id VARCHAR(50),
                application_type ENUM('create_guild', 'join_guild') NOT NULL,
                guild_name VARCHAR(100),
                description TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                processed_time DATETIME,
                processed_by VARCHAR(50),
                INDEX idx_username (username),
                INDEX idx_guild_id (guild_id),
                INDEX idx_status (status),
                FOREIGN KEY (username) REFERENCES users(username) ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 创建帮会成员表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_members (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                name VARCHAR(100) NOT NULL,
                profession VARCHAR(50) NOT NULL,
                main_group VARCHAR(50),
                sub_team VARCHAR(50),
                squad VARCHAR(50),
                position VARCHAR(100),
                status ENUM('主力', '替补', '请假', '帮外') DEFAULT '主力',
                skills JSON,
                created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_guild_id (guild_id),
                INDEX idx_name (name),
                INDEX idx_profession (profession),
                FOREIGN KEY (guild_id) REFERENCES guilds(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 创建战斗记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS battle_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                battle_id VARCHAR(100) UNIQUE NOT NULL,
                our_guild VARCHAR(100) NOT NULL,
                enemy_guild VARCHAR(100) NOT NULL,
                battle_data JSON,
                member_performance JSON,
                upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                uploaded_by VARCHAR(50),
                INDEX idx_guild_id (guild_id),
                INDEX idx_battle_id (battle_id),
                FOREIGN KEY (guild_id) REFERENCES guilds(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 创建团队名称配置表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_team_names (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                main_group VARCHAR(50) NOT NULL,
                sub_team VARCHAR(50) NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_guild_team (guild_id, main_group, sub_team),
                INDEX idx_guild (guild_id),
                FOREIGN KEY (guild_id) REFERENCES guilds(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 创建角色绑定表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS character_bindings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                character_name VARCHAR(50) NOT NULL,
                status ENUM('pending', 'approved', 'rejected', 'unbound', 'unbound_log') DEFAULT 'pending',
                verification_code VARCHAR(10),
                verification_expires DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                approved_by VARCHAR(50),
                approved_at TIMESTAMP NULL,
                reason TEXT,
                INDEX idx_user_character (user_id, character_name),
                INDEX idx_character_status (character_name, status),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 移除旧的唯一约束，允许角色换绑（安全处理）
        try:
            cursor.execute("SHOW INDEX FROM character_bindings WHERE Key_name = 'unique_character'")
            if cursor.fetchone():
                cursor.execute("ALTER TABLE character_bindings DROP INDEX unique_character")
                print("✅ 移除了旧的唯一约束，现在支持角色换绑")
        except Exception as e:
            print(f"移除唯一约束时出现错误（可能不存在）: {e}")

        # 更新状态枚举，添加新的状态值
        try:
            cursor.execute("""
                ALTER TABLE character_bindings
                MODIFY COLUMN status ENUM('pending', 'approved', 'rejected', 'unbound', 'unbound_log') DEFAULT 'pending'
            """)
            print("✅ 更新了状态枚举，支持解绑和换绑操作")
        except Exception as e:
            print(f"更新状态枚举时出现错误: {e}")

        # 添加原因字段（如果不存在）
        try:
            cursor.execute("SHOW COLUMNS FROM character_bindings LIKE 'reason'")
            if not cursor.fetchone():
                cursor.execute("ALTER TABLE character_bindings ADD COLUMN reason TEXT")
                print("✅ 添加了原因字段")
        except Exception as e:
            print(f"添加原因字段时出现错误: {e}")

        # 🆕 添加技能字段到成员表（如果不存在）
        try:
            cursor.execute("SHOW COLUMNS FROM guild_members LIKE 'skills'")
            if not cursor.fetchone():
                cursor.execute("ALTER TABLE guild_members ADD COLUMN skills JSON")
                print("✅ 添加了成员技能字段")
        except Exception as e:
            print(f"添加技能字段时出现错误: {e}")

        # 创建光荣墙表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS honor_wall (
                id INT AUTO_INCREMENT PRIMARY KEY,
                character_name VARCHAR(50) NOT NULL,
                achievement_type ENUM('battle_mvp', 'damage_king', 'support_hero', 'growth_star') NOT NULL,
                achievement_data JSON,
                display_order INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                INDEX idx_character (character_name),
                INDEX idx_type (achievement_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 🆕 创建排表快照表（新功能 - 安全添加）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roster_snapshots (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                snapshot_time TIMESTAMP NOT NULL,
                week_start_date DATE NOT NULL,
                snapshot_type ENUM('weekly_auto', 'manual', 'battle_start') DEFAULT 'weekly_auto',
                member_data JSON NOT NULL,
                created_by INT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (guild_id) REFERENCES guilds(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                INDEX idx_guild_week (guild_id, week_start_date),
                INDEX idx_snapshot_time (snapshot_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 🆕 创建帮外成员踢出记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS guild_kick_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                guild_id VARCHAR(50) NOT NULL,
                member_name VARCHAR(100) NOT NULL,
                moved_to_outside_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                kick_deadline TIMESTAMP NULL,
                status ENUM('pending', 'kicked', 'cancelled', 'manual_kick') DEFAULT 'pending',
                kicked_time TIMESTAMP NULL,
                kicked_by VARCHAR(50),
                reason TEXT,
                user_id INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_guild_member (guild_id, member_name),
                INDEX idx_deadline (kick_deadline),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        print("数据库表结构创建成功（包含新的排表快照功能）")
        return True

    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False
    finally:
        connection.close()

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, password_hash):
    """验证密码"""
    return hash_password(password) == password_hash

def warm_up_cache():
    """预热缓存 - 在应用启动时预加载常用数据"""
    print("正在预热缓存...")
    try:
        with app.app_context():
            # 预加载用户数据（不依赖请求上下文）
            users = load_users()
            print(f"预加载用户数据: {len(users)} 个用户")

            # 预加载帮会数据
            guilds = load_guilds()
            print(f"预加载帮会数据: {len(guilds)} 个帮会")

        print("✅ 缓存预热完成")
    except Exception as e:
        print(f"缓存预热失败: {e}")

# 注册模板函数
app.jinja_env.globals.update(
    get_profession_color=get_profession_color,
    get_current_user=get_current_user,
    is_super_admin=is_super_admin,
    is_guild_leader=is_guild_leader,
    is_admin=is_admin,
    is_guest=is_guest,
    can_edit=can_edit,
    get_user_guild=get_user_guild
)

# 数据文件路径
DATA_DIR = 'data'
USERS_FILE = 'data/users.json'
GUILDS_FILE = 'data/guilds.json'
GUILD_APPLICATIONS_FILE = 'data/guild_applications.json'

# 兼容性：原有文件路径（用于纸落云烟帮会）
MEMBERS_FILE = 'data/guild_members.json'
BATTLE_RECORDS_FILE = 'data/battle_records.json'

# 新的用户和帮会管理函数（数据库版本 + 缓存）
def load_users():
    """从数据库加载用户数据（带缓存）"""
    # 先检查缓存
    cached_users = cache.get('users')
    if cached_users:
        return cached_users

    connection = get_db_connection()
    if not connection:
        # 回退到系统用户
        return SYSTEM_USERS.copy()

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute("SELECT * FROM users WHERE status = 'active'")
        db_users = cursor.fetchall()

        # 转换为应用格式
        users = {}
        for user in db_users:
            users[user['username']] = {
                'password': user['password_hash'],  # 注意：这里存储的是哈希值
                'role': user['role'],
                'name': user['real_name'] or user['username'],
                'email': user.get('email', ''),
                'guild_id': user.get('guild_id'),
                'created_time': user['created_time'].isoformat() if user['created_time'] else None,
                'status': user['status']
            }

        # 添加游客用户
        users['guest'] = SYSTEM_USERS['guest']

        # 缓存结果
        cache.set('users', users)

        return users

    except Exception as e:
        print(f"从数据库加载用户失败: {e}")
        return SYSTEM_USERS.copy()
    finally:
        return_db_connection(connection)

def save_user(username, user_data):
    """保存单个用户到数据库"""
    connection = get_db_connection()
    if not connection:
        print("无法连接数据库，用户保存失败")
        return False

    try:
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO users (username, password_hash, email, role, real_name, guild_id, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            password_hash = VALUES(password_hash),
            email = VALUES(email),
            role = VALUES(role),
            real_name = VALUES(real_name),
            guild_id = VALUES(guild_id),
            status = VALUES(status),
            updated_time = CURRENT_TIMESTAMP
        """, (
            username,
            user_data['password'],  # 应该是哈希值
            user_data.get('email', ''),
            user_data.get('role', 'user'),
            user_data.get('name', username),
            user_data.get('guild_id'),
            user_data.get('status', 'active')
        ))

        # 清除用户缓存
        cache.delete('users')

        return True
    except Exception as e:
        print(f"保存用户失败: {e}")
        return False
    finally:
        return_db_connection(connection)

def load_guilds():
    """从数据库加载帮会数据"""
    connection = get_db_connection()
    if not connection:
        return {}

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute("SELECT * FROM guilds WHERE status = 'active'")
        db_guilds = cursor.fetchall()

        guilds = {}
        for guild in db_guilds:
            guilds[guild['id']] = {
                'id': guild['id'],
                'name': guild['name'],
                'leader': guild['leader_username'],
                'description': guild['description'],
                'created_time': guild['created_time'].isoformat() if guild['created_time'] else None,
                'members_count': guild['members_count'],
                'max_members': guild['max_members'],
                'status': guild['status']
            }

        return guilds

    except Exception as e:
        print(f"从数据库加载帮会失败: {e}")
        return {}
    finally:
        connection.close()

def save_guilds(guilds):
    """保存帮会数据"""
    os.makedirs(DATA_DIR, exist_ok=True)
    with open(GUILDS_FILE, 'w', encoding='utf-8') as f:
        json.dump(guilds, f, ensure_ascii=False, indent=2)

def load_guild_applications():
    """从数据库加载帮会申请数据"""
    connection = get_db_connection()
    if not connection:
        return []

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute("""
            SELECT * FROM guild_applications
            ORDER BY created_time DESC
        """)
        return cursor.fetchall()

    except Exception as e:
        print(f"从数据库加载帮会申请失败: {e}")
        return []
    finally:
        connection.close()

def save_guild_application(username, app_type, guild_name=None, guild_id=None, description=""):
    """保存帮会申请到数据库"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO guild_applications (username, guild_id, application_type, guild_name, description)
            VALUES (%s, %s, %s, %s, %s)
        """, (username, guild_id, app_type, guild_name, description))
        return True

    except Exception as e:
        print(f"保存帮会申请失败: {e}")
        return False
    finally:
        connection.close()

def update_application_status(app_id, status, processed_by):
    """更新申请状态"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()
        cursor.execute("""
            UPDATE guild_applications
            SET status = %s, processed_by = %s, processed_time = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (status, processed_by, app_id))
        return True

    except Exception as e:
        print(f"更新申请状态失败: {e}")
        return False
    finally:
        connection.close()

def create_guild(guild_id, guild_name, leader_username, description=""):
    """创建新帮会"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()

        # 创建帮会
        cursor.execute("""
            INSERT INTO guilds (id, name, leader_username, description)
            VALUES (%s, %s, %s, %s)
        """, (guild_id, guild_name, leader_username, description))

        # 更新用户角色和帮会ID
        cursor.execute("""
            UPDATE users
            SET role = 'guild_leader', guild_id = %s
            WHERE username = %s
        """, (guild_id, leader_username))

        # 清除用户缓存和帮会缓存
        cache.delete('users')
        cache.delete('guilds')

        return True

    except Exception as e:
        print(f"创建帮会失败: {e}")
        return False
    finally:
        connection.close()

# 角色绑定相关函数
def generate_verification_code():
    """生成6位验证码"""
    import random
    return ''.join([str(random.randint(0, 9)) for _ in range(6)])

def create_character_binding(user_id, character_name):
    """创建角色绑定申请"""
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"

    try:
        cursor = connection.cursor()

        # 检查角色是否已被绑定
        cursor.execute("SELECT user_id FROM character_bindings WHERE character_name = %s AND status = 'approved'", (character_name,))
        existing = cursor.fetchone()
        if existing:
            return False, "该角色已被其他用户绑定"

        # 检查用户是否已有待审核的绑定
        cursor.execute("SELECT id FROM character_bindings WHERE user_id = %s AND status = 'pending'", (user_id,))
        pending = cursor.fetchone()
        if pending:
            return False, "您已有待审核的角色绑定申请"

        # 生成验证码
        verification_code = generate_verification_code()
        expires = datetime.now() + timedelta(hours=24)  # 24小时有效期

        # 创建绑定申请
        cursor.execute("""
            INSERT INTO character_bindings (user_id, character_name, verification_code, verification_expires)
            VALUES (%s, %s, %s, %s)
        """, (user_id, character_name, verification_code, expires))

        connection.commit()
        return True, verification_code

    except Exception as e:
        print(f"创建角色绑定失败: {e}")
        return False, str(e)
    finally:
        return_db_connection(connection)

def get_character_bindings(user_id=None, status=None, guild_id=None):
    """获取角色绑定记录（按帮会隔离）"""
    connection = get_db_connection()
    if not connection:
        return []

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 🔒 添加帮会隔离：只获取当前帮会用户的绑定记录
        query = """
            SELECT cb.*, u.username, u.guild_id
            FROM character_bindings cb
            JOIN users u ON cb.user_id = u.id
        """
        params = []
        conditions = []

        # 如果没有指定guild_id，从当前用户获取
        if not guild_id:
            current_user = get_current_user()
            if current_user and current_user.get('guild_id'):
                guild_id = current_user['guild_id']
            else:
                # 无帮会用户或游客，返回空数据
                return []

        # 🔒 关键修复：按帮会ID过滤
        conditions.append("u.guild_id = %s")
        params.append(guild_id)

        if user_id:
            conditions.append("cb.user_id = %s")
            params.append(user_id)

        if status:
            conditions.append("cb.status = %s")
            params.append(status)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        query += " ORDER BY cb.created_at DESC"

        cursor.execute(query, params)
        bindings = cursor.fetchall()

        print(f"🔒 帮会 {guild_id} 的角色绑定记录: {len(bindings)} 条")
        return bindings

    except Exception as e:
        print(f"获取角色绑定记录失败: {e}")
        return []
    finally:
        return_db_connection(connection)

def approve_character_binding(binding_id, approved_by):
    """审核通过角色绑定（添加帮会权限检查）"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()

        # 🔒 权限检查：确保只能审核本帮会的绑定申请
        current_user = get_current_user()
        if not current_user or not current_user.get('guild_id'):
            print("❌ 无法获取当前用户帮会信息")
            return False

        admin_guild_id = current_user['guild_id']

        # 检查绑定记录是否属于当前帮会
        cursor.execute("""
            SELECT cb.id, u.guild_id, u.username, cb.character_name
            FROM character_bindings cb
            JOIN users u ON cb.user_id = u.id
            WHERE cb.id = %s
        """, (binding_id,))

        binding_info = cursor.fetchone()
        if not binding_info:
            print(f"❌ 绑定记录 {binding_id} 不存在")
            return False

        _, user_guild_id, username, character_name = binding_info

        if user_guild_id != admin_guild_id:
            print(f"❌ 权限拒绝：管理员帮会 {admin_guild_id} 不能审核其他帮会 {user_guild_id} 的绑定申请")
            return False

        # 执行审核
        cursor.execute("""
            UPDATE character_bindings
            SET status = 'approved', approved_by = %s, approved_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (approved_by, binding_id))

        connection.commit()
        print(f"✅ 已审核通过帮会 {admin_guild_id} 用户 {username} 的角色 {character_name} 绑定申请")
        return True

    except Exception as e:
        print(f"审核角色绑定失败: {e}")
        return False
    finally:
        return_db_connection(connection)

def reject_character_binding(binding_id, approved_by):
    """拒绝角色绑定（添加帮会权限检查）"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()

        # 🔒 权限检查：确保只能拒绝本帮会的绑定申请
        current_user = get_current_user()
        if not current_user or not current_user.get('guild_id'):
            print("❌ 无法获取当前用户帮会信息")
            return False

        admin_guild_id = current_user['guild_id']

        # 检查绑定记录是否属于当前帮会
        cursor.execute("""
            SELECT cb.id, u.guild_id, u.username, cb.character_name
            FROM character_bindings cb
            JOIN users u ON cb.user_id = u.id
            WHERE cb.id = %s
        """, (binding_id,))

        binding_info = cursor.fetchone()
        if not binding_info:
            print(f"❌ 绑定记录 {binding_id} 不存在")
            return False

        _, user_guild_id, username, character_name = binding_info

        if user_guild_id != admin_guild_id:
            print(f"❌ 权限拒绝：管理员帮会 {admin_guild_id} 不能拒绝其他帮会 {user_guild_id} 的绑定申请")
            return False

        # 执行拒绝
        cursor.execute("""
            UPDATE character_bindings
            SET status = 'rejected', approved_by = %s, approved_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (approved_by, binding_id))

        connection.commit()
        print(f"✅ 已拒绝帮会 {admin_guild_id} 用户 {username} 的角色 {character_name} 绑定申请")
        return True

    except Exception as e:
        print(f"拒绝角色绑定失败: {e}")
        return False
    finally:
        return_db_connection(connection)

def get_user_bound_character(user_id):
    """获取用户绑定的角色"""
    connection = get_db_connection()
    if not connection:
        return None

    try:
        cursor = connection.cursor()

        cursor.execute("""
            SELECT character_name FROM character_bindings
            WHERE user_id = %s AND status = 'approved'
        """, (user_id,))

        result = cursor.fetchone()
        return result[0] if result else None

    except Exception as e:
        print(f"获取用户绑定角色失败: {e}")
        return None
    finally:
        return_db_connection(connection)

def unbind_character(character_name, admin_username, reason="管理员解绑"):
    """解绑角色（管理员操作，添加帮会权限检查）"""
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"

    try:
        cursor = connection.cursor()

        # 🔒 权限检查：确保只能解绑本帮会的角色
        current_user = get_current_user()
        if not current_user or not current_user.get('guild_id'):
            return False, "无法获取当前用户帮会信息"

        admin_guild_id = current_user['guild_id']

        # 检查角色是否已绑定，并验证帮会权限
        cursor.execute("""
            SELECT cb.id, cb.user_id, u.username, u.guild_id
            FROM character_bindings cb
            JOIN users u ON cb.user_id = u.id
            WHERE cb.character_name = %s AND cb.status = 'approved'
        """, (character_name,))

        binding = cursor.fetchone()
        if not binding:
            return False, "该角色未绑定或不存在"

        binding_id, user_id, username, user_guild_id = binding

        # 🔒 帮会权限检查
        if user_guild_id != admin_guild_id:
            return False, f"权限拒绝：不能解绑其他帮会的角色（角色属于帮会 {user_guild_id}）"

        # 将绑定状态改为已解绑
        cursor.execute("""
            UPDATE character_bindings
            SET status = 'unbound', approved_by = %s, approved_at = CURRENT_TIMESTAMP, reason = %s
            WHERE id = %s
        """, (admin_username, reason, binding_id))

        # 记录解绑日志
        cursor.execute("""
            INSERT INTO character_bindings (user_id, character_name, status, approved_by, approved_at, reason)
            VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, %s)
        """, (user_id, f"[解绑]{character_name}", 'unbound_log', admin_username, reason))

        connection.commit()

        # 创建通知
        user = get_current_user()
        if user and user.get('guild_id'):
            create_notification(
                guild_id=user['guild_id'],
                title="角色解绑通知",
                message=f"管理员已解绑用户 {username} 的角色 {character_name}。原因：{reason}",
                notification_type='warning'
            )

        return True, f"已成功解绑用户 {username} 的角色 {character_name}"

    except Exception as e:
        print(f"解绑角色失败: {e}")
        connection.rollback()
        return False, f"解绑失败：{str(e)}"
    finally:
        return_db_connection(connection)

def force_bind_character(user_id, character_name, admin_username, reason="管理员强制绑定"):
    """强制绑定角色（管理员操作，会先解绑原用户，添加帮会权限检查）"""
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"

    try:
        cursor = connection.cursor()

        # 🔒 权限检查：确保只能操作本帮会的用户
        current_user = get_current_user()
        if not current_user or not current_user.get('guild_id'):
            return False, "无法获取当前用户帮会信息"

        admin_guild_id = current_user['guild_id']

        # 获取新用户信息并检查帮会权限
        cursor.execute("SELECT username, guild_id FROM users WHERE id = %s", (user_id,))
        new_user = cursor.fetchone()
        if not new_user:
            return False, "目标用户不存在"

        new_username, new_user_guild_id = new_user

        # 🔒 帮会权限检查
        if new_user_guild_id != admin_guild_id:
            return False, f"权限拒绝：不能为其他帮会的用户绑定角色（用户属于帮会 {new_user_guild_id}）"

        # 检查目标用户是否已有绑定
        cursor.execute("""
            SELECT character_name FROM character_bindings
            WHERE user_id = %s AND status = 'approved'
        """, (user_id,))

        existing_char = cursor.fetchone()
        if existing_char:
            return False, f"该用户已绑定角色 {existing_char[0]}，请先解绑"

        # 检查角色是否已被其他用户绑定
        cursor.execute("""
            SELECT cb.user_id, u.username
            FROM character_bindings cb
            JOIN users u ON cb.user_id = u.id
            WHERE cb.character_name = %s AND cb.status = 'approved'
        """, (character_name,))

        existing_binding = cursor.fetchone()
        if existing_binding:
            old_user_id, old_username = existing_binding
            # 直接在当前事务中解绑原用户，避免递归调用
            cursor.execute("""
                UPDATE character_bindings
                SET status = 'unbound', approved_by = %s, approved_at = CURRENT_TIMESTAMP, reason = %s
                WHERE character_name = %s AND status = 'approved'
            """, (admin_username, f"为用户 {new_username} 换绑", character_name))

            # 记录解绑日志
            cursor.execute("""
                INSERT INTO character_bindings (user_id, character_name, status, approved_by, approved_at, reason)
                VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, %s)
            """, (old_user_id, f"[解绑]{character_name}", 'unbound_log', admin_username, f"为用户 {new_username} 换绑"))

        # 创建新的绑定
        cursor.execute("""
            INSERT INTO character_bindings (user_id, character_name, status, approved_by, approved_at, reason)
            VALUES (%s, %s, 'approved', %s, CURRENT_TIMESTAMP, %s)
        """, (user_id, character_name, admin_username, reason))

        connection.commit()

        # 创建通知
        user = get_current_user()
        if user and user.get('guild_id'):
            create_notification(
                guild_id=user['guild_id'],
                title="角色强制绑定通知",
                message=f"管理员已将角色 {character_name} 强制绑定给用户 {new_username}。原因：{reason}",
                notification_type='info'
            )

        return True, f"已成功将角色 {character_name} 绑定给用户 {new_username}"

    except Exception as e:
        print(f"强制绑定角色失败: {e}")
        connection.rollback()
        return False, f"强制绑定失败：{str(e)}"
    finally:
        return_db_connection(connection)

def load_members(guild_id=None):
    """加载成员数据（根据帮会ID进行数据隔离）"""
    # 获取当前用户的帮会ID
    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            # 游客或无帮会用户，返回空数据
            return []

    # 使用帮会ID作为缓存键，实现数据隔离
    cache_key = f'members_{guild_id}'
    cached_members = cache.get(cache_key)
    if cached_members:
        return cached_members

    # 从数据库加载对应帮会的成员数据
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT * FROM guild_members
                WHERE guild_id = %s
                ORDER BY name
            """, (guild_id,))
            db_members = cursor.fetchall()

            if db_members:
                # 转换为应用格式
                members = []
                for member in db_members:
                    # 解析技能JSON数据
                    skills = []
                    if member.get('skills'):
                        try:
                            skills = json.loads(member['skills'])
                        except (json.JSONDecodeError, TypeError):
                            skills = []

                    members.append({
                        'name': member['name'],
                        'profession': member['profession'],
                        'main_group': member['main_group'],
                        'sub_team': member['sub_team'],
                        'squad': member['squad'],
                        'position': member['position'],
                        'status': member['status'],
                        'skills': skills
                    })
                print(f"从数据库加载了帮会 {guild_id} 的 {len(members)} 个成员")
                # 使用帮会特定的缓存键
                cache.set(cache_key, members)
                return members

        except Exception as e:
            print(f"从数据库加载帮会 {guild_id} 成员失败: {e}")
        finally:
            return_db_connection(connection)

    print(f"帮会 {guild_id} 暂无成员数据")
    return []

def save_members(members, guild_id=None):
    """保存成员数据（根据帮会ID进行数据隔离）- 改进版本避免重复"""
    # 获取当前用户的帮会ID
    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            print("无法确定帮会ID，无法保存成员数据")
            return False

    # 保存到数据库
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()

            # 首先确保表有正确的唯一键约束
            try:
                cursor.execute("""
                    ALTER TABLE guild_members
                    ADD UNIQUE KEY unique_guild_member (guild_id, name)
                """)
                print("✅ 添加唯一键约束成功")
            except Exception as e:
                # 约束可能已经存在，忽略错误
                pass

            # 使用更安全的方法：先检查再插入/更新
            for member in members:
                member_name = member.get('name', '')
                if not member_name:
                    continue

                # 检查成员是否已存在
                cursor.execute("""
                    SELECT id FROM guild_members
                    WHERE guild_id = %s AND name = %s
                """, (guild_id, member_name))

                existing = cursor.fetchone()

                if existing:
                    # 更新现有成员
                    cursor.execute("""
                        UPDATE guild_members SET
                        profession = %s,
                        main_group = %s,
                        sub_team = %s,
                        squad = %s,
                        position = %s,
                        status = %s
                        WHERE guild_id = %s AND name = %s
                    """, (
                        member.get('profession', ''),
                        member.get('main_group', ''),
                        member.get('sub_team', ''),
                        member.get('squad', ''),
                        member.get('position', ''),
                        member.get('status', '主力'),
                        guild_id,
                        member_name
                    ))
                else:
                    # 插入新成员
                    cursor.execute("""
                        INSERT INTO guild_members
                        (guild_id, name, profession, main_group, sub_team, squad, position, status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        guild_id,
                        member_name,
                        member.get('profession', ''),
                        member.get('main_group', ''),
                        member.get('sub_team', ''),
                        member.get('squad', ''),
                        member.get('position', ''),
                        member.get('status', '主力')
                    ))

            connection.commit()
            print(f"成功保存帮会 {guild_id} 的 {len(members)} 个成员到数据库")

            # 🚪 检查踢出逻辑：处理帮外成员
            try:
                # 获取当前用户信息
                user = get_current_user()
                admin_username = user.get('username', 'system') if user else 'system'

                # 查找被移到帮外的成员
                outside_members = [m for m in members if m.get('main_group') == '帮外']
                for member in outside_members:
                    member_name = member.get('name', '')
                    if member_name:
                        # 创建踢出记录
                        success, message = create_kick_record(guild_id, member_name, admin_username)
                        if success:
                            print(f"🚪 {message}")

                # 查找从帮外移回的成员（不在帮外但有待处理踢出记录的）
                non_outside_members = [m.get('name', '') for m in members if m.get('main_group') != '帮外' and m.get('name')]
                if non_outside_members:
                    # 查询这些成员中有待处理踢出记录的
                    cursor.execute("""
                        SELECT member_name FROM guild_kick_records
                        WHERE guild_id = %s AND status = 'pending' AND member_name IN ({})
                    """.format(','.join(['%s'] * len(non_outside_members))),
                    [guild_id] + non_outside_members)

                    moved_back_members = cursor.fetchall()
                    for (member_name,) in moved_back_members:
                        # 取消踢出记录
                        success, message = cancel_kick_record(guild_id, member_name, admin_username)
                        if success:
                            print(f"🔄 {message}")

            except Exception as e:
                print(f"处理踢出逻辑时出错: {e}")

            # 清除该帮会的成员缓存
            cache_key = f'members_{guild_id}'
            cache.delete(cache_key)

            return True

        except Exception as e:
            print(f"保存帮会 {guild_id} 成员到数据库失败: {e}")
            import traceback
            traceback.print_exc()
            connection.rollback()
            return False
        finally:
            return_db_connection(connection)

    return False

def clean_duplicate_members(guild_id=None):
    """清理重复的成员数据"""
    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            print("无法确定帮会ID")
            return False

    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()

            # 查找重复的成员
            cursor.execute("""
                SELECT guild_id, name, COUNT(*) as count
                FROM guild_members
                WHERE guild_id = %s
                GROUP BY guild_id, name
                HAVING COUNT(*) > 1
            """, (guild_id,))

            duplicates = cursor.fetchall()

            if duplicates:
                print(f"🔍 发现 {len(duplicates)} 个重复成员，开始清理...")

                for guild_id_dup, name, count in duplicates:
                    print(f"清理重复成员: {name} (重复 {count} 次)")

                    # 保留最新的记录，删除旧的
                    cursor.execute("""
                        DELETE FROM guild_members
                        WHERE guild_id = %s AND name = %s
                        AND id NOT IN (
                            SELECT * FROM (
                                SELECT MAX(id) FROM guild_members
                                WHERE guild_id = %s AND name = %s
                            ) as temp
                        )
                    """, (guild_id_dup, name, guild_id_dup, name))

                connection.commit()
                print(f"✅ 清理完成，删除了重复记录")

                # 清除缓存
                cache_key = f'members_{guild_id}'
                cache.delete(cache_key)

            else:
                print("✅ 没有发现重复成员")

            return True

        except Exception as e:
            print(f"清理重复成员失败: {e}")
            import traceback
            traceback.print_exc()
            connection.rollback()
            return False
        finally:
            return_db_connection(connection)

    return False

# 🆕 排表快照功能（新功能 - 安全添加）
def create_roster_snapshot(guild_id=None, snapshot_type='manual', notes=None):
    """创建排表快照"""
    from datetime import datetime, timedelta

    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            print("无法确定帮会ID")
            return False

    # 获取当前排表数据
    members = load_members(guild_id)
    if not members:
        print("没有成员数据，无法创建快照")
        return False

    # 计算本周开始日期（周六为一周开始）
    now = datetime.now()
    days_since_saturday = (now.weekday() + 2) % 7  # 周六=0, 周日=1, ..., 周五=6
    week_start = (now - timedelta(days=days_since_saturday)).date()

    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()

            # 检查本周是否已有快照
            cursor.execute("""
                SELECT id FROM roster_snapshots
                WHERE guild_id = %s AND week_start_date = %s AND snapshot_type = %s
            """, (guild_id, week_start, snapshot_type))

            existing = cursor.fetchone()
            if existing and snapshot_type == 'weekly_auto':
                print(f"本周已存在自动快照，跳过创建")
                return True

            # 获取创建者ID
            created_by = None
            user = get_current_user()
            if user and 'username' in user:
                cursor.execute("SELECT id FROM users WHERE username = %s", (user['username'],))
                user_result = cursor.fetchone()
                if user_result:
                    created_by = user_result[0]

            # 创建快照
            cursor.execute("""
                INSERT INTO roster_snapshots
                (guild_id, snapshot_time, week_start_date, snapshot_type, member_data, created_by, notes)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                guild_id,
                now,
                week_start,
                snapshot_type,
                json.dumps(members, ensure_ascii=False),
                created_by,
                notes
            ))

            connection.commit()
            print(f"✅ 成功创建排表快照: {snapshot_type}, 周期: {week_start}, 成员数: {len(members)}")
            return True

        except Exception as e:
            print(f"创建排表快照失败: {e}")
            import traceback
            traceback.print_exc()
            connection.rollback()
            return False
        finally:
            return_db_connection(connection)

    return False

def get_roster_snapshot_for_battle(battle_time, guild_id=None):
    """根据战斗时间获取对应的排表快照"""
    from datetime import datetime, timedelta

    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            return None

    # 解析战斗时间
    if isinstance(battle_time, str):
        try:
            battle_dt = datetime.fromisoformat(battle_time.replace('T', ' ').split('.')[0])
        except:
            print(f"无法解析战斗时间: {battle_time}")
            return None
    else:
        battle_dt = battle_time

    # 计算战斗时间对应的周期开始日期
    days_since_saturday = (battle_dt.weekday() + 2) % 7
    week_start = (battle_dt - timedelta(days=days_since_saturday)).date()

    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            # 查找对应周期的快照（优先级：battle_start > weekly_auto > manual）
            cursor.execute("""
                SELECT * FROM roster_snapshots
                WHERE guild_id = %s AND week_start_date = %s
                ORDER BY
                    CASE snapshot_type
                        WHEN 'battle_start' THEN 1
                        WHEN 'weekly_auto' THEN 2
                        WHEN 'manual' THEN 3
                    END,
                    snapshot_time DESC
                LIMIT 1
            """, (guild_id, week_start))

            snapshot = cursor.fetchone()
            if snapshot:
                # 解析成员数据
                member_data = json.loads(snapshot['member_data'])
                print(f"📸 找到匹配的排表快照: {snapshot['snapshot_type']}, 时间: {snapshot['snapshot_time']}")
                return {
                    'snapshot_id': snapshot['id'],
                    'snapshot_time': snapshot['snapshot_time'],
                    'snapshot_type': snapshot['snapshot_type'],
                    'week_start_date': snapshot['week_start_date'],
                    'member_data': member_data,
                    'notes': snapshot['notes']
                }
            else:
                print(f"⚠️ 未找到战斗时间 {battle_time} 对应的排表快照")
                return None

        except Exception as e:
            print(f"获取排表快照失败: {e}")
            return None
        finally:
            return_db_connection(connection)

    return None

# 🚪 帮外成员踢出管理功能
def create_kick_record(guild_id, member_name, admin_username):
    """创建踢出记录（成员被移到帮外时调用）"""
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"

    try:
        cursor = connection.cursor()

        # 检查是否已存在待处理的踢出记录
        cursor.execute("""
            SELECT id FROM guild_kick_records
            WHERE guild_id = %s AND member_name = %s AND status = 'pending'
        """, (guild_id, member_name))

        existing = cursor.fetchone()
        if existing:
            return True, "该成员已在踢出倒计时中"

        # 获取成员绑定的用户ID
        cursor.execute("""
            SELECT cb.user_id FROM character_bindings cb
            WHERE cb.character_name = %s AND cb.status = 'approved'
        """, (member_name,))

        user_binding = cursor.fetchone()
        user_id = user_binding[0] if user_binding else None

        # 计算踢出截止时间（3天后）
        kick_deadline = datetime.now() + timedelta(days=3)

        # 创建踢出记录
        cursor.execute("""
            INSERT INTO guild_kick_records
            (guild_id, member_name, moved_to_outside_time, kick_deadline, user_id, kicked_by, reason)
            VALUES (%s, %s, CURRENT_TIMESTAMP, %s, %s, %s, %s)
        """, (guild_id, member_name, kick_deadline, user_id, admin_username, "移动到帮外，3天后自动踢出"))

        connection.commit()

        # 创建通知
        create_notification(
            guild_id=guild_id,
            title="成员踢出倒计时",
            message=f"成员 {member_name} 已被移至帮外，将在3天后自动踢出帮会",
            notification_type='warning'
        )

        return True, f"已为成员 {member_name} 创建3天踢出倒计时"

    except Exception as e:
        print(f"创建踢出记录失败: {e}")
        connection.rollback()
        return False, f"创建踢出记录失败：{str(e)}"
    finally:
        return_db_connection(connection)

def cancel_kick_record(guild_id, member_name, admin_username):
    """取消踢出记录（成员从帮外移回时调用）"""
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"

    try:
        cursor = connection.cursor()

        # 更新踢出记录状态为已取消
        cursor.execute("""
            UPDATE guild_kick_records
            SET status = 'cancelled', kicked_by = %s, kicked_time = CURRENT_TIMESTAMP, reason = %s
            WHERE guild_id = %s AND member_name = %s AND status = 'pending'
        """, (admin_username, "成员已移回帮内", guild_id, member_name))

        affected_rows = cursor.rowcount
        connection.commit()

        if affected_rows > 0:
            # 创建通知
            create_notification(
                guild_id=guild_id,
                title="踢出倒计时取消",
                message=f"成员 {member_name} 已移回帮内，踢出倒计时已取消",
                notification_type='info'
            )
            return True, f"已取消成员 {member_name} 的踢出倒计时"
        else:
            return False, "未找到待处理的踢出记录"

    except Exception as e:
        print(f"取消踢出记录失败: {e}")
        connection.rollback()
        return False, f"取消踢出记录失败：{str(e)}"
    finally:
        return_db_connection(connection)

def manual_kick_member(guild_id, member_name, admin_username, reason="管理员直接踢出"):
    """管理员手动踢出成员"""
    connection = get_db_connection()
    if not connection:
        return False, "数据库连接失败"

    try:
        cursor = connection.cursor()

        # 检查成员是否存在
        cursor.execute("""
            SELECT id FROM guild_members
            WHERE guild_id = %s AND name = %s
        """, (guild_id, member_name))

        member = cursor.fetchone()
        if not member:
            return False, "成员不存在"

        # 删除成员记录
        cursor.execute("""
            DELETE FROM guild_members
            WHERE guild_id = %s AND name = %s
        """, (guild_id, member_name))

        # 更新或创建踢出记录
        cursor.execute("""
            INSERT INTO guild_kick_records
            (guild_id, member_name, moved_to_outside_time, kick_deadline, status, kicked_time, kicked_by, reason)
            VALUES (%s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'manual_kick', CURRENT_TIMESTAMP, %s, %s)
            ON DUPLICATE KEY UPDATE
            status = 'manual_kick', kicked_time = CURRENT_TIMESTAMP, kicked_by = VALUES(kicked_by), reason = VALUES(reason)
        """, (guild_id, member_name, admin_username, reason))

        connection.commit()

        # 创建通知
        create_notification(
            guild_id=guild_id,
            title="成员被踢出帮会",
            message=f"管理员 {admin_username} 将成员 {member_name} 踢出帮会。原因：{reason}",
            notification_type='warning'
        )

        return True, f"已成功踢出成员 {member_name}"

    except Exception as e:
        print(f"手动踢出成员失败: {e}")
        connection.rollback()
        return False, f"踢出成员失败：{str(e)}"
    finally:
        return_db_connection(connection)

def get_pending_kick_records(guild_id=None):
    """获取待处理的踢出记录"""
    connection = get_db_connection()
    if not connection:
        return []

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        if guild_id:
            cursor.execute("""
                SELECT kr.*, u.username as bound_username
                FROM guild_kick_records kr
                LEFT JOIN users u ON kr.user_id = u.id
                WHERE kr.guild_id = %s AND kr.status = 'pending'
                ORDER BY kr.kick_deadline ASC
            """, (guild_id,))
        else:
            cursor.execute("""
                SELECT kr.*, u.username as bound_username
                FROM guild_kick_records kr
                LEFT JOIN users u ON kr.user_id = u.id
                WHERE kr.status = 'pending'
                ORDER BY kr.kick_deadline ASC
            """)

        return cursor.fetchall()

    except Exception as e:
        print(f"获取踢出记录失败: {e}")
        return []
    finally:
        return_db_connection(connection)

def process_auto_kicks():
    """处理自动踢出（定时任务调用）"""
    connection = get_db_connection()
    if not connection:
        return

    try:
        cursor = connection.cursor()

        # 查找到期的踢出记录
        cursor.execute("""
            SELECT kr.id, kr.guild_id, kr.member_name, kr.user_id, u.username
            FROM guild_kick_records kr
            LEFT JOIN users u ON kr.user_id = u.id
            WHERE kr.status = 'pending' AND kr.kick_deadline <= CURRENT_TIMESTAMP
        """)

        expired_records = cursor.fetchall()

        for record in expired_records:
            record_id, guild_id, member_name, user_id, username = record

            try:
                # 只踢出有绑定账户的成员
                if user_id and username:
                    # 删除成员记录
                    cursor.execute("""
                        DELETE FROM guild_members
                        WHERE guild_id = %s AND name = %s
                    """, (guild_id, member_name))

                    # 更新踢出记录状态
                    cursor.execute("""
                        UPDATE guild_kick_records
                        SET status = 'kicked', kicked_time = CURRENT_TIMESTAMP, kicked_by = 'system',
                            reason = '3天倒计时到期，自动踢出'
                        WHERE id = %s
                    """, (record_id,))

                    # 创建通知
                    create_notification(
                        guild_id=guild_id,
                        title="成员自动踢出",
                        message=f"成员 {member_name}（用户：{username}）3天倒计时到期，已自动踢出帮会",
                        notification_type='warning'
                    )

                    print(f"✅ 自动踢出成员: {member_name} (用户: {username})")
                else:
                    # 没有绑定账户的成员，取消踢出
                    cursor.execute("""
                        UPDATE guild_kick_records
                        SET status = 'cancelled', kicked_time = CURRENT_TIMESTAMP, kicked_by = 'system',
                            reason = '成员未绑定账户，取消自动踢出'
                        WHERE id = %s
                    """, (record_id,))

                    print(f"⏭️ 取消踢出未绑定成员: {member_name}")

            except Exception as e:
                print(f"处理踢出记录 {record_id} 失败: {e}")
                continue

        connection.commit()

        if expired_records:
            print(f"🚪 处理了 {len(expired_records)} 条到期踢出记录")

    except Exception as e:
        print(f"处理自动踢出失败: {e}")
        connection.rollback()
    finally:
        return_db_connection(connection)

# 🕐 自动定时任务功能
def schedule_weekly_snapshots():
    """启动每周自动快照的定时任务"""
    def check_and_create_snapshot():
        while True:
            try:
                now = datetime.now()

                # 每分钟检查一次自动踢出
                process_auto_kicks()

                # 检查是否是周六晚上8点（20:00）
                if now.weekday() == 5 and now.hour == 20 and now.minute == 0:
                    print(f"🕐 触发每周自动快照创建: {now}")

                    # 获取所有活跃的帮会
                    connection = get_db_connection()
                    if connection:
                        try:
                            cursor = connection.cursor()
                            cursor.execute("SELECT id FROM guilds WHERE status = 'active'")
                            guilds = cursor.fetchall()

                            for guild_tuple in guilds:
                                guild_id = guild_tuple[0]
                                print(f"📸 为帮会 {guild_id} 创建自动快照...")
                                success = create_roster_snapshot(
                                    guild_id=guild_id,
                                    snapshot_type='weekly_auto',
                                    notes=f'每周自动快照 - {now.strftime("%Y年%m月%d日")}'
                                )
                                if success:
                                    print(f"✅ 帮会 {guild_id} 自动快照创建成功")
                                else:
                                    print(f"❌ 帮会 {guild_id} 自动快照创建失败")

                        except Exception as e:
                            print(f"自动快照创建过程出错: {e}")
                        finally:
                            return_db_connection(connection)

                    # 等待61秒，避免重复触发
                    time.sleep(61)
                else:
                    # 每分钟检查一次
                    time.sleep(60)

            except Exception as e:
                print(f"定时任务异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

    # 启动后台线程
    snapshot_thread = threading.Thread(target=check_and_create_snapshot, daemon=True)
    snapshot_thread.start()
    print("🕐 每周自动快照定时任务已启动")
    print("🚪 自动踢出检查任务已启动")

def create_battle_start_snapshot(guild_id=None):
    """战斗开始时创建快照"""
    return create_roster_snapshot(
        guild_id=guild_id,
        snapshot_type='battle_start',
        notes='战斗开始时的排表快照'
    )

def load_battle_records(guild_id=None):
    """加载战斗记录（根据帮会ID进行数据隔离）"""
    # 获取当前用户的帮会ID
    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            # 游客或无帮会用户，返回空数据
            return []

    # 使用帮会ID作为缓存键，实现数据隔离
    cache_key = f'battle_records_{guild_id}'
    cached_records = cache.get(cache_key)
    if cached_records:
        return cached_records

    # 从数据库加载对应帮会的战斗记录
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT * FROM battle_records
                WHERE guild_id = %s
                ORDER BY upload_time DESC
            """, (guild_id,))
            db_records = cursor.fetchall()

            if db_records:
                # 转换为应用格式
                records = []
                for record in db_records:
                    # 解析JSON字段
                    member_performance = json.loads(record.get('member_performance', '{}'))

                    records.append({
                        'battle_id': record['battle_id'],
                        'our_guild': record['our_guild'],
                        'enemy_guild': record['enemy_guild'],
                        'upload_time': record['upload_time'].isoformat() if record['upload_time'] else None,
                        'member_performance': member_performance
                    })
                print(f"从数据库加载了帮会 {guild_id} 的 {len(records)} 条战斗记录")
                # 使用帮会特定的缓存键
                cache.set(cache_key, records)
                return records

        except Exception as e:
            print(f"从数据库加载帮会 {guild_id} 战斗记录失败: {e}")
        finally:
            return_db_connection(connection)

    print(f"帮会 {guild_id} 暂无战斗记录")
    return []

def save_battle_records(records, guild_id=None):
    """保存战斗记录（根据帮会ID进行数据隔离）"""
    # 获取当前用户的帮会ID
    if not guild_id:
        user = get_current_user()
        if user and user.get('guild_id'):
            guild_id = user['guild_id']
        else:
            print("无法确定帮会ID，无法保存战斗记录")
            return False

    # 保存到数据库
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()

            # 先删除该帮会的所有战斗记录
            cursor.execute("DELETE FROM battle_records WHERE guild_id = %s", (guild_id,))

            # 插入新的战斗记录
            for record in records:
                cursor.execute("""
                    INSERT INTO battle_records
                    (guild_id, battle_id, our_guild, enemy_guild, upload_time, member_performance)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    guild_id,
                    record.get('battle_id', ''),
                    record.get('our_guild', ''),
                    record.get('enemy_guild', ''),
                    record.get('upload_time'),
                    json.dumps(record.get('member_performance', {}), ensure_ascii=False)
                ))

            connection.commit()
            print(f"成功保存帮会 {guild_id} 的 {len(records)} 条战斗记录到数据库")

            # 清除该帮会的战斗记录缓存
            cache_key = f'battle_records_{guild_id}'
            cache.delete(cache_key)

            # 如果是纸落云烟帮会，同时保存到JSON文件
            if guild_id == 'zhiluoyunyan':
                try:
                    os.makedirs('data', exist_ok=True)
                    with open(BATTLE_RECORDS_FILE, 'w', encoding='utf-8') as f:
                        json.dump(records, f, ensure_ascii=False, indent=2)
                    print(f"同时保存纸落云烟帮会战斗记录到JSON文件")
                except Exception as e:
                    print(f"保存JSON文件失败: {e}")

            return True

        except Exception as e:
            print(f"保存帮会 {guild_id} 战斗记录到数据库失败: {e}")
            connection.rollback()
            return False
        finally:
            return_db_connection(connection)

    return False

def migrate_json_to_database():
    """一次性迁移JSON文件中的战斗记录到数据库"""
    try:
        # 检查JSON文件是否存在
        if not os.path.exists(BATTLE_RECORDS_FILE):
            print("JSON战斗记录文件不存在，无需迁移")
            return True

        # 读取JSON文件
        with open(BATTLE_RECORDS_FILE, 'r', encoding='utf-8') as f:
            json_records = json.load(f)

        if not json_records:
            print("JSON文件中没有战斗记录，无需迁移")
            return True

        print(f"开始迁移 {len(json_records)} 条战斗记录到数据库...")

        # 保存到数据库（纸落云烟帮会）
        guild_id = 'zhiluoyunyan'
        success = save_battle_records(json_records, guild_id)

        if success:
            print(f"✅ 成功迁移 {len(json_records)} 条战斗记录到数据库")

            # 备份JSON文件
            backup_file = BATTLE_RECORDS_FILE + '.backup'
            os.rename(BATTLE_RECORDS_FILE, backup_file)
            print(f"✅ JSON文件已备份为: {backup_file}")

            return True
        else:
            print("❌ 迁移失败")
            return False

    except Exception as e:
        print(f"❌ 迁移过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def clean_guild_member_data(guild_id):
    """清理指定帮会的成员数据，去除重复和无效数据"""
    try:
        print(f"🧹 开始清理帮会 {guild_id} 的成员数据...")

        # 从数据库加载所有成员数据
        connection = get_db_connection()
        if not connection:
            print("❌ 无法连接数据库")
            return False

        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT * FROM guild_members
                WHERE guild_id = %s
                ORDER BY name
            """, (guild_id,))
            all_members = cursor.fetchall()

            print(f"📊 数据库中共有 {len(all_members)} 条成员记录")

            if not all_members:
                print("ℹ️ 没有找到成员数据，无需清理")
                return True

            # 去重逻辑：以成员姓名为键，保留最新的记录
            unique_members = {}
            duplicate_count = 0

            for member in all_members:
                name = member['name']
                if not name or name.strip() == '':
                    duplicate_count += 1
                    continue

                if name in unique_members:
                    duplicate_count += 1
                    print(f"🔍 发现重复成员: {name}")
                else:
                    unique_members[name] = member

            print(f"📈 去重结果: 原有 {len(all_members)} 条记录，去重后 {len(unique_members)} 条记录，删除 {duplicate_count} 条重复/无效记录")

            if duplicate_count == 0:
                print("✅ 数据已经是干净的，无需清理")
                return True

            # 清空该帮会的所有成员数据
            cursor.execute("DELETE FROM guild_members WHERE guild_id = %s", (guild_id,))

            # 重新插入去重后的数据
            for member in unique_members.values():
                cursor.execute("""
                    INSERT INTO guild_members
                    (guild_id, name, profession, main_group, sub_team, squad, position, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    guild_id,
                    member['name'],
                    member['profession'],
                    member['main_group'],
                    member['sub_team'],
                    member['squad'],
                    member['position'],
                    member['status']
                ))

            connection.commit()

            # 清除缓存
            cache_key = f'members_{guild_id}'
            cache.delete(cache_key)

            print(f"✅ 成功清理帮会 {guild_id} 的成员数据")
            print(f"📊 最终结果: {len(unique_members)} 个唯一成员")

            return True

        except Exception as e:
            connection.rollback()
            print(f"❌ 清理过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"❌ 清理帮会成员数据失败: {e}")
        return False

def migrate_members_json_to_database():
    """一次性迁移JSON文件中的成员数据到数据库"""
    try:
        # 检查JSON文件是否存在
        if not os.path.exists(MEMBERS_FILE):
            print("JSON成员数据文件不存在，无需迁移")
            return True

        # 读取JSON文件
        with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
            json_members = json.load(f)

        if not json_members:
            print("JSON文件中没有成员数据，无需迁移")
            return True

        print(f"开始迁移 {len(json_members)} 个成员到数据库...")

        # 保存到数据库（纸落云烟帮会）
        guild_id = 'zhiluoyunyan'
        success = save_members(json_members, guild_id)

        if success:
            print(f"✅ 成功迁移 {len(json_members)} 个成员到数据库")

            # 备份JSON文件
            backup_file = MEMBERS_FILE + '.backup'
            os.rename(MEMBERS_FILE, backup_file)
            print(f"✅ JSON文件已备份为: {backup_file}")

            return True
        else:
            print("❌ 成员数据迁移失败")
            return False

    except Exception as e:
        print(f"❌ 成员数据迁移过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_team_type_from_member(member_info):
    """从成员信息获取团队类型"""
    main_group = member_info.get('main_group', '')
    if '进攻' in main_group:
        return '进攻团'
    elif '防守' in main_group:
        return '防守团'
    else:
        return '其他团'

def infer_team_type_from_battle_data(battle_member, profession):
    """从战斗数据推断团队类型"""
    # 简单推断逻辑，可以根据需要扩展
    building_damage = battle_member.get('building_damage', 0)
    player_damage = battle_member.get('player_damage', 0)

    # 建筑伤害高的可能是进攻团
    if building_damage > player_damage * 2:
        return '进攻团'
    # 玩家伤害高的可能是防守团
    elif player_damage > building_damage:
        return '防守团'
    else:
        return '其他团'

def infer_enemy_guild_from_data(battle_data):
    """从战斗数据推断对手帮会名称"""
    try:
        # 获取当前用户的帮会名称
        user = get_current_user()
        our_guild = None
        if user and user.get('guild_id'):
            guilds = load_guilds()
            guild_info = guilds.get(user['guild_id'])
            if guild_info:
                our_guild = guild_info['name']

        # 如果没有找到当前帮会名称，尝试从战斗数据中推断
        if not our_guild and battle_data:
            # 获取所有帮会名称，选择数据最多的作为当前帮会
            guild_counts = {}
            for b in battle_data:
                guild = b.get('guild', '')
                if guild:
                    guild_counts[guild] = guild_counts.get(guild, 0) + 1
            if guild_counts:
                our_guild = max(guild_counts.items(), key=lambda x: x[1])[0]

        # 获取所有非当前帮会的帮会名称
        enemy_guilds = set()
        for member in battle_data:
            guild = member.get('guild', '')
            if guild and guild != our_guild:
                enemy_guilds.add(guild)

        if len(enemy_guilds) == 1:
            return list(enemy_guilds)[0]
        elif len(enemy_guilds) > 1:
            # 如果有多个对手帮会，选择人数最多的
            guild_counts = {}
            for member in battle_data:
                guild = member.get('guild', '')
                if guild and guild != our_guild:
                    guild_counts[guild] = guild_counts.get(guild, 0) + 1

            if guild_counts:
                return max(guild_counts.items(), key=lambda x: x[1])[0]

        return "未知对手"
    except Exception as e:
        print(f"推断对手帮会失败: {e}")
        return "未知对手"

def create_battle_record(csv_filename, battle_data, member_performance):
    """创建新的战斗记录"""
    # 从文件名提取信息
    filename_parts = csv_filename.replace('.csv', '').split('_')

    print(f"解析文件名: {csv_filename}")
    print(f"文件名分段: {filename_parts}")

    # 获取当前用户的帮会名称
    user = get_current_user()
    our_guild = "未知帮会"
    if user and user.get('guild_id'):
        guilds = load_guilds()
        guild_info = guilds.get(user['guild_id'])
        if guild_info:
            our_guild = guild_info['name']

    # 如果没有找到当前帮会名称，尝试从战斗数据中推断
    if our_guild == "未知帮会" and battle_data:
        # 获取所有帮会名称，选择数据最多的作为当前帮会
        guild_counts = {}
        for b in battle_data:
            guild = b.get('guild', '')
            if guild:
                guild_counts[guild] = guild_counts.get(guild, 0) + 1
        if guild_counts:
            our_guild = max(guild_counts.items(), key=lambda x: x[1])[0]

    enemy_guild = "未知对手"

    if len(filename_parts) >= 2:
        if len(filename_parts) >= 4 and filename_parts[2] and filename_parts[3]:  # 格式：日期_时间_纸落云烟_对手帮会
            date_str = filename_parts[0]
            time_str = filename_parts[1]
            our_guild = filename_parts[2]
            enemy_guild = filename_parts[3]
            battle_id = f"{date_str}_{time_str}_{our_guild}_{enemy_guild}"
        elif our_guild in filename_parts:  # 包含当前帮会的格式
            # 找到当前帮会的位置
            guild_index = filename_parts.index(our_guild)
            if guild_index + 1 < len(filename_parts) and filename_parts[guild_index + 1]:
                enemy_guild = filename_parts[guild_index + 1]
            else:
                enemy_guild = "未知对手"
            battle_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{our_guild}_{enemy_guild}"
        elif len(filename_parts) >= 2 and filename_parts[0] and filename_parts[1] and not filename_parts[0].isdigit():  # 格式：帮会1_帮会2（排除日期时间）
            our_guild = filename_parts[0]
            enemy_guild = filename_parts[1]
            battle_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{our_guild}_{enemy_guild}"
        else:
            # 文件名被截断或格式不正确，尝试从战斗数据推断对手
            print(f"警告: 文件名格式不正确或被截断: {csv_filename}")
            enemy_guild = infer_enemy_guild_from_data(battle_data)
            battle_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{our_guild}_{enemy_guild}"
    else:
        # 默认格式，尝试从战斗数据推断对手
        enemy_guild = infer_enemy_guild_from_data(battle_data)
        battle_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{our_guild}_{enemy_guild}"

    print(f"解析结果: 我方={our_guild}, 对手={enemy_guild}")

    # 统计参战信息
    total_members = len(member_performance)
    participated_members = len([p for p in member_performance.values() if p['battle_data']])
    new_members = len([p for p in member_performance.values() if not p['is_roster_member']])

    # 分析替补关系
    substitute_analysis = analyze_substitutes(member_performance)

    record = {
        'battle_id': battle_id,
        'filename': csv_filename,
        'our_guild': our_guild,
        'enemy_guild': enemy_guild,
        'upload_time': datetime.now().isoformat(),
        'total_members': total_members,
        'participated_members': participated_members,
        'new_members': new_members,
        'member_performance': member_performance,
        'battle_data': battle_data,
        'substitute_analysis': substitute_analysis
    }

    return record

def analyze_substitutes(member_performance):
    """分析替补关系"""
    try:
        # 获取缺席的排表成员
        absent_members = []
        substitutes = []

        for member_name, performance in member_performance.items():
            if performance.get('status') == '未参战' and performance.get('is_roster_member', False):
                absent_members.append(performance)
            elif performance.get('status') == '新成员/替补' and not performance.get('is_roster_member', True):
                substitutes.append(performance)

        substitute_relations = []

        # 尝试匹配替补关系（基于职业和团队类型）
        for substitute in substitutes:
            best_match = None
            substitute_name = substitute.get('name', '未知')
            substitute_profession = substitute.get('profession', '未知')
            substitute_team_type = substitute.get('team_type', '未知')

            for absent in absent_members:
                absent_name = absent.get('name', '未知')
                absent_profession = absent.get('profession', '未知')
                absent_team_type = absent.get('team_type', '未知')

                # 如果职业相同，很可能是替补关系
                if substitute_profession == absent_profession:
                    # 如果团队类型也相同，匹配度更高
                    if substitute_team_type == absent_team_type:
                        best_match = absent
                        break
                    elif best_match is None:
                        best_match = absent

            if best_match:
                substitute_relations.append({
                    'substitute': substitute_name,
                    'substitute_profession': substitute_profession,
                    'substitute_team_type': substitute_team_type,
                    'replaced': best_match.get('name', '未知'),
                    'replaced_profession': best_match.get('profession', '未知'),
                    'replaced_team': best_match.get('team', '未知'),
                    'confidence': 'high' if substitute_profession == best_match.get('profession') and substitute_team_type == best_match.get('team_type') else 'medium'
                })
                # 从缺席列表中移除已匹配的成员
                if best_match in absent_members:
                    absent_members.remove(best_match)

        return {
            'relations': substitute_relations,
            'unmatched_substitutes': [s.get('name', '未知') for s in substitutes if not any(r['substitute'] == s.get('name', '未知') for r in substitute_relations)],
            'unmatched_absent': [a.get('name', '未知') for a in absent_members]
        }

    except Exception as e:
        print(f"替补分析出错: {e}")
        return {
            'relations': [],
            'unmatched_substitutes': [],
            'unmatched_absent': []
        }

def parse_csv_battle_data(file_path):
    """解析CSV战斗数据文件"""
    try:
        battles = []
        current_guild = None

        # 尝试不同的编码
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
        lines = []

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    lines = f.readlines()
                print(f"成功使用编码 {encoding} 读取文件")
                break
            except UnicodeDecodeError:
                continue

        if not lines:
            print("无法读取CSV文件")
            return []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            parts = [part.strip().replace('"', '') for part in line.split(',')]

            # 检查帮会名称 - 动态识别任何帮会名称
            if len(parts) >= 1 and len(parts[0]) > 1 and not parts[0].isdigit() and '玩家名字' not in parts[0]:
                # 可能是帮会名（非数字，长度>1，不是表头）
                potential_guild = parts[0]
                # 检查是否是帮会名的几种情况：
                # 1. 只有一个字段
                # 2. 第二个字段是数字（可能是人数）
                # 3. 第一个字段不是常见的玩家名格式
                valid_fields = [p for p in parts if p.strip()]
                is_guild_line = (
                    len(valid_fields) == 1 or  # 只有帮会名
                    (len(valid_fields) == 2 and parts[1].isdigit()) or  # 帮会名+人数
                    (len(valid_fields) <= 3 and not any(char in potential_guild for char in ['丶', '灬', '·']))  # 不像玩家名
                )

                if is_guild_line:
                    current_guild = potential_guild
                    print(f"检测到帮会: {current_guild}")
                    continue

            # 检查表头
            if len(parts) >= 2 and parts[0] == '玩家名字':
                continue

            # 处理成员数据 - 处理所有帮会的数据
            if current_guild and len(parts) >= 12:
                try:
                    member_data = {
                        'guild': current_guild,
                        'name': parts[0],
                        'profession': parts[1],
                        'kills': int(parts[2]) if parts[2].isdigit() else 0,
                        'assists': int(parts[3]) if parts[3].isdigit() else 0,
                        'resources': int(parts[4]) if parts[4].isdigit() else 0,
                        'player_damage': int(parts[5]) if parts[5].isdigit() else 0,
                        'building_damage': int(parts[6]) if parts[6].isdigit() else 0,
                        'healing': int(parts[7]) if parts[7].isdigit() else 0,
                        'damage_taken': int(parts[8]) if parts[8].isdigit() else 0,
                        'heavy_injuries': int(parts[9]) if parts[9].isdigit() else 0,
                        'resurrections': int(parts[10]) if parts[10].isdigit() else 0,  # 化羽/清泉
                        'demolitions': int(parts[11]) if parts[11].isdigit() else 0,  # 焚骨（拆迁数据）
                        'qingquan': int(parts[10]) if parts[10].isdigit() else 0,  # 清泉数据（潮光专用，与resurrections相同）
                        'fenggu': int(parts[11]) if parts[11].isdigit() else 0  # 焚骨数据（与demolitions相同）
                    }
                    battles.append(member_data)
                except (ValueError, IndexError):
                    continue

        print(f"成功解析 {len(battles)} 条战斗数据")
        return battles

    except Exception as e:
        print(f"解析CSV文件失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def get_organization_structure(members):
    """获取三层级组织结构 - 支持自定义主分组"""
    # 动态初始化结构，支持自定义主分组
    structure = {}

    for member in members:
        # 解析成员的组织信息
        main_group = member.get('main_group', '其他团')  # 进攻团/防守团/其他团/自定义主分组
        sub_team = member.get('sub_team', '一团')        # 一团/二团/三团
        squad = member.get('squad', '1队')              # 1队/2队/3队/4队/5队

        # 动态初始化结构
        if main_group not in structure:
            structure[main_group] = {}
        if sub_team not in structure[main_group]:
            structure[main_group][sub_team] = {
                'count': 0,
                'squads': {},
                'members': [],
                'professions': {},
                'status': {'主力': 0, '替补': 0, '请假': 0}
            }
        if squad not in structure[main_group][sub_team]['squads']:
            structure[main_group][sub_team]['squads'][squad] = {
                'count': 0,
                'members': []
            }

        # 添加成员到结构中
        structure[main_group][sub_team]['count'] += 1
        structure[main_group][sub_team]['members'].append(member)
        structure[main_group][sub_team]['squads'][squad]['count'] += 1
        structure[main_group][sub_team]['squads'][squad]['members'].append(member)

        # 统计职业
        prof = member['profession']
        structure[main_group][sub_team]['professions'][prof] = \
            structure[main_group][sub_team]['professions'].get(prof, 0) + 1

        # 统计状态
        status = member.get('status', '主力')
        structure[main_group][sub_team]['status'][status] = \
            structure[main_group][sub_team]['status'].get(status, 0) + 1

    # 确保基础分组存在（即使没有成员）
    base_groups = ['进攻团', '防守团', '其他团']
    for group in base_groups:
        if group not in structure:
            structure[group] = {}

    return structure

def get_top_performers(battle_records, limit=5):
    """获取表现最佳的成员"""
    try:
        member_scores = {}

        for record in battle_records:
            member_performance = record.get('member_performance', {})
            for member_name, performance in member_performance.items():
                score = performance.get('score', 0)
                if score > 0:
                    if member_name not in member_scores:
                        member_scores[member_name] = []
                    member_scores[member_name].append(score)

        # 计算平均分并排序
        top_performers = []
        for member_name, scores in member_scores.items():
            avg_score = sum(scores) / len(scores)
            top_performers.append({
                'name': member_name,
                'avg_score': avg_score,
                'battle_count': len(scores)
            })

        # 按平均分排序，取前N名
        top_performers.sort(key=lambda x: x['avg_score'], reverse=True)
        return top_performers[:limit]

    except Exception as e:
        print(f"获取顶级表现者失败: {e}")
        return []

def generate_recent_activities(battle_records, members):
    """生成最近活动列表"""
    try:
        activities = []

        # 添加最近的战斗记录
        for record in battle_records[-3:]:  # 最近3场战斗
            upload_time = record.get('upload_time', '')
            if upload_time:
                # 格式化时间
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(upload_time.replace('T', ' ').split('.')[0])
                    time_str = dt.strftime('%m-%d %H:%M')
                except:
                    time_str = upload_time[:16].replace('T', ' ')

                activities.append({
                    'type': 'battle',
                    'title': f'战斗: {record["our_guild"]} VS {record["enemy_guild"]}',
                    'time': time_str
                })

        # 按时间倒序排列
        activities.reverse()

        return activities

    except Exception as e:
        print(f"生成最近活动失败: {e}")
        return []

def get_team_stats(members):
    """获取团队统计信息（兼容旧版本）"""
    # 为了兼容现有代码，保持原有的team字段统计
    teams = set()
    for member in members:
        team = member.get('team', '未分配')
        if team != '未分配':
            teams.add(team)

    # 初始化统计
    stats = {}
    for team in sorted(teams):
        stats[team] = {
            'count': 0,
            'professions': {},
            'squads': {},
            'status': {'主力': 0, '替补': 0, '请假': 0}
        }

    for member in members:
        team = member.get('team', '未分配')
        if team in stats:
            stats[team]['count'] += 1

            # 职业统计
            prof = member['profession']
            stats[team]['professions'][prof] = stats[team]['professions'].get(prof, 0) + 1

            # 小队统计
            squad = member.get('squad', '未分配小队')
            stats[team]['squads'][squad] = stats[team]['squads'].get(squad, 0) + 1

            # 状态统计
            status = member.get('status', '主力')
            stats[team]['status'][status] = stats[team]['status'].get(status, 0) + 1

    return stats

def get_team_type_from_member(member):
    """从成员的组织架构信息获取团队类型"""
    # 使用正确的三层组织架构
    main_group = member.get('main_group', '其他团')  # 进攻团/防守团/其他团
    return main_group

def load_battle_data():
    """加载战斗数据"""
    try:
        # 查找最新的战斗数据文件
        data_dir = 'data'
        battle_files = []

        if os.path.exists(data_dir):
            for filename in os.listdir(data_dir):
                if filename.endswith('.csv') and '纸落云烟' in filename:
                    battle_files.append(os.path.join(data_dir, filename))

        if not battle_files:
            print("没有找到战斗数据文件")
            return []

        # 使用最新的文件（按文件名排序，最新的在最后）
        battle_file = sorted(battle_files)[-1]
        print(f"加载战斗数据文件: {battle_file}")

        if not os.path.exists(battle_file):
            print(f"战斗数据文件不存在: {battle_file}")
            return []

        battles = []
        current_guild = None

        # 尝试不同的编码
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
        lines = []

        for encoding in encodings:
            try:
                with open(battle_file, 'r', encoding=encoding) as f:
                    lines = f.readlines()
                print(f"成功使用编码 {encoding} 读取文件")
                break
            except UnicodeDecodeError:
                continue

        if not lines:
            print("无法读取CSV文件")
            return []

        for line in lines:  # 处理所有行，获取完整数据
            line = line.strip()
            if not line:
                continue

            parts = [part.strip().replace('"', '') for part in line.split(',')]

            # 检查帮会名称（更灵活的检测）
            if len(parts) >= 1 and len(parts[0]) > 1 and not parts[0].isdigit() and '玩家名字' not in parts[0]:
                # 可能是帮会名（非数字，长度>1，不是表头）
                potential_guild = parts[0]
                # 检查是否是帮会名的几种情况：
                # 1. 只有一个字段
                # 2. 第二个字段是数字（可能是人数）
                # 3. 第一个字段不是常见的玩家名格式
                valid_fields = [p for p in parts if p.strip()]
                is_guild_line = (
                    len(valid_fields) == 1 or  # 只有帮会名
                    (len(valid_fields) == 2 and parts[1].isdigit()) or  # 帮会名+人数
                    (len(valid_fields) <= 3 and not any(char in potential_guild for char in ['丶', '灬', '·']))  # 不像玩家名
                )

                if is_guild_line:
                    current_guild = potential_guild
                    print(f"检测到帮会: {current_guild}")
                    continue

            # 检查表头
            if len(parts) >= 2 and parts[0] == '玩家名字':
                continue

            # 处理成员数据（处理所有帮会的数据）
            if current_guild and len(parts) >= 12:
                try:
                    member_data = {
                        'guild': current_guild,
                        'name': parts[0],
                        'profession': parts[1],
                        'kills': int(parts[2]) if parts[2].isdigit() else 0,
                        'assists': int(parts[3]) if parts[3].isdigit() else 0,
                        'resources': int(parts[4]) if parts[4].isdigit() else 0,
                        'player_damage': int(parts[5]) if parts[5].isdigit() else 0,
                        'building_damage': int(parts[6]) if parts[6].isdigit() else 0,
                        'healing': int(parts[7]) if parts[7].isdigit() else 0,
                        'damage_taken': int(parts[8]) if parts[8].isdigit() else 0,
                        'heavy_injuries': int(parts[9]) if parts[9].isdigit() else 0,
                        'resurrections': int(parts[10]) if parts[10].isdigit() else 0,  # 化羽/清泉
                        'demolitions': int(parts[11]) if parts[11].isdigit() else 0,  # 焚骨（拆迁数据）
                        'qingquan': int(parts[10]) if parts[10].isdigit() else 0,  # 清泉数据（潮光专用，与resurrections相同）
                        'fenggu': int(parts[11]) if parts[11].isdigit() else 0  # 焚骨数据（与demolitions相同）
                    }
                    battles.append(member_data)
                except (ValueError, IndexError):
                    continue

        print(f"成功加载 {len(battles)} 条战斗数据")
        return battles

    except Exception as e:
        print(f"加载战斗数据失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def analyze_member_performance(battle_data, members):
    """分析成员战斗表现 - 新版全面分析"""
    performance = {}

    try:
        # 获取当前用户帮会的战斗数据
        user = get_current_user()
        current_guild_name = None
        if user and user.get('guild_id'):
            guilds = load_guilds()
            guild_info = guilds.get(user['guild_id'])
            if guild_info:
                current_guild_name = guild_info['name']

        # 如果没有找到当前帮会名称，尝试从战斗数据中推断
        if not current_guild_name and battle_data:
            # 获取所有帮会名称，选择数据最多的作为当前帮会
            guild_counts = {}
            for b in battle_data:
                guild = b.get('guild', '')
                if guild:
                    guild_counts[guild] = guild_counts.get(guild, 0) + 1
            if guild_counts:
                current_guild_name = max(guild_counts.items(), key=lambda x: x[1])[0]

        if not current_guild_name:
            current_guild_name = '未知帮会'

        guild_data = [b for b in battle_data if b.get('guild') == current_guild_name]
        print(f"{current_guild_name}战斗数据: {len(guild_data)} 条")

        # 计算战斗平均值
        battle_averages = calculate_battle_averages(guild_data, members)
        print(f"📊 计算战斗平均值完成")

        # 创建成员名单字典，便于查找
        member_dict = {m.get('name', ''): m for m in members if m.get('name')}

        # 获取所有战斗数据中的成员名单
        battle_members = {b.get('name', '') for b in guild_data if b.get('name')}
        roster_members = {m.get('name', '') for m in members if m.get('name')}

        # 分析参战情况
        participated_members = battle_members & roster_members  # 排表中参战的
        new_members = battle_members - roster_members  # 战斗数据中有但排表中没有的（新成员/替补）
        absent_members = roster_members - battle_members  # 排表中有但战斗数据中没有的（未参战）

        print(f"📊 参战分析:")
        print(f"  排表成员参战: {len(participated_members)} 人")
        print(f"  新成员/替补: {len(new_members)} 人")
        print(f"  未参战成员: {len(absent_members)} 人")

        if new_members:
            print(f"  新成员/替补名单: {list(new_members)}")

        # 1. 处理排表中的成员（包括参战和未参战）
        for member in members:
            member_name = member.get('name', '')
            if not member_name:
                continue

            # 检查是否是"其他团"成员（替补/请假）
            main_group = member.get('main_group', '其他团')
            is_substitute = (main_group == '其他团')

            member_battle = None

            # 查找战斗数据
            for b in guild_data:
                if b.get('name') == member_name:
                    member_battle = b
                    break

            # 模糊匹配（处理特殊字符）
            if not member_battle:
                clean_member_name = member_name.replace('丶', '').replace('灬', '').replace('·', '')
                for b in guild_data:
                    clean_battle_name = b.get('name', '').replace('丶', '').replace('灬', '').replace('·', '')
                    if clean_battle_name == clean_member_name:
                        member_battle = b
                        print(f"模糊匹配成功: {member_name} -> {b.get('name')}")
                        break

            if member_battle:
                # 参战成员
                team_type = get_team_type_from_member(member)
                profession = member.get('profession', '未知')
                score_result = calculate_member_score(member_battle, profession, team_type, member, battle_averages)

                # 如果是"其他团"成员但参战了，标记为替补上场
                status = '新成员/替补' if is_substitute else '排表参战'

                performance[member_name] = {
                    'name': member_name,
                    'battle_data': member_battle,
                    'score': score_result['score'],
                    'rating': get_performance_rating(score_result['score']),
                    'profession': profession,
                    'team': f"{member.get('main_group', '未知')}-{member.get('sub_team', '未知')}",  # 使用组织架构
                    'team_type': team_type,
                    'squad': member.get('squad', '未分配小队'),
                    'status': status,
                    'is_roster_member': not is_substitute,  # 其他团成员不算正式排表成员
                    'position': member.get('position', '拆塔'),
                    'details': score_result['details'],
                    'bonus_items': score_result['bonus_items'],
                    'penalty_items': score_result['penalty_items']
                }
            else:
                # 未参战成员
                team_type = get_team_type_from_member(member)

                # 如果是"其他团"成员未参战，标记为替补缺席
                status = '替补缺席' if is_substitute else '未参战'

                performance[member_name] = {
                    'name': member_name,
                    'battle_data': None,
                    'score': 0,
                    'rating': '未参战',
                    'profession': member.get('profession', '未知'),
                    'team': f"{member.get('main_group', '未知')}-{member.get('sub_team', '未知')}",  # 使用组织架构
                    'team_type': team_type,
                    'squad': member.get('squad', '未分配小队'),
                    'status': status,
                    'is_roster_member': not is_substitute  # 其他团成员不算正式排表成员
                }

        # 2. 处理新成员/替补（战斗数据中有但排表中没有的）
        for battle_member in guild_data:
            member_name = battle_member.get('name', '')
            if not member_name or member_name in member_dict:
                continue

            # 这是新成员或替补
            # 尝试推断团队类型（基于职业和数据表现）
            profession = battle_member.get('profession', '未知')
            team_type = infer_team_type_from_battle_data(battle_member, profession)

            # 为新成员创建临时member_info
            temp_member_info = {
                'position': '拆塔',  # 默认位置
                'sub_team': '推断团队'
            }

            score_result = calculate_member_score(battle_member, profession, team_type, temp_member_info, battle_averages)

            performance[member_name] = {
                'name': member_name,
                'battle_data': battle_member,
                'score': score_result['score'],
                'rating': get_performance_rating(score_result['score']),
                'profession': profession,
                'team': f'推断-{team_type}',
                'team_type': team_type,
                'squad': '未分配小队',
                'status': '新成员/替补',
                'is_roster_member': False,
                'position': '拆塔',
                'details': score_result['details'],
                'bonus_items': score_result['bonus_items'],
                'penalty_items': score_result['penalty_items']
            }

        print(f"✅ 分析完成: 总计 {len(performance)} 人")
        print(f"  排表成员: {len([p for p in performance.values() if p.get('is_roster_member', False)])} 人")
        print(f"  新成员/替补: {len([p for p in performance.values() if not p.get('is_roster_member', True)])} 人")

        # 🚫 过滤掉帮外成员，只保留进攻团、防守团和替补成员
        filtered_performance = {}
        excluded_count = 0

        for member_name, data in performance.items():
            team = data.get('team', '')
            sub_team = data.get('team_type', '')

            # 排除帮外成员（team包含"帮外"或sub_team为"帮外"）
            if '帮外' in team or sub_team == '帮外':
                excluded_count += 1
                print(f"  ❌ 排除帮外成员: {member_name} (团队: {team})")
            else:
                filtered_performance[member_name] = data

        print(f"🔍 帮外成员过滤结果:")
        print(f"  保留成员: {len(filtered_performance)} 人")
        print(f"  排除帮外: {excluded_count} 人")

        return filtered_performance

    except Exception as e:
        print(f"成员表现分析出错: {e}")
        import traceback
        traceback.print_exc()
        return {}

def auto_add_new_members(battle_data, existing_members):
    """自动添加战斗数据中的新成员到成员数据库"""
    try:
        # 获取当前用户的帮会信息
        user = get_current_user()
        current_guild_name = None
        if user and user.get('guild_id'):
            guilds = load_guilds()
            guild_info = guilds.get(user['guild_id'])
            if guild_info:
                current_guild_name = guild_info['name']

        # 如果没有找到当前帮会名称，尝试从战斗数据中推断
        if not current_guild_name and battle_data:
            guild_counts = {}
            for b in battle_data:
                guild = b.get('guild', '')
                if guild:
                    guild_counts[guild] = guild_counts.get(guild, 0) + 1
            if guild_counts:
                current_guild_name = max(guild_counts.items(), key=lambda x: x[1])[0]

        if not current_guild_name:
            print("⚠️ 无法确定当前帮会，跳过自动添加新成员")
            return []

        # 获取当前帮会的战斗数据
        guild_battle_data = [b for b in battle_data if b.get('guild') == current_guild_name]

        # 创建现有成员名单字典
        existing_member_names = {m.get('name', '') for m in existing_members if m.get('name')}

        # 找出新成员（战斗数据中有但成员列表中没有的）
        new_members_to_add = []
        for battle_member in guild_battle_data:
            member_name = battle_member.get('name', '')
            if not member_name or member_name in existing_member_names:
                continue

            # 这是新成员，需要添加
            profession = battle_member.get('profession', '未知')
            team_type = infer_team_type_from_battle_data(battle_member, profession)

            # 根据职业推断默认位置
            if profession in ['素问']:
                default_position = '治疗'
            elif profession in ['铁衣']:
                default_position = '扛伤'
            elif profession in ['碎梦']:
                default_position = '击杀'
            elif profession in ['神相', '玄机']:
                default_position = '拆塔'
            elif profession in ['潮光']:
                default_position = '辅助'
            else:
                default_position = '人伤'

            new_member = {
                'name': member_name,
                'profession': profession,
                'position': default_position,
                'main_group': '其他团',  # 新成员默认放入其他团
                'sub_team': '替补',      # 默认为替补
                'squad': '替补',        # 默认为替补
                'status': '替补'        # 默认状态为替补
            }

            new_members_to_add.append(new_member)
            print(f"🆕 发现新成员: {member_name} ({profession}) - 推断团队类型: {team_type}")

        # 如果有新成员，添加到数据库
        if new_members_to_add:
            # 添加到现有成员列表
            updated_members = existing_members + new_members_to_add

            # 保存到数据库
            save_members(updated_members)

            print(f"✅ 成功自动添加 {len(new_members_to_add)} 个新成员到数据库")
            return new_members_to_add
        else:
            print("ℹ️ 没有发现需要添加的新成员")
            return []

    except Exception as e:
        print(f"❌ 自动添加新成员失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def infer_team_type_from_battle_data(battle_data, profession):
    """根据战斗数据推断团队类型"""
    # 基于数据表现推断是进攻团还是防守团
    # 注意：building_damage 才是真正的拆迁数据（塔伤），demolitions 是焚骨
    building_damage = battle_data.get('building_damage', 0)  # 真正的拆迁数据
    kills = battle_data.get('kills', 0)
    player_damage = battle_data.get('player_damage', 0)

    # 计算拆迁倾向和击杀倾向
    demolition_score = building_damage / 1000000  # 建筑伤害才是拆迁
    kill_score = kills * 5 + player_damage / 1000000

    # 特殊职业的推断逻辑
    if profession == '素问':
        # 素问主要看复活和治疗，难以从数据推断团队类型，默认进攻团
        return '进攻团'
    elif profession == '铁衣':
        # 铁衣主要看承伤，难以推断，默认进攻团
        return '进攻团'
    elif profession == '潮光':
        # 潮光看清泉数据，如果清泉多可能是进攻团
        qingquan = battle_data.get('qingquan', 0)
        if qingquan > 0:
            return '进攻团'
        elif kill_score > demolition_score:
            return '防守团'
        else:
            return '进攻团'
    else:
        # 其他职业根据拆迁vs击杀倾向判断
        if demolition_score > kill_score * 1.2:  # 拆迁明显更多
            return '进攻团'
        elif kill_score > demolition_score * 1.2:  # 击杀明显更多
            return '防守团'
        else:
            # 数据相近，默认进攻团（因为进攻团人数更多）
            return '进攻团'

def calculate_battle_averages(all_battle_data, members):
    """计算战斗数据的平均值，用于相对评分 - 按团队类型和职责双层分组计算专项平均值"""
    if not all_battle_data:
        return {}

    # 创建成员名单字典，便于查找职责和团队类型
    member_dict = {m.get('name', ''): m for m in members if m.get('name')}

    # 按团队类型和职责双层分组战斗数据
    team_responsibility_data = {
        '进攻团': {
            '拆塔': [],
            '击杀': [],
            '人伤': [],
            '治疗': [],
            '扛伤': [],
            '辅助': []
        },
        '防守团': {
            '拆塔': [],
            '击杀': [],
            '人伤': [],
            '治疗': [],
            '扛伤': [],
            '辅助': []
        },
        '其他团': {
            '拆塔': [],
            '击杀': [],
            '人伤': [],
            '治疗': [],
            '扛伤': [],
            '辅助': []
        }
    }

    # 分组战斗数据 - 🚫 只排除帮外成员，包含所有真实参战人员
    core_battle_data = []  # 用于计算overall平均值的核心数据（排除帮外）
    excluded_external_count = 0

    for battle_member in all_battle_data:
        member_name = battle_member.get('name', '')
        member_info = member_dict.get(member_name)

        if member_info:
            position = member_info.get('position', '拆塔')
            profession = member_info.get('profession', '未知')
            team_type = get_team_type_from_member(member_info)
            responsibility = get_position_mapping(position, profession)

            # 🚫 只排除帮外成员，包含进攻团、防守团、替补等所有真实参战人员
            if team_type != '帮外':  # 只要不是帮外就包含
                core_battle_data.append(battle_member)
            else:
                excluded_external_count += 1
                print(f"  ❌ 排除帮外成员(平均值计算): {member_name}")
        else:
            # 新成员/替补，根据职业推断职责和团队类型
            profession = battle_member.get('profession', '未知')
            responsibility = get_position_mapping('拆塔', profession)  # 默认位置
            team_type = infer_team_type_from_battle_data(battle_member, profession)

            # 🚫 新成员/替补也只排除推断为帮外的
            if team_type != '帮外':  # 只要不是帮外就包含
                core_battle_data.append(battle_member)
            else:
                excluded_external_count += 1
                print(f"  ❌ 排除帮外成员(平均值计算): {member_name} (推断)")

        # 确保团队类型有效
        if team_type not in team_responsibility_data:
            team_type = '其他团'

        if responsibility in team_responsibility_data[team_type]:
            team_responsibility_data[team_type][responsibility].append(battle_member)

    print(f"🔍 平均值计算数据筛选:")
    print(f"  总战斗数据: {len(all_battle_data)} 人")
    print(f"  包含数据(所有真实参战): {len(core_battle_data)} 人")
    print(f"  排除数据(仅帮外): {excluded_external_count} 人")

    # 计算各项指标的平均值
    averages = {
        'overall': {},
        'by_team_responsibility': {}  # 新的双层结构
    }

    # 🆕 全体平均值（基于所有真实参战人员，排除帮外成员）
    if core_battle_data:
        total_count = len(core_battle_data)
        averages['overall'] = {
            'healing': sum(b.get('healing', 0) for b in core_battle_data) / total_count,
            'damage_taken': sum(b.get('damage_taken', 0) for b in core_battle_data) / total_count,
            'heavy_injuries': sum(b.get('heavy_injuries', 0) for b in core_battle_data) / total_count,
            'resurrections': sum(b.get('resurrections', 0) for b in core_battle_data) / total_count,
            'resources': sum(b.get('resources', 0) for b in core_battle_data) / total_count,
            'assists': sum(b.get('assists', 0) for b in core_battle_data) / total_count
        }
        print(f"📊 全体平均值计算基于: {total_count} 人 (所有真实参战，排除帮外)")
    else:
        # 如果没有核心团队数据，使用默认值
        averages['overall'] = {
            'healing': 0, 'damage_taken': 0, 'heavy_injuries': 1,
            'resurrections': 0, 'resources': 0, 'assists': 0
        }
        print("⚠️ 警告: 没有真实参战数据，使用默认全体平均值")

    # 🆕 潮光专项清泉平均值（基于所有真实参战的潮光，排除帮外）
    chaoguan_members = [b for b in core_battle_data if b.get('profession') == '潮光']
    if chaoguan_members:
        averages['chaoguan_springs'] = sum(b.get('qingquan', 0) for b in chaoguan_members) / len(chaoguan_members)
        print(f"📊 潮光清泉专项平均值: {averages['chaoguan_springs']:.1f} (样本数:{len(chaoguan_members)}, 所有真实参战)")
    else:
        averages['chaoguan_springs'] = 0
        print("📊 潮光清泉专项平均值: 0 (无真实参战潮光数据)")

    # 按团队类型和职责计算双层专项平均值
    print("\n🆕 计算新的团队专项平均值:")
    for team_type, responsibility_data in team_responsibility_data.items():
        averages['by_team_responsibility'][team_type] = {}

        for responsibility, members_data in responsibility_data.items():
            if len(members_data) > 0:
                count = len(members_data)
                averages['by_team_responsibility'][team_type][responsibility] = {
                    'kills': sum(b.get('kills', 0) for b in members_data) / count,
                    'building_damage': sum(b.get('building_damage', 0) for b in members_data) / count,
                    'player_damage': sum(b.get('player_damage', 0) for b in members_data) / count,
                    'healing': sum(b.get('healing', 0) for b in members_data) / count,
                    'damage_taken': sum(b.get('damage_taken', 0) for b in members_data) / count,
                    'resurrections': sum(b.get('resurrections', 0) for b in members_data) / count,
                    'springs': sum(b.get('qingquan', 0) for b in members_data) / count,
                    'count': count
                }

                # 显示团队-职责专项平均值
                team_resp_avg = averages['by_team_responsibility'][team_type][responsibility]
                if responsibility == '拆塔':
                    print(f"🆕 {team_type}-{responsibility}平均值: 建筑伤害={team_resp_avg['building_damage']:.0f}, 击杀={team_resp_avg['kills']:.1f} (样本数:{count})")
                elif responsibility == '击杀':
                    print(f"🆕 {team_type}-{responsibility}平均值: 击杀={team_resp_avg['kills']:.1f}, 玩家伤害={team_resp_avg['player_damage']:.0f} (样本数:{count})")
                elif responsibility == '人伤':
                    print(f"🆕 {team_type}-{responsibility}平均值: 玩家伤害={team_resp_avg['player_damage']:.0f}, 击杀={team_resp_avg['kills']:.1f} (样本数:{count})")
                elif responsibility == '扛伤':
                    print(f"🆕 {team_type}-{responsibility}平均值: 承受伤害={team_resp_avg['damage_taken']:.0f} (样本数:{count})")
                elif responsibility == '治疗':
                    print(f"🆕 {team_type}-{responsibility}平均值: 治疗量={team_resp_avg['healing']:.0f}, 羽化={team_resp_avg['resurrections']:.1f} (样本数:{count})")
                elif responsibility == '辅助':
                    print(f"🆕 {team_type}-{responsibility}平均值: 清泉={team_resp_avg['springs']:.1f}, 羽化={team_resp_avg['resurrections']:.1f} (样本数:{count})")

    # ❌ 禁用旧的by_responsibility结构计算，强制使用新的团队专项平均值
    print("\n❌ 旧的职责专项平均值已禁用，强制使用新的团队专项平均值")
    averages['by_responsibility'] = {}  # 保留空结构避免报错

    return averages

def get_position_mapping(position, profession):
    """将现有位置映射到新的职责分类"""
    # 新的职责分类直接返回
    if position in ['拆塔', '击杀', '人伤', '治疗', '扛伤']:
        return position

    # 兼容旧的位置分类
    elif position == '坦克':
        return '扛伤'
    elif position == '输出':
        # 输出需要根据职业细分
        if profession == '碎梦':
            return '击杀'  # 碎梦主要负责击杀
        else:
            return '人伤'  # 其他输出职业主要负责人伤
    elif position in ['辅助', '控制']:
        # 旧的辅助/控制根据职业推断
        if profession in ['素问']:
            return '治疗'
        elif profession in ['铁衣']:
            return '扛伤'
        elif profession in ['碎梦']:
            return '击杀'
        elif profession in ['神相', '玄机']:
            return '拆塔'
        elif profession in ['潮光']:
            return '辅助'  # 潮光辅助位置映射为辅助职责
        else:
            return '人伤'
    else:
        # 默认根据职业推断
        if profession in ['素问']:
            return '治疗'
        elif profession in ['铁衣']:
            return '扛伤'
        elif profession in ['碎梦']:
            return '击杀'
        elif profession in ['神相', '玄机']:
            return '拆塔'
        else:
            return '人伤'

def calculate_member_score(battle_data, profession, team_type, member_info=None, battle_averages=None):
    """基于职责的相对评分系统 - 带详细加分扣分记录"""

    if not battle_averages:
        return {'score': 50, 'details': [], 'bonus_items': [], 'penalty_items': []}

    # 基础分数
    score = 50
    details = []
    bonus_items = []  # 加分项
    penalty_items = []  # 扣分项

    # 获取成员职责
    position = member_info.get('position', '拆塔') if member_info else '拆塔'
    responsibility = get_position_mapping(position, profession)

    # 获取平均值数据
    overall_avg = battle_averages.get('overall', {})
    member_name = battle_data.get('name', '未知')

    print(f"评分 {member_name}: 职责={responsibility}, 职业={profession}, 位置={position}")

    # 特殊处理：辅助职责（潮光和素问）
    if responsibility == '辅助':
        details.append(f"🤝 辅助职责评分 (职业: {profession})")

        # 🆕 强制使用团队-辅助职责专项平均值（禁用旧的兼容逻辑）
        team_responsibility_avg = battle_averages.get('by_team_responsibility', {}).get(team_type, {}).get('辅助', {})

        print(f"🆕 {team_type}-辅助职责评分，强制使用团队专项平均值")

        # 重伤评分：重伤多扣分，重伤少加分（使用全体平均值，重伤是通用指标）
        heavy_avg = overall_avg.get('heavy_injuries', 1)
        if heavy_avg > 0:
            heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
            if heavy_ratio > 1:
                heavy_penalty = (heavy_ratio - 1) * 8
                score -= heavy_penalty
                penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
            elif heavy_ratio < 0.8:  # 重伤少的给加分
                heavy_bonus = (0.8 - heavy_ratio) * 6
                score += heavy_bonus
                bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

        # 潮光：辅助职责评分（根据团队类型区分考核项目）
        if profession == '潮光':
            details.append(f"🌊 潮光辅助职责评分 (清泉+团队特定考核)")

            # 必要考核：清泉数据（所有辅助潮光都要考核）
            springs_avg = battle_averages.get('chaoguan_springs', 1)
            qingquan_value = battle_data.get('qingquan', 0)
            print(f"  潮光辅助评分: 清泉值={qingquan_value}, 潮光专项平均值={springs_avg}")

            if springs_avg > 1:  # 避免除以1的问题
                springs_ratio = qingquan_value / springs_avg
                springs_score = (springs_ratio - 1) * 25  # 主要考核指标
                score += springs_score
                if springs_score > 0:
                    bonus_items.append(f"清泉数据优秀: +{springs_score:.1f}分 ({qingquan_value}/{springs_avg:.1f})")
                else:
                    penalty_items.append(f"清泉数据不足: {springs_score:.1f}分 ({qingquan_value}/{springs_avg:.1f})")
            else:
                print(f"  潮光辅助警告: 清泉平均值无效 springs_avg={springs_avg}")

            # 根据团队类型确定第二考核项目
            if team_type == '进攻团':
                # 进攻团辅助潮光：考核建筑伤害（使用进攻团-拆塔专项平均值）
                attack_demolition_avg = battle_averages.get('by_team_responsibility', {}).get('进攻团', {}).get('拆塔', {})
                building_avg = attack_demolition_avg.get('building_damage', 1)
                building_value = battle_data.get('building_damage', 0)
                # 获取旧的平均值进行对比
                old_building_avg = battle_averages.get('by_responsibility', {}).get('拆塔', {}).get('building_damage', 1)

                print(f"  🆕 进攻团潮光辅助建筑伤害评分: 建筑伤害={building_value}")
                print(f"  📊 新平均值(进攻团-拆塔): {building_avg:.0f}")
                print(f"  📊 旧平均值(全体-拆塔): {old_building_avg:.0f}")
                print(f"  📊 平均值差异: {building_avg - old_building_avg:.0f} ({'新>旧' if building_avg > old_building_avg else '新<旧' if building_avg < old_building_avg else '相等'})")
                print(f"  ✅ 使用新的团队专项平均值: by_team_responsibility['进攻团']['拆塔']['building_damage']")

                if building_avg > 1000:  # 确保平均值合理
                    building_ratio = building_value / building_avg
                    building_score = (building_ratio - 1) * 20  # 主要考核项目权重高
                    score += building_score
                    print(f"  进攻团潮光辅助建筑伤害计算: 比率={building_ratio:.2f}, 得分={building_score:.1f}")
                    if building_score > 0:
                        bonus_items.append(f"建筑伤害优秀: +{building_score:.1f}分 ({building_value:.0f}/{building_avg:.0f})")
                    else:
                        penalty_items.append(f"建筑伤害不足: {building_score:.1f}分 ({building_value:.0f}/{building_avg:.0f})")
                else:
                    print(f"  进攻团潮光辅助警告: 进攻团-拆塔专项平均值过小 {building_avg}, 跳过评分")

            elif team_type == '防守团':
                # 防守团辅助潮光：考核玩家伤害（使用防守团-人伤专项平均值）
                defense_player_dmg_avg = battle_averages.get('by_team_responsibility', {}).get('防守团', {}).get('人伤', {})
                player_dmg_avg = defense_player_dmg_avg.get('player_damage', 1)
                player_dmg_value = battle_data.get('player_damage', 0)
                # 获取旧的平均值进行对比
                old_player_dmg_avg = battle_averages.get('by_responsibility', {}).get('人伤', {}).get('player_damage', 1)

                print(f"  🆕 防守团潮光辅助人伤评分: 玩家伤害={player_dmg_value}")
                print(f"  📊 新平均值(防守团-人伤): {player_dmg_avg:.0f}")
                print(f"  📊 旧平均值(全体-人伤): {old_player_dmg_avg:.0f}")
                print(f"  📊 平均值差异: {player_dmg_avg - old_player_dmg_avg:.0f} ({'新>旧' if player_dmg_avg > old_player_dmg_avg else '新<旧' if player_dmg_avg < old_player_dmg_avg else '相等'})")
                print(f"  ✅ 使用新的团队专项平均值: by_team_responsibility['防守团']['人伤']['player_damage']")

                if player_dmg_avg > 1000:  # 确保平均值合理
                    player_dmg_ratio = player_dmg_value / player_dmg_avg
                    player_dmg_score = (player_dmg_ratio - 1) * 20  # 主要考核项目权重高
                    score += player_dmg_score
                    print(f"  防守团潮光辅助人伤计算: 比率={player_dmg_ratio:.2f}, 得分={player_dmg_score:.1f}")
                    if player_dmg_score > 0:
                        bonus_items.append(f"玩家伤害优秀: +{player_dmg_score:.1f}分 ({player_dmg_value:.0f}/{player_dmg_avg:.0f})")
                    else:
                        penalty_items.append(f"玩家伤害不足: {player_dmg_score:.1f}分 ({player_dmg_value:.0f}/{player_dmg_avg:.0f})")
                else:
                    print(f"  防守团潮光辅助警告: 防守团-人伤专项平均值过小 {player_dmg_avg}, 跳过评分")
            else:
                # 其他团或未知团队：默认考核建筑伤害（使用进攻团-拆塔平均值）
                print(f"  潮光辅助团队类型未知({team_type})，默认考核建筑伤害")
                attack_demolition_avg = battle_averages.get('by_team_responsibility', {}).get('进攻团', {}).get('拆塔', {})
                building_avg = attack_demolition_avg.get('building_damage', 1)
                building_value = battle_data.get('building_damage', 0)

                if building_avg > 1000:
                    building_ratio = building_value / building_avg
                    building_score = (building_ratio - 1) * 20
                    score += building_score
                    if building_score > 0:
                        bonus_items.append(f"建筑伤害优秀: +{building_score:.1f}分 ({building_value:.0f}/{building_avg:.0f})")
                    else:
                        penalty_items.append(f"建筑伤害不足: {building_score:.1f}分 ({building_value:.0f}/{building_avg:.0f})")

        # 素问：只考核羽化和重伤
        elif profession == '素问':
            details.append(f"🌸 素问职责评分 (羽化+重伤)")

            # 主要考核：羽化数据（使用团队-辅助职责专项平均值）
            resurrections_avg = team_responsibility_avg.get('resurrections', 1) if team_responsibility_avg else 1
            if resurrections_avg > 1:  # 避免除以1的问题
                resurrections_ratio = battle_data.get('resurrections', 0) / resurrections_avg
                resurrections_score = (resurrections_ratio - 1) * 30  # 提高权重，作为主要指标
                score += resurrections_score
                if resurrections_score > 0:
                    bonus_items.append(f"羽化数据优秀: +{resurrections_score:.1f}分 ({battle_data.get('resurrections', 0)}/{resurrections_avg:.1f})")
                else:
                    penalty_items.append(f"羽化数据不足: {resurrections_score:.1f}分 ({battle_data.get('resurrections', 0)}/{resurrections_avg:.1f})")

            # 主要考核：重伤评分（素问死太多次很严重，死得少要加分）
            heavy_avg = overall_avg.get('heavy_injuries', 1)
            if heavy_avg > 0:
                heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
                if heavy_ratio > 1:
                    heavy_penalty = (heavy_ratio - 1) * 15  # 素问重伤扣分更重
                    score -= heavy_penalty
                    penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
                elif heavy_ratio < 0.8:  # 重伤少的给加分
                    heavy_bonus = (0.8 - heavy_ratio) * 10  # 素问重伤少加分也更多
                    score += heavy_bonus
                    bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

            # 次要加分：治疗量（素问也有一定治疗能力）- 使用团队专项平均值
            healing_avg = team_responsibility_avg.get('healing', 1) if team_responsibility_avg else 1
            print(f"🆕 辅助素问治疗量评分: 使用团队专项平均值({team_type}-辅助)={healing_avg:.0f}")
            if healing_avg > 1000:
                healing_ratio = battle_data.get('healing', 0) / healing_avg
                if healing_ratio > 1:  # 只有超过平均才加分
                    healing_score = (healing_ratio - 1) * 0.8  # 缩小10倍：8 → 0.8
                    score += healing_score
                    bonus_items.append(f"治疗量良好: +{healing_score:.1f}分 ({battle_data.get('healing', 0):.0f}/{healing_avg:.0f})")

            # 不考虑其他因素（生存能力等）

        # 其他职业的辅助职责
        else:
            # 通用辅助评分 - 使用团队专项平均值
            springs_avg = team_responsibility_avg.get('springs', 1) if team_responsibility_avg else 1
            print(f"🆕 通用辅助清泉评分: 使用团队专项平均值({team_type}-辅助)={springs_avg:.1f}")
            if springs_avg > 0:
                springs_ratio = battle_data.get('qingquan', 0) / springs_avg
                springs_score = (springs_ratio - 1) * 15
                score += springs_score
                if springs_score > 0:
                    bonus_items.append(f"清泉数据优秀: +{springs_score:.1f}分 ({battle_data.get('qingquan', 0)}/{springs_avg:.1f})")
                else:
                    penalty_items.append(f"清泉数据不足: {springs_score:.1f}分 ({battle_data.get('qingquan', 0)}/{springs_avg:.1f})")

    # 基于职责的评分
    elif responsibility == '拆塔':
        details.append(f"🏗️ 拆塔职责评分")

        # 🆕 强制使用团队-拆塔职责专项平均值（禁用旧的兼容逻辑）
        team_responsibility_avg = battle_averages.get('by_team_responsibility', {}).get(team_type, {}).get('拆塔', {})
        # ❌ 禁用旧的职责平均值
        # responsibility_avg = battle_averages.get('by_responsibility', {}).get('拆塔', {})

        print(f"🆕 {team_type}-拆塔职责评分，优先使用团队专项平均值")

        # 拆塔职责的潮光不考核清泉，只考核拆塔能力
        if profession == '潮光':
            details.append(f"🌊 潮光拆塔职责评分 (不考核清泉)")

        # 🆕 主要职责：建筑伤害（强制使用团队-拆塔职责专项平均值）
        building_avg = team_responsibility_avg.get('building_damage', 1) if team_responsibility_avg else 1

        print(f"🆕 建筑伤害评分: 强制使用新平均值({team_type}-拆塔)={building_avg:.0f}")
        if not team_responsibility_avg:
            print(f"⚠️ 警告: {team_type}-拆塔专项平均值不存在，使用默认值1")

        if building_avg > 1000:  # 确保平均值合理
            building_ratio = battle_data.get('building_damage', 0) / building_avg
            building_score = (building_ratio - 1) * 20
            score += building_score
            if building_score > 0:
                bonus_items.append(f"建筑伤害优秀: +{building_score:.1f}分 ({battle_data.get('building_damage', 0):.0f}/{building_avg:.0f})")
            else:
                penalty_items.append(f"建筑伤害不足: {building_score:.1f}分 ({battle_data.get('building_damage', 0):.0f}/{building_avg:.0f})")
        else:
            print(f"  拆塔职责警告: 建筑伤害平均值过小 {building_avg}, 跳过评分")

        # 次要加分：击杀（使用团队-拆塔职责专项平均值，只加分不减分）
        kills_avg = team_responsibility_avg.get('kills', 1) if team_responsibility_avg else 1
        if kills_avg > 1:  # 避免除以1的问题
            kills_ratio = battle_data.get('kills', 0) / kills_avg
            if kills_ratio > 1:  # 只有超过平均才加分
                kills_score = (kills_ratio - 1) * 1.5  # 进一步下调：3 → 1.5
                score += kills_score
                bonus_items.append(f"击杀表现良好: +{kills_score:.1f}分 ({battle_data.get('kills', 0)}/{kills_avg:.1f})")

        # 移除助攻评分（按要求不做参考）

        # 重伤评分：重伤多扣分，重伤少加分（使用全体平均值，重伤是通用指标）
        heavy_avg = overall_avg.get('heavy_injuries', 1)
        if heavy_avg > 0:
            heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
            if heavy_ratio > 1:
                heavy_penalty = (heavy_ratio - 1) * 8
                score -= heavy_penalty
                penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
            elif heavy_ratio < 0.8:  # 重伤少的给加分
                heavy_bonus = (0.8 - heavy_ratio) * 6
                score += heavy_bonus
                bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

    elif responsibility == '击杀':
        details.append(f"⚔️ 击杀职责评分")

        # 🆕 强制使用团队-击杀职责专项平均值（禁用旧的兼容逻辑）
        team_responsibility_avg = battle_averages.get('by_team_responsibility', {}).get(team_type, {}).get('击杀', {})
        # ❌ 禁用旧的职责平均值
        # responsibility_avg = battle_averages.get('by_responsibility', {}).get('击杀', {})

        print(f"🆕 {team_type}-击杀职责评分，优先使用团队专项平均值")

        # 击杀职责的潮光不考核清泉，只考核击杀能力
        if profession == '潮光':
            details.append(f"🌊 潮光击杀职责评分 (不考核清泉)")

        # 🆕 主要职责：击杀数（强制使用团队-击杀职责专项平均值）
        kills_avg = team_responsibility_avg.get('kills', 1) if team_responsibility_avg else 1

        print(f"🆕 击杀数评分: 强制使用新平均值({team_type}-击杀)={kills_avg:.1f}")
        if not team_responsibility_avg:
            print(f"⚠️ 警告: {team_type}-击杀专项平均值不存在，使用默认值1")

        if kills_avg > 0:
            kills_ratio = battle_data.get('kills', 0) / kills_avg
            kills_score = (kills_ratio - 1) * 35  # 🆕 增加击杀权重：25 → 35
            score += kills_score
            if kills_score > 0:
                bonus_items.append(f"击杀数据优秀: +{kills_score:.1f}分 ({battle_data.get('kills', 0)}/{kills_avg:.1f})")
            else:
                penalty_items.append(f"击杀数据不足: {kills_score:.1f}分 ({battle_data.get('kills', 0)}/{kills_avg:.1f})")

        # 次要加分：人伤数据（使用团队-击杀职责专项平均值，只加分不减分）
        player_dmg_avg = team_responsibility_avg.get('player_damage', 1) if team_responsibility_avg else 1
        if player_dmg_avg > 1:
            player_dmg_ratio = battle_data.get('player_damage', 0) / player_dmg_avg
            if player_dmg_ratio > 1:  # 只有超过平均才加分
                player_dmg_score = (player_dmg_ratio - 1) * 3.0  # 🆕 增加人伤权重：2.0 → 3.0
                score += player_dmg_score
                bonus_items.append(f"玩家伤害良好: +{player_dmg_score:.1f}分 ({battle_data.get('player_damage', 0):.0f}/{player_dmg_avg:.0f})")

        # 移除助攻评分（按要求不做参考）

        # 重伤评分：重伤多扣分，重伤少加分（使用全体平均值，重伤是通用指标）
        heavy_avg = overall_avg.get('heavy_injuries', 1)
        if heavy_avg > 0:
            heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
            if heavy_ratio > 1:
                # 碎梦重伤扣分稍少，但仍然扣分
                heavy_penalty = (heavy_ratio - 1) * (4 if profession == '碎梦' else 6)
                score -= heavy_penalty
                penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
            elif heavy_ratio < 0.8:  # 重伤少的给加分
                heavy_bonus = (0.8 - heavy_ratio) * 5
                score += heavy_bonus
                bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

    elif responsibility == '人伤':
        details.append(f"💥 人伤职责评分")

        # 🆕 强制使用团队-人伤职责专项平均值（禁用旧的兼容逻辑）
        team_responsibility_avg = battle_averages.get('by_team_responsibility', {}).get(team_type, {}).get('人伤', {})
        # ❌ 禁用旧的职责平均值
        # responsibility_avg = battle_averages.get('by_responsibility', {}).get('人伤', {})

        print(f"🆕 {team_type}-人伤职责评分，优先使用团队专项平均值")

        # 人伤职责的潮光不考核清泉，只考核人伤能力
        if profession == '潮光':
            details.append(f"🌊 潮光人伤职责评分 (不考核清泉)")

        # 🆕 主要职责：玩家伤害（强制使用团队-人伤职责专项平均值）
        player_dmg_avg = team_responsibility_avg.get('player_damage', 1) if team_responsibility_avg else 1

        print(f"🆕 玩家伤害评分: 强制使用新平均值({team_type}-人伤)={player_dmg_avg:.0f}")
        if not team_responsibility_avg:
            print(f"⚠️ 警告: {team_type}-人伤专项平均值不存在，使用默认值1")

        if player_dmg_avg > 1000:  # 确保平均值合理
            player_dmg_ratio = battle_data.get('player_damage', 0) / player_dmg_avg
            player_dmg_score = (player_dmg_ratio - 1) * 20
            score += player_dmg_score
            if player_dmg_score > 0:
                bonus_items.append(f"玩家伤害优秀: +{player_dmg_score:.1f}分 ({battle_data.get('player_damage', 0):.0f}/{player_dmg_avg:.0f})")
            else:
                penalty_items.append(f"玩家伤害不足: {player_dmg_score:.1f}分 ({battle_data.get('player_damage', 0):.0f}/{player_dmg_avg:.0f})")
        else:
            print(f"  人伤职责警告: 玩家伤害平均值过小 {player_dmg_avg}, 跳过评分")

        # 次要加分：击杀（使用团队-人伤职责专项平均值，只加分不减分）
        kills_avg = team_responsibility_avg.get('kills', 1) if team_responsibility_avg else 1
        if kills_avg > 1:  # 避免除以1的问题
            kills_ratio = battle_data.get('kills', 0) / kills_avg
            if kills_ratio > 1:  # 只有超过平均才加分
                kills_score = (kills_ratio - 1) * 2.5  # 🆕 适度增加次要击杀权重：2.0 → 2.5
                score += kills_score
                bonus_items.append(f"击杀表现良好: +{kills_score:.1f}分 ({battle_data.get('kills', 0)}/{kills_avg:.1f})")

        # 移除助攻评分（按要求不做参考）

        # 重伤评分：重伤多扣分，重伤少加分（使用全体平均值，重伤是通用指标）
        heavy_avg = overall_avg.get('heavy_injuries', 1)
        if heavy_avg > 0:
            heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
            if heavy_ratio > 1:
                heavy_penalty = (heavy_ratio - 1) * 6
                score -= heavy_penalty
                penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
            elif heavy_ratio < 0.8:  # 重伤少的给加分
                heavy_bonus = (0.8 - heavy_ratio) * 5
                score += heavy_bonus
                bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

    elif responsibility == '治疗':
        details.append(f"💚 治疗职责评分")

        # 🆕 强制使用团队-治疗职责专项平均值（禁用旧的兼容逻辑）
        team_responsibility_avg = battle_averages.get('by_team_responsibility', {}).get(team_type, {}).get('治疗', {})

        print(f"🆕 {team_type}-治疗职责评分，强制使用团队专项平均值")

        # 素问：只考核羽化和重伤
        if profession == '素问':
            details.append(f"🌸 素问治疗职责评分 (羽化+重伤)")

            # 主要考核：羽化数据（使用团队-治疗职责专项平均值）
            resurrections_avg = team_responsibility_avg.get('resurrections', 1) if team_responsibility_avg else 1
            if resurrections_avg > 1:  # 避免除以1的问题
                resurrections_ratio = battle_data.get('resurrections', 0) / resurrections_avg
                resurrections_score = (resurrections_ratio - 1) * 30  # 主要考核指标
                score += resurrections_score
                if resurrections_score > 0:
                    bonus_items.append(f"羽化数据优秀: +{resurrections_score:.1f}分 ({battle_data.get('resurrections', 0)}/{resurrections_avg:.1f})")
                else:
                    penalty_items.append(f"羽化数据不足: {resurrections_score:.1f}分 ({battle_data.get('resurrections', 0)}/{resurrections_avg:.1f})")

            # 主要考核：重伤评分（素问死太多次很严重，死得少要加分）- 使用全体平均值
            heavy_avg = overall_avg.get('heavy_injuries', 1)
            if heavy_avg > 0:
                heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
                if heavy_ratio > 1:
                    heavy_penalty = (heavy_ratio - 1) * 15  # 素问重伤扣分更重
                    score -= heavy_penalty
                    penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
                elif heavy_ratio < 0.8:  # 重伤少的给加分
                    heavy_bonus = (0.8 - heavy_ratio) * 10  # 素问重伤少加分也更多
                    score += heavy_bonus
                    bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

            # 次要加分：治疗量（素问也有一定治疗能力）- 使用团队专项平均值
            healing_avg = team_responsibility_avg.get('healing', 1) if team_responsibility_avg else 1
            print(f"🆕 素问治疗量评分: 使用团队专项平均值({team_type}-治疗)={healing_avg:.0f}")
            if healing_avg > 1000:
                healing_ratio = battle_data.get('healing', 0) / healing_avg
                if healing_ratio > 1:  # 只有超过平均才加分
                    healing_score = (healing_ratio - 1) * 1.0  # 缩小10倍：10 → 1.0
                    score += healing_score
                    bonus_items.append(f"治疗量良好: +{healing_score:.1f}分 ({battle_data.get('healing', 0):.0f}/{healing_avg:.0f})")

        # 其他治疗职业：主要考核治疗量
        else:
            # 主要考核：治疗量（使用团队-治疗职责专项平均值）
            healing_avg = team_responsibility_avg.get('healing', 1) if team_responsibility_avg else 1
            if healing_avg > 1000:  # 确保平均值合理
                healing_ratio = battle_data.get('healing', 0) / healing_avg
                healing_score = (healing_ratio - 1) * 25  # 主要考核指标
                score += healing_score
                if healing_score > 0:
                    bonus_items.append(f"治疗量优秀: +{healing_score:.1f}分 ({battle_data.get('healing', 0):.0f}/{healing_avg:.0f})")
                else:
                    penalty_items.append(f"治疗量不足: {healing_score:.1f}分 ({battle_data.get('healing', 0):.0f}/{healing_avg:.0f})")
            else:
                print(f"  治疗职责警告: 治疗量平均值过小 {healing_avg}, 跳过评分")

            # 次要加分：羽化数据（只加分不减分）
            resurrections_avg = team_responsibility_avg.get('resurrections', 1) if team_responsibility_avg else 1
            if resurrections_avg > 1:
                resurrections_ratio = battle_data.get('resurrections', 0) / resurrections_avg
                if resurrections_ratio > 1:  # 只有超过平均才加分
                    resurrections_score = (resurrections_ratio - 1) * 8
                    score += resurrections_score
                    bonus_items.append(f"羽化数据良好: +{resurrections_score:.1f}分 ({battle_data.get('resurrections', 0)}/{resurrections_avg:.1f})")

        # 减分：重伤过多（治疗死太多次不好）- 只对非素问治疗职业扣分，使用全体平均值
        if profession != '素问':  # 素问已经在上面扣过重伤分了
            heavy_avg = overall_avg.get('heavy_injuries', 1)
            if heavy_avg > 0:
                heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
                if heavy_ratio > 1:
                    heavy_penalty = (heavy_ratio - 1) * 12
                    score -= heavy_penalty
                    penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
                elif heavy_ratio < 0.8:  # 重伤少的给加分
                    heavy_bonus = (0.8 - heavy_ratio) * 8
                    score += heavy_bonus
                    bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

    elif responsibility == '扛伤':
        details.append(f"🛡️ 扛伤职责评分")

        # 🆕 强制使用团队-扛伤职责专项平均值（禁用旧的兼容逻辑）
        team_responsibility_avg = battle_averages.get('by_team_responsibility', {}).get(team_type, {}).get('扛伤', {})

        print(f"🆕 {team_type}-扛伤职责评分，强制使用团队专项平均值")

        # 主要职责：承受伤害（使用团队-扛伤职责专项平均值）
        damage_taken_avg = team_responsibility_avg.get('damage_taken', 1) if team_responsibility_avg else 1
        if damage_taken_avg > 1000:  # 确保平均值合理
            damage_taken_ratio = battle_data.get('damage_taken', 0) / damage_taken_avg
            damage_taken_score = (damage_taken_ratio - 1) * 25
            score += damage_taken_score
            if damage_taken_score > 0:
                bonus_items.append(f"承受伤害优秀: +{damage_taken_score:.1f}分 ({battle_data.get('damage_taken', 0):.0f}/{damage_taken_avg:.0f})")
            else:
                penalty_items.append(f"承受伤害不足: {damage_taken_score:.1f}分 ({battle_data.get('damage_taken', 0):.0f}/{damage_taken_avg:.0f})")
        else:
            print(f"  扛伤职责警告: 承受伤害平均值过小 {damage_taken_avg}, 跳过评分")

        # 次要加分：击杀（使用团队-扛伤职责专项平均值，只加分不减分）
        kills_avg = team_responsibility_avg.get('kills', 1) if team_responsibility_avg else 1
        if kills_avg > 1:  # 避免除以1的问题
            kills_ratio = battle_data.get('kills', 0) / kills_avg
            if kills_ratio > 1:  # 只有超过平均才加分
                kills_score = (kills_ratio - 1) * 2.5  # 🆕 适度增加次要击杀权重：2.0 → 2.5
                score += kills_score
                bonus_items.append(f"击杀表现良好: +{kills_score:.1f}分 ({battle_data.get('kills', 0)}/{kills_avg:.1f})")

        # 重伤评分：坦克死太多次不好，死得少要加分（使用全体平均值，重伤是通用指标）
        heavy_avg = overall_avg.get('heavy_injuries', 1)
        if heavy_avg > 0:
            heavy_ratio = battle_data.get('heavy_injuries', 0) / heavy_avg
            if heavy_ratio > 1:
                heavy_penalty = (heavy_ratio - 1) * 10  # 坦克重伤扣分更重
                score -= heavy_penalty
                penalty_items.append(f"重伤过多: -{heavy_penalty:.1f}分 ({battle_data.get('heavy_injuries', 0)}/{heavy_avg:.1f})")
            elif heavy_ratio < 0.8:  # 重伤少的给加分
                heavy_bonus = (0.8 - heavy_ratio) * 8
                score += heavy_bonus
                bonus_items.append(f"生存能力优秀: +{heavy_bonus:.1f}分 (重伤较少)")

    # 资源作为纯加分项（不扣分，只加分）
    resources_avg = overall_avg.get('resources', 1)
    if resources_avg > 0 and battle_data.get('resources', 0) > 0:
        resources_ratio = battle_data.get('resources', 0) / resources_avg
        if resources_ratio > 0.5:  # 只有达到一定水平才加分
            if team_type == '防守团':
                # 防守团资源权重更高（缩小10倍）
                resources_score = resources_ratio * 1.5  # 纯加分，不减1
                bonus_items.append(f"资源贡献优秀(防守团): +{resources_score:.1f}分 ({battle_data.get('resources', 0)}/{resources_avg:.1f})")
            else:
                # 进攻团资源权重较低（缩小10倍）
                resources_score = resources_ratio * 0.8  # 纯加分，不减1
                bonus_items.append(f"资源贡献良好(进攻团): +{resources_score:.1f}分 ({battle_data.get('resources', 0)}/{resources_avg:.1f})")
            score += resources_score

    # 确保分数在合理范围内 (最低5分，最高100分)
    score = max(5, min(100, score))

    print(f"  最终评分: {score:.1f}")
    print(f"  加分项: {len(bonus_items)} 项")
    print(f"  扣分项: {len(penalty_items)} 项")

    return {
        'score': score,
        'details': details,
        'bonus_items': bonus_items,
        'penalty_items': penalty_items
    }


def get_performance_rating(score):
    """根据分数获取评级"""
    if score is None:
        return '未知'
    if score >= 100:
        return 'S+'
    elif score >= 80:
        return 'S'
    elif score >= 60:
        return 'A'
    elif score >= 40:
        return 'B'
    elif score >= 20:
        return 'C'
    elif score > 0:
        return 'D'
    else:
        return '未参战'

# 认证路由
@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        if 'guest_access' in request.form:
            # 游客访问
            session['user_id'] = 'guest'
            flash('以游客身份访问，功能受限', 'info')
            return redirect(url_for('index'))
        else:
            # 正常登录
            username = request.form['username']
            password = request.form['password']

            users = load_users()
            user = users.get(username)

            # 验证密码（统一使用哈希验证）
            password_valid = False
            if user:
                # 所有用户都使用哈希密码验证
                password_valid = verify_password(password, user['password'])

            if password_valid:
                session['user_id'] = username
                flash(f'欢迎回来，{user["name"]}！', 'success')
                return redirect(url_for('index'))
            else:
                flash('用户名或密码错误', 'error')

    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册页面"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        email = request.form.get('email', '')

        # 验证输入
        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return render_template('register.html')

        if len(password) < 6:
            flash('密码长度至少6位', 'error')
            return render_template('register.html')

        # 检查用户名是否已存在
        users = load_users()
        if username in users:
            flash('用户名已存在', 'error')
            return render_template('register.html')

        # 创建新用户
        new_user = {
            'password': hash_password(password),  # 哈希密码
            'role': 'user',
            'name': username,
            'email': email,
            'guild_id': None,
            'status': 'active'
        }

        if save_user(username, new_user):
            flash('注册成功！请登录', 'success')
            return redirect(url_for('login'))
        else:
            flash('注册失败，请稍后重试', 'error')
            return render_template('register.html')

    return render_template('register.html')

@app.route('/guild_application', methods=['GET', 'POST'])
@login_required
def guild_application():
    """帮会申请页面"""
    user = get_current_user()
    if not user or user['role'] != 'user':
        flash('只有普通用户可以申请帮会', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        app_type = request.form['application_type']
        description = request.form.get('description', '')

        if app_type == 'create_guild':
            guild_name = request.form['guild_name']
            guild_id = request.form['guild_id']

            # 检查帮会ID是否已存在
            guilds = load_guilds()
            if guild_id in guilds:
                flash('帮会ID已存在，请选择其他ID', 'error')
                return render_template('guild_application.html', guilds=load_guilds())

            if save_guild_application(user['name'], 'create_guild', guild_name, guild_id, description):
                flash('创建帮会申请已提交，等待超级管理员审批', 'success')
                return redirect(url_for('index'))
            else:
                flash('申请提交失败，请稍后重试', 'error')

        elif app_type == 'join_guild':
            guild_id = request.form['guild_id']

            if save_guild_application(user['name'], 'join_guild', None, guild_id, description):
                flash('加入帮会申请已提交，等待帮会大当家审批', 'success')
                return redirect(url_for('index'))
            else:
                flash('申请提交失败，请稍后重试', 'error')

    guilds = load_guilds()
    return render_template('guild_application.html', guilds=guilds)

@app.route('/super_admin')
@login_required
def super_admin_dashboard():
    """超级管理员仪表板 - 帮会管理界面"""
    user = get_current_user()
    if not user or user['role'] != 'super_admin':
        flash('需要超级管理员权限', 'error')
        return redirect(url_for('index'))

    # 加载所有帮会数据
    guilds = load_guilds()
    users = load_users()
    applications = load_guild_applications()

    # 统计信息
    total_guilds = len(guilds)
    total_users = len([u for u in users.values() if u['role'] != 'guest'])
    pending_applications = len([app for app in applications if app['status'] == 'pending'])

    # 帮会统计
    guild_stats = []
    for guild_id, guild in guilds.items():
        # 统计帮会成员
        guild_users = [u for u in users.values() if u.get('guild_id') == guild_id]

        # 获取帮会申请
        guild_applications = [app for app in applications if app.get('guild_id') == guild_id and app['status'] == 'pending']

        guild_stats.append({
            'id': guild_id,
            'name': guild['name'],
            'leader': guild['leader'],
            'members_count': len(guild_users),
            'max_members': guild.get('max_members', 100),
            'pending_applications': len(guild_applications),
            'created_time': guild.get('created_time', ''),
            'status': guild.get('status', 'active')
        })

    # 按创建时间排序
    guild_stats.sort(key=lambda x: x['created_time'], reverse=True)

    return render_template('super_admin_dashboard.html',
                         guilds=guild_stats,
                         total_guilds=total_guilds,
                         total_users=total_users,
                         pending_applications=pending_applications)

@app.route('/admin/users')
@login_required
def admin_users():
    """超级管理员用户管理页面"""
    user = get_current_user()
    if not user or user['role'] != 'super_admin':
        flash('需要超级管理员权限', 'error')
        return redirect(url_for('index'))

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    role_filter = request.args.get('role', 'all', type=str)
    guild_filter = request.args.get('guild', 'all', type=str)

    # 加载数据
    users = load_users()
    guilds = load_guilds()

    # 过滤用户（排除游客）
    filtered_users = []
    for username, user_data in users.items():
        if username == 'guest':
            continue

        # 搜索过滤
        if search:
            search_lower = search.lower()
            if not (search_lower in username.lower() or
                   search_lower in user_data.get('name', '').lower() or
                   search_lower in user_data.get('email', '').lower()):
                continue

        # 角色过滤
        if role_filter != 'all' and user_data.get('role') != role_filter:
            continue

        # 帮会过滤
        if guild_filter != 'all':
            if guild_filter == 'none':
                if user_data.get('guild_id'):
                    continue
            else:
                if user_data.get('guild_id') != guild_filter:
                    continue

        # 添加用户名到数据中
        user_data['username'] = username
        filtered_users.append(user_data)

    # 按创建时间排序
    filtered_users.sort(key=lambda x: x.get('created_time', ''), reverse=True)

    # 分页
    total_users_count = len(filtered_users)
    total_pages = (total_users_count + per_page - 1) // per_page
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_users = filtered_users[start_idx:end_idx]

    # 分页信息
    pagination = {
        'page': page,
        'per_page': per_page,
        'total': total_users_count,
        'total_pages': total_pages,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if page < total_pages else None,
        'pages': list(range(max(1, page - 2), min(total_pages + 1, page + 3)))
    }

    return render_template('admin_users.html',
                         users=paginated_users,
                         guilds=guilds,
                         pagination=pagination,
                         search=search,
                         role_filter=role_filter,
                         guild_filter=guild_filter)

@app.route('/admin/applications')
@login_required
def admin_applications():
    """管理员审批页面"""
    user = get_current_user()
    if not user or user['role'] not in ['super_admin', 'guild_leader']:
        flash('需要管理员权限', 'error')
        return redirect(url_for('index'))

    applications = load_guild_applications()

    # 根据用户角色过滤申请
    if user['role'] == 'guild_leader':
        # 帮会大当家只能看到加入自己帮会的申请
        user_guild = user.get('guild_id')
        applications = [app for app in applications if app['application_type'] == 'join_guild' and app['guild_id'] == user_guild]

    return render_template('admin_applications.html', applications=applications)

@app.route('/admin/process_application', methods=['POST'])
@login_required
def process_application():
    """处理申请"""
    user = get_current_user()
    if not user or user['role'] not in ['super_admin', 'guild_leader']:
        return jsonify({'error': '需要管理员权限'}), 403

    app_id = request.form['app_id']
    action = request.form['action']  # approve 或 reject

    # 获取申请详情
    applications = load_guild_applications()
    application = next((app for app in applications if app['id'] == int(app_id)), None)

    if not application:
        return jsonify({'error': '申请不存在'}), 404

    # 权限检查
    if user['role'] == 'guild_leader':
        if application['application_type'] != 'join_guild' or application['guild_id'] != user.get('guild_id'):
            return jsonify({'error': '权限不足'}), 403

    if action == 'approve':
        if application['application_type'] == 'create_guild':
            # 创建帮会
            if create_guild(application['guild_id'], application['guild_name'], application['username'], application['description']):
                update_application_status(app_id, 'approved', user['name'])
                return jsonify({'success': True, 'message': '帮会创建成功'})
            else:
                return jsonify({'error': '帮会创建失败'}), 500

        elif application['application_type'] == 'join_guild':
            # 加入帮会
            connection = get_db_connection()
            if connection:
                try:
                    cursor = connection.cursor()
                    cursor.execute("""
                        UPDATE users SET guild_id = %s WHERE username = %s
                    """, (application['guild_id'], application['username']))

                    # 清除用户缓存
                    cache.delete('users')

                    update_application_status(app_id, 'approved', user['name'])
                    return jsonify({'success': True, 'message': '用户已加入帮会'})
                except Exception as e:
                    return jsonify({'error': f'加入帮会失败: {e}'}), 500
                finally:
                    connection.close()

    elif action == 'reject':
        update_application_status(app_id, 'rejected', user['name'])
        return jsonify({'success': True, 'message': '申请已拒绝'})

    return jsonify({'error': '无效操作'}), 400

@app.route('/admin/user_action', methods=['POST'])
@login_required
def admin_user_action():
    """超级管理员用户操作"""
    user = get_current_user()
    if not user or user['role'] != 'super_admin':
        return jsonify({'error': '需要超级管理员权限'}), 403

    data = request.get_json()
    action = data.get('action')
    username = data.get('username')

    if not action or not username:
        return jsonify({'error': '缺少必要参数'}), 400

    connection = get_db_connection()
    if not connection:
        return jsonify({'error': '数据库连接失败'}), 500

    try:
        cursor = connection.cursor()

        if action == 'change_role':
            new_role = data.get('new_role')
            if new_role not in ['user', 'guild_leader', 'super_admin']:
                return jsonify({'error': '无效角色'}), 400

            cursor.execute("""
                UPDATE users SET role = %s WHERE username = %s
            """, (new_role, username))

            # 清除用户缓存
            cache.delete('users')

            return jsonify({'success': True, 'message': f'用户 {username} 角色已更改为 {new_role}'})

        elif action == 'change_guild':
            new_guild_id = data.get('new_guild_id')

            cursor.execute("""
                UPDATE users SET guild_id = %s WHERE username = %s
            """, (new_guild_id if new_guild_id != 'none' else None, username))

            # 清除用户缓存
            cache.delete('users')

            guild_name = '无帮会' if new_guild_id == 'none' else new_guild_id
            return jsonify({'success': True, 'message': f'用户 {username} 已转移到 {guild_name}'})

        elif action == 'ban_user':
            cursor.execute("""
                UPDATE users SET status = 'banned' WHERE username = %s
            """, (username,))

            # 清除用户缓存
            cache.delete('users')

            return jsonify({'success': True, 'message': f'用户 {username} 已被封禁'})

        elif action == 'unban_user':
            cursor.execute("""
                UPDATE users SET status = 'active' WHERE username = %s
            """, (username,))

            # 清除用户缓存
            cache.delete('users')

            return jsonify({'success': True, 'message': f'用户 {username} 已解除封禁'})

        elif action == 'delete_user':
            # 删除用户（谨慎操作）
            cursor.execute("""
                DELETE FROM users WHERE username = %s
            """, (username,))

            # 清除用户缓存
            cache.delete('users')

            return jsonify({'success': True, 'message': f'用户 {username} 已被删除'})

        else:
            return jsonify({'error': '未知操作'}), 400

    except Exception as e:
        return jsonify({'error': f'操作失败: {e}'}), 500
    finally:
        return_db_connection(connection)

@app.route('/admin/guild/<guild_id>')
@login_required
def admin_guild_detail(guild_id):
    """超级管理员帮会详情管理"""
    user = get_current_user()
    if not user or user['role'] != 'super_admin':
        flash('需要超级管理员权限', 'error')
        return redirect(url_for('index'))

    # 加载帮会信息
    guilds = load_guilds()
    guild = guilds.get(guild_id)

    if not guild:
        flash('帮会不存在', 'error')
        return redirect(url_for('super_admin_dashboard'))

    # 加载帮会成员
    users = load_users()
    guild_users = [u for u in users.values() if u.get('guild_id') == guild_id and u.get('username') != 'guest']

    # 加载帮会申请
    applications = load_guild_applications()
    guild_applications = [app for app in applications if app.get('guild_id') == guild_id]

    # 统计信息
    stats = {
        'total_members': len(guild_users),
        'active_members': len([u for u in guild_users if u.get('status') == 'active']),
        'pending_applications': len([app for app in guild_applications if app['status'] == 'pending']),
        'total_applications': len(guild_applications)
    }

    return render_template('admin_guild_detail.html',
                         guild=guild,
                         guild_id=guild_id,
                         guild_users=guild_users,
                         guild_applications=guild_applications,
                         stats=stats)

@app.route('/admin/guild/<guild_id>/members')
@login_required
def admin_guild_members(guild_id):
    """超级管理员帮会成员管理"""
    user = get_current_user()
    if not user or user['role'] != 'super_admin':
        flash('需要超级管理员权限', 'error')
        return redirect(url_for('index'))

    # 加载帮会信息
    guilds = load_guilds()
    guild = guilds.get(guild_id)

    if not guild:
        flash('帮会不存在', 'error')
        return redirect(url_for('super_admin_dashboard'))

    # 加载帮会的游戏成员数据（从数据库）
    game_members = []
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT * FROM guild_members
                WHERE guild_id = %s
                ORDER BY name
            """, (guild_id,))
            game_members = cursor.fetchall()
        except Exception as e:
            print(f"加载帮会游戏成员失败: {e}")
        finally:
            return_db_connection(connection)

    # 如果数据库没有数据，且是纸落云烟帮会，则从JSON加载
    if not game_members and guild_id == 'zhiluoyunyan':
        try:
            with open(MEMBERS_FILE, 'r', encoding='utf-8') as f:
                json_members = json.load(f)
                # 确保game_members是列表
                if not isinstance(game_members, list):
                    game_members = []
                # 转换为数据库格式
                for member in json_members:
                    game_members.append({
                        'name': member.get('name', ''),
                        'profession': member.get('profession', ''),
                        'main_group': member.get('main_group', ''),
                        'sub_team': member.get('sub_team', ''),
                        'squad': member.get('squad', ''),
                        'position': member.get('position', ''),
                        'status': member.get('status', '主力')
                    })
        except Exception as e:
            print(f"从JSON加载成员失败: {e}")
            game_members = []  # 确保出错时也是列表

    # 加载帮会用户（注册用户）
    users = load_users()
    guild_users = [u for u in users.values() if u.get('guild_id') == guild_id and u.get('username') != 'guest']

    return render_template('admin_guild_members.html',
                         guild=guild,
                         guild_id=guild_id,
                         game_members=game_members,
                         guild_users=guild_users)

@app.route('/admin/settings')
@login_required
def admin_settings():
    """超级管理员系统设置"""
    user = get_current_user()
    if not user or user['role'] != 'super_admin':
        flash('需要超级管理员权限', 'error')
        return redirect(url_for('index'))

    # 系统统计
    users = load_users()
    guilds = load_guilds()
    applications = load_guild_applications()

    system_stats = {
        'total_users': len([u for u in users.values() if u.get('username') != 'guest']),
        'total_guilds': len(guilds),
        'total_applications': len(applications),
        'active_users': len([u for u in users.values() if u.get('status') == 'active' and u.get('username') != 'guest']),
        'banned_users': len([u for u in users.values() if u.get('status') == 'banned']),
        'pending_applications': len([app for app in applications if app['status'] == 'pending'])
    }

    # 缓存统计
    cache_stats = {
        'cached_items': len(cache._cache),
        'cache_hits': 'N/A',  # 可以添加缓存命中统计
        'cache_size': 'N/A'   # 可以添加缓存大小统计
    }

    return render_template('admin_settings.html',
                         system_stats=system_stats,
                         cache_stats=cache_stats)

@app.route('/logout')
def logout():
    """登出"""
    user = get_current_user()
    if user:
        flash(f'再见，{user["name"]}！', 'info')
    session.pop('user_id', None)
    return redirect(url_for('login'))

# 角色绑定相关路由
@app.route('/character_binding', methods=['GET', 'POST'])
@login_required
def character_binding():
    """角色绑定页面"""
    user = get_current_user()
    if not user or user['role'] == 'guest':
        flash('请先注册登录', 'error')
        return redirect(url_for('login'))

    # 获取用户ID
    users = load_users()
    user_id = None
    for username, user_data in users.items():
        if username == session['user_id']:
            # 从数据库获取用户ID
            connection = get_db_connection()
            if connection:
                try:
                    cursor = connection.cursor()
                    cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
                    result = cursor.fetchone()
                    if result:
                        user_id = result[0]
                finally:
                    return_db_connection(connection)
            break

    if not user_id:
        flash('用户信息错误', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        character_name = request.form.get('character_name', '').strip()
        if not character_name:
            flash('请输入角色名称', 'error')
        else:
            success, result = create_character_binding(user_id, character_name)
            if success:
                flash(f'角色绑定申请已提交！验证码：{result}，请在游戏内完成验证', 'success')
                return redirect(url_for('character_binding'))
            else:
                flash(f'申请失败：{result}', 'error')

    # 获取用户的绑定记录
    bindings = get_character_bindings(user_id=user_id)

    return render_template('character_binding.html', bindings=bindings)

@app.route('/admin/character_bindings')
@admin_required
def admin_character_bindings():
    """管理员审核角色绑定"""
    # 获取所有待审核的绑定
    pending_bindings = get_character_bindings(status='pending')
    all_bindings = get_character_bindings()

    return render_template('admin_character_bindings.html',
                         pending_bindings=pending_bindings,
                         all_bindings=all_bindings)

@app.route('/admin/approve_binding/<int:binding_id>')
@admin_required
def approve_binding(binding_id):
    """审核通过角色绑定"""
    user = get_current_user()
    if approve_character_binding(binding_id, session['user_id']):
        flash('角色绑定已审核通过', 'success')
    else:
        flash('审核失败', 'error')
    return redirect(url_for('admin_character_bindings'))

@app.route('/admin/reject_binding/<int:binding_id>')
@admin_required
def reject_binding(binding_id):
    """拒绝角色绑定"""
    user = get_current_user()
    if reject_character_binding(binding_id, session['user_id']):
        flash('角色绑定已拒绝', 'success')
    else:
        flash('操作失败', 'error')
    return redirect(url_for('admin_character_bindings'))

@app.route('/admin/unbind_character', methods=['POST'])
@admin_required
def admin_unbind_character():
    """管理员解绑角色"""
    character_name = request.form.get('character_name', '').strip()
    reason = request.form.get('reason', '管理员解绑').strip()

    if not character_name:
        flash('请输入角色名称', 'error')
        return redirect(url_for('admin_character_bindings'))

    success, message = unbind_character(character_name, session['user_id'], reason)
    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('admin_character_bindings'))

@app.route('/admin/force_bind_character', methods=['POST'])
@admin_required
def admin_force_bind_character():
    """管理员强制绑定角色"""
    username = request.form.get('username', '').strip()
    character_name = request.form.get('character_name', '').strip()
    reason = request.form.get('reason', '管理员强制绑定').strip()

    if not username or not character_name:
        flash('请输入用户名和角色名称', 'error')
        return redirect(url_for('admin_character_bindings'))

    # 获取用户ID
    connection = get_db_connection()
    if not connection:
        flash('数据库连接失败', 'error')
        return redirect(url_for('admin_character_bindings'))

    try:
        cursor = connection.cursor()
        cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
        user_result = cursor.fetchone()

        if not user_result:
            flash(f'用户 {username} 不存在', 'error')
            return redirect(url_for('admin_character_bindings'))

        user_id = user_result[0]
        success, message = force_bind_character(user_id, character_name, session['user_id'], reason)

        if success:
            flash(message, 'success')
        else:
            flash(message, 'error')

    except Exception as e:
        flash(f'操作失败：{str(e)}', 'error')
    finally:
        return_db_connection(connection)

    return redirect(url_for('admin_character_bindings'))

@app.route('/admin/manual_kick_member', methods=['POST'])
@admin_required
def admin_manual_kick_member():
    """管理员手动踢出成员"""
    member_name = request.form.get('member_name', '').strip()
    reason = request.form.get('reason', '管理员直接踢出').strip()

    if not member_name:
        flash('请输入成员名称', 'error')
        return redirect(url_for('admin_kick_management'))

    user = get_current_user()
    if not user or not user.get('guild_id'):
        flash('无法获取帮会信息', 'error')
        return redirect(url_for('index'))

    success, message = manual_kick_member(user['guild_id'], member_name, session['user_id'], reason)
    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('admin_kick_management'))

@app.route('/admin/kick_management')
@admin_required
def admin_kick_management():
    """踢出管理页面"""
    user = get_current_user()
    if not user or not user.get('guild_id'):
        flash('无法获取帮会信息', 'error')
        return redirect(url_for('index'))

    guild_id = user['guild_id']
    pending_kicks = get_pending_kick_records(guild_id)

    return render_template('admin_kick_management.html', pending_kicks=pending_kicks)

@app.route('/personal_dashboard')
@login_required
def personal_dashboard():
    """个人首页 - 显示绑定角色的详细信息"""
    user = get_current_user()
    if not user or user['role'] == 'guest':
        flash('请先注册登录', 'error')
        return redirect(url_for('login'))

    # 获取用户ID
    connection = get_db_connection()
    if not connection:
        flash('数据库连接失败', 'error')
        return redirect(url_for('index'))

    try:
        cursor = connection.cursor()
        cursor.execute("SELECT id FROM users WHERE username = %s", (session['user_id'],))
        result = cursor.fetchone()
        if not result:
            flash('用户信息错误', 'error')
            return redirect(url_for('index'))

        user_id = result[0]
        bound_character = get_user_bound_character(user_id)

        if not bound_character:
            flash('您还没有绑定角色，请先绑定角色', 'info')
            return redirect(url_for('character_binding'))

        # 清除缓存，确保获取最新数据
        if user and user.get('guild_id'):
            cache_key = f'members_{user["guild_id"]}'
            cache.delete(cache_key)

        # 获取角色详细信息
        members = load_members()
        character_info = None
        for member in members:
            if member['name'] == bound_character:
                character_info = member
                break

        if not character_info:
            # 如果在成员列表中找不到，直接从数据库查询
            cursor.execute("""
                SELECT * FROM guild_members
                WHERE name = %s AND guild_id = %s
            """, (bound_character, user['guild_id']))
            db_member = cursor.fetchone()
            if db_member:
                character_info = {
                    'name': db_member[1],  # name
                    'profession': db_member[2],  # profession
                    'main_group': db_member[3],  # main_group
                    'sub_team': db_member[4],  # sub_team
                    'squad': db_member[5],  # squad
                    'position': db_member[6],  # position
                    'status': db_member[7]  # status
                }
                print(f"从数据库直接获取角色信息: {character_info}")
            else:
                flash('未找到角色信息', 'error')
                return redirect(url_for('character_binding'))

        print(f"角色信息: {character_info}")

        # 获取战斗记录
        battle_records = load_battle_records()
        character_battles = []

        print(f"加载了 {len(battle_records)} 条战斗记录")

        for record in battle_records:
            member_performance = record.get('member_performance', {})
            if bound_character in member_performance:
                performance = member_performance[bound_character]
                battle_data = performance.get('battle_data', {})

                # 构造个人战斗记录
                battle_info = {
                    'battle_id': record.get('battle_id', ''),
                    'battle_date': extract_battle_date(record),
                    'enemy_guild': record.get('enemy_guild', ''),
                    'score': round(performance.get('score', 0), 2),  # 保留2位小数
                    'total_score': round(performance.get('score', 0), 2),  # 兼容模板
                    'rating': performance.get('rating', 'N/A'),
                    # 从battle_data中正确提取数据
                    'demolition': battle_data.get('demolitions', battle_data.get('building_damage', 0)),
                    'kills': battle_data.get('kills', 0),
                    'player_damage': battle_data.get('player_damage', 0),
                    'assists': battle_data.get('assists', 0),
                    'healing': battle_data.get('healing', 0),
                    'damage_taken': battle_data.get('damage_taken', 0),
                    'heavy_injuries': battle_data.get('heavy_injuries', 0),
                    'battle_data': battle_data,
                    'bonus_items': performance.get('bonus_items', []),
                    'penalty_items': performance.get('penalty_items', [])
                }
                character_battles.append(battle_info)

        print(f"找到角色 {bound_character} 的 {len(character_battles)} 条战斗记录")

        # 计算统计数据
        stats = calculate_character_stats(character_battles, character_info)

        return render_template('personal_dashboard.html',
                             character=character_info,
                             battles=character_battles,
                             stats=stats)

    except Exception as e:
        print(f"获取个人信息失败: {e}")
        flash('获取个人信息失败', 'error')
        return redirect(url_for('index'))
    finally:
        return_db_connection(connection)

def calculate_character_stats(battles, character_info):
    """计算角色统计数据"""
    if not battles:
        return {
            'total_battles': 0,
            'avg_score': 0,
            'best_performance': None,
            'recent_trend': 'stable',
            'profession_rank': 'N/A',
            'avg_kills': 0,
            'avg_assists': 0,
            'avg_damage': 0,
            'avg_demolition': 0,
            'avg_healing': 0,
            'avg_heavy_injuries': 0,
            'suggestions': []
        }

    total_battles = len(battles)
    total_score = sum(float(battle.get('total_score', 0)) for battle in battles)
    avg_score = round(total_score / total_battles if total_battles > 0 else 0, 2)

    # 计算各项平均数据
    avg_kills = round(sum(battle.get('kills', 0) for battle in battles) / total_battles, 1)
    avg_assists = round(sum(battle.get('assists', 0) for battle in battles) / total_battles, 1)
    avg_damage = round(sum(battle.get('player_damage', 0) for battle in battles) / total_battles, 0)
    avg_demolition = round(sum(battle.get('demolition', 0) for battle in battles) / total_battles, 0)
    avg_healing = round(sum(battle.get('healing', 0) for battle in battles) / total_battles, 0)
    avg_heavy_injuries = round(sum(battle.get('heavy_injuries', 0) for battle in battles) / total_battles, 1)

    # 找出最佳表现
    best_battle = max(battles, key=lambda x: float(x.get('total_score', 0)))

    # 计算趋势（最近5场vs之前的平均分）
    recent_battles = battles[-5:] if len(battles) >= 5 else battles
    recent_avg = sum(float(b.get('total_score', 0)) for b in recent_battles) / len(recent_battles)

    if len(battles) > 5:
        earlier_battles = battles[:-5]
        earlier_avg = sum(float(b.get('total_score', 0)) for b in earlier_battles) / len(earlier_battles)
        if recent_avg > earlier_avg * 1.1:
            trend = 'improving'
        elif recent_avg < earlier_avg * 0.9:
            trend = 'declining'
        else:
            trend = 'stable'
    else:
        trend = 'stable'

    # 生成个性化建议
    suggestions = generate_personalized_suggestions(character_info, battles, {
        'avg_score': avg_score,
        'avg_kills': avg_kills,
        'avg_assists': avg_assists,
        'avg_damage': avg_damage,
        'avg_demolition': avg_demolition,
        'avg_healing': avg_healing,
        'avg_heavy_injuries': avg_heavy_injuries,
        'total_battles': total_battles,
        'recent_trend': trend
    })

    return {
        'total_battles': total_battles,
        'avg_score': avg_score,
        'best_performance': best_battle,
        'recent_trend': trend,
        'profession_rank': 'A',  # 这里可以根据实际算法计算
        'avg_kills': avg_kills,
        'avg_assists': avg_assists,
        'avg_damage': avg_damage,
        'avg_demolition': avg_demolition,
        'avg_healing': avg_healing,
        'avg_heavy_injuries': avg_heavy_injuries,
        'suggestions': suggestions
    }

def generate_personalized_suggestions(character_info, battles, stats):
    """生成个性化建议"""
    import random
    suggestions = []

    if not battles:
        no_battle_suggestions = [
            "🎯 建议多参与帮战，积累战斗经验！实战是最好的老师",
            "📚 可以观看其他成员的战斗录像学习技巧和走位",
            "💪 先从简单的帮战开始，逐步提升战斗技能",
            "🤝 多与老成员交流，了解各职业的战斗特点",
            "⚔️ 熟悉自己职业的技能连招，为实战做好准备"
        ]
        suggestions.extend(random.sample(no_battle_suggestions, min(3, len(no_battle_suggestions))))
        return suggestions

    profession = character_info.get('profession', '')
    avg_score = stats['avg_score']
    avg_kills = stats['avg_kills']
    avg_assists = stats['avg_assists']
    avg_damage = stats['avg_damage']
    avg_demolition = stats['avg_demolition']
    avg_healing = stats['avg_healing']
    avg_heavy_injuries = stats['avg_heavy_injuries']
    total_battles = stats['total_battles']
    recent_trend = stats['recent_trend']

    # 基于总体表现的丰富建议
    if avg_score < 30:
        performance_suggestions = [
            "💪 当前表现需要大幅提升，建议从基础操作开始练习",
            "🎯 专注于提升单项技能，比如走位、技能释放时机等",
            "📖 建议观看高手视频，学习基本的战斗思路",
            "🤝 多与队友配合，先从简单的团队战术开始"
        ]
        suggestions.append(random.choice(performance_suggestions))
    elif avg_score < 50:
        performance_suggestions = [
            "📈 表现正在进步，继续保持练习的节奏！",
            "🎮 建议多尝试不同的战斗策略，找到适合自己的风格",
            "💡 注意观察敌方的行为模式，提前做出应对",
            "⚡ 提升反应速度，多练习技能连招的流畅度"
        ]
        suggestions.append(random.choice(performance_suggestions))
    elif avg_score < 70:
        performance_suggestions = [
            "👍 表现稳定，已经掌握了基本技巧！",
            "🚀 可以尝试更高难度的战术配合",
            "🎯 专注于提升某个特定方面，比如团队意识或个人操作",
            "📊 分析自己的战斗数据，找出还能改进的地方"
        ]
        suggestions.append(random.choice(performance_suggestions))
    elif avg_score < 85:
        performance_suggestions = [
            "🌟 表现优秀，已经是团队的中坚力量！",
            "🎖️ 可以考虑承担更多的团队责任",
            "📚 分享经验给新成员，帮助团队整体提升",
            "🔥 向顶尖水平冲刺，追求完美的战斗表现"
        ]
        suggestions.append(random.choice(performance_suggestions))
    else:
        performance_suggestions = [
            "👑 表现卓越，是团队的王牌选手！",
            "🏆 可以考虑指导其他成员，传授战斗技巧",
            "💎 保持这种高水平，继续引领团队前进",
            "🌈 尝试创新战术，为团队带来新的战斗思路"
        ]
        suggestions.append(random.choice(performance_suggestions))

    # 基于趋势的丰富建议
    if recent_trend == 'improving':
        trend_suggestions = [
            "📈 最近表现呈上升趋势，说明训练方法很有效！",
            "🔥 状态正佳，继续保持这种进步的势头！",
            "⭐ 进步明显，可以尝试挑战更高难度的对手",
            "🎯 趋势很好，建议制定更高的目标继续冲刺"
        ]
        suggestions.append(random.choice(trend_suggestions))
    elif recent_trend == 'declining':
        trend_suggestions = [
            "📉 最近表现有所下滑，建议调整训练方式",
            "🔄 可能需要休息调整，避免过度疲劳",
            "💭 反思最近的战斗，找出问题所在",
            "🎮 尝试改变战斗风格，可能会有新的突破"
        ]
        suggestions.append(random.choice(trend_suggestions))

    # 基于职业的专业建议（大幅丰富）
    if profession == '素问':
        suwen_suggestions = []
        if avg_healing < 30000:
            suwen_suggestions.extend([
                "💊 治疗量偏低，建议多关注队友血量，及时施展治疗技能",
                "🌿 学会预判伤害，提前给队友加血而不是等残血再救",
                "💚 合理分配治疗资源，优先保护核心输出队友"
            ])
        elif avg_healing < 80000:
            suwen_suggestions.extend([
                "💊 治疗量不错，可以尝试更精准的治疗时机",
                "🌸 学会在保证治疗的同时，适当输出增加团队伤害",
                "💫 掌握群体治疗的最佳时机，一次救多个队友"
            ])
        else:
            suwen_suggestions.extend([
                "🌟 治疗量优秀！你是团队的生命守护者",
                "👑 可以考虑指导其他辅助职业的治疗技巧",
                "💎 尝试更高难度的治疗挑战，比如极限救人"
            ])

        if avg_heavy_injuries > 3:
            suwen_suggestions.extend([
                "🛡️ 死亡次数较多，注意走位和自保技能的使用",
                "🌿 羽化技能要用在关键时刻，不要浪费保命机会",
                "👥 多躲在队友身后，避免成为敌方的首要目标"
            ])
        elif avg_heavy_injuries < 1:
            suwen_suggestions.extend([
                "🏆 生存能力极强！完美的辅助表现",
                "💫 可以尝试更激进的治疗策略，因为你的生存很有保障"
            ])

        general_suwen = [
            "🌿 素问的核心是团队意识，时刻关注队友状态",
            "💧 合理使用清心技能，为团队提供持续支援",
            "🌸 学会在安全位置施展技能，避免被集火",
            "💚 掌握不同情况下的治疗优先级"
        ]
        suwen_suggestions.extend(random.sample(general_suwen, min(2, len(general_suwen))))
        suggestions.extend(random.sample(suwen_suggestions, min(3, len(suwen_suggestions))))

    elif profession == '铁衣':
        tieyi_suggestions = []
        if avg_heavy_injuries > 4:
            tieyi_suggestions.extend([
                "🛡️ 作为前排，死亡次数过多，需要提升生存技巧",
                "⚔️ 学会合理使用防御技能，不要硬抗所有伤害",
                "🏰 掌握进退时机，该撤退时要果断后撤"
            ])
        elif avg_heavy_injuries < 2:
            tieyi_suggestions.extend([
                "🏆 生存能力出色！真正的钢铁战士",
                "💪 可以尝试更激进的前排策略",
                "🛡️ 你的坚韧为团队提供了强大的保障"
            ])

        if avg_damage < 50000:
            tieyi_suggestions.extend([
                "⚔️ 输出偏低，铁衣也需要一定的威胁性",
                "💥 学会在保证生存的同时，寻找输出机会",
                "🎯 掌握技能连招，提升单次爆发伤害"
            ])

        general_tieyi = [
            "🛡️ 铁衣的职责是保护队友，承担前排压力",
            "⚔️ 学会控制战场节奏，引导敌方攻击方向",
            "🏰 掌握团战站位，既要保护后排又要威胁敌方",
            "💪 合理使用嘲讽技能，分散敌方火力"
        ]
        tieyi_suggestions.extend(random.sample(general_tieyi, min(2, len(general_tieyi))))
        suggestions.extend(random.sample(tieyi_suggestions, min(3, len(tieyi_suggestions))))

    elif profession == '碎梦':
        suimeng_suggestions = []
        if avg_kills < 2:
            suimeng_suggestions.extend([
                "⚔️ 击杀数偏低，碎梦需要更强的收割能力",
                "🗡️ 学会寻找残血敌人，提升击杀效率",
                "💀 掌握爆发连招，一套技能秒杀脆皮"
            ])
        elif avg_kills > 5:
            suimeng_suggestions.extend([
                "👑 击杀能力出众！真正的战场收割者",
                "🏆 可以考虑指导其他输出职业的击杀技巧",
                "💎 尝试更高难度的击杀挑战"
            ])

        if avg_assists < 3:
            suimeng_suggestions.extend([
                "🤝 助攻数较低，需要更多的团队配合",
                "⚡ 学会与队友形成连招，提升团队伤害",
                "🎯 不要只想着单杀，团队配合更重要"
            ])

        general_suimeng = [
            "🗡️ 碎梦的核心是爆发和机动性",
            "💀 学会选择合适的切入时机，避免被集火",
            "⚡ 掌握技能连招的最佳释放顺序",
            "🌙 利用隐身和位移技能，创造击杀机会"
        ]
        suimeng_suggestions.extend(random.sample(general_suimeng, min(2, len(general_suimeng))))
        suggestions.extend(random.sample(suimeng_suggestions, min(3, len(suimeng_suggestions))))

    elif profession in ['血河', '龙吟']:
        dps_suggestions = []
        if avg_damage < 80000:
            dps_suggestions.extend([
                "💥 输出伤害偏低，需要提升技能连招和装备搭配",
                "🔥 学会抓住敌方破绽，集中火力快速击杀",
                "⚡ 掌握爆发时机，在团战中发挥最大伤害"
            ])
        elif avg_damage > 200000:
            dps_suggestions.extend([
                "👑 输出能力卓越！团队的主要火力输出",
                "🏆 可以指导其他输出职业提升伤害技巧",
                "💎 尝试更复杂的输出循环，追求极限伤害"
            ])

        if avg_kills < 2:
            dps_suggestions.extend([
                "🎯 击杀数偏低，需要提升收割能力",
                "💀 学会补刀技巧，不要让残血敌人逃脱",
                "⚔️ 掌握技能的击杀阈值，精准收割"
            ])

        general_dps = [
            f"🔥 {profession}的核心是持续高输出",
            "💥 学会在保证安全的前提下最大化输出",
            "🎯 掌握技能优先级，合理分配伤害",
            "⚡ 注意走位，避免被敌方集火秒杀"
        ]
        dps_suggestions.extend(random.sample(general_dps, min(2, len(general_dps))))
        suggestions.extend(random.sample(dps_suggestions, min(3, len(dps_suggestions))))

    elif profession == '潮光':
        chaoguang_suggestions = []
        if avg_demolition < 20000:
            chaoguang_suggestions.extend([
                "🏗️ 拆塔数据偏低，潮光应该重视建筑物攻击",
                "🏰 学会在安全情况下优先攻击敌方建筑",
                "💧 合理使用清泉技能，既能输出又能拆塔"
            ])
        elif avg_demolition > 80000:
            chaoguang_suggestions.extend([
                "🏆 拆塔能力出色！真正的建筑克星",
                "💎 可以承担更多的拆塔责任",
                "🌟 你的拆塔为团队提供了巨大优势"
            ])

        general_chaoguang = [
            "💧 潮光需要平衡输出和辅助功能",
            "🌊 清泉技能的使用时机很关键",
            "🏗️ 在团战中要兼顾人员和建筑目标",
            "⚡ 掌握远程输出的安全距离"
        ]
        chaoguang_suggestions.extend(random.sample(general_chaoguang, min(2, len(general_chaoguang))))
        suggestions.extend(random.sample(chaoguang_suggestions, min(3, len(chaoguang_suggestions))))

    # 添加更多职业的建议...
    elif profession in ['九灵', '玄机', '神相', '沧澜']:
        other_suggestions = [
            f"⚔️ {profession}有独特的战斗风格，多探索不同的战术组合",
            f"🎯 掌握{profession}的核心技能，发挥职业特色",
            f"🤝 学会与其他职业配合，形成有效的团队战术",
            f"📈 持续练习{profession}的技能连招，提升操作熟练度"
        ]
        suggestions.extend(random.sample(other_suggestions, min(2, len(other_suggestions))))

    # 基于具体数据的丰富建议
    data_suggestions = []

    if avg_demolition > 0 and avg_demolition < 15000:
        data_suggestions.extend([
            "🏗️ 拆塔数据有提升空间，战斗中多关注建筑物",
            "🏰 学会在安全时机攻击敌方防御建筑",
            "💥 拆塔也是获得优势的重要途径"
        ])
    elif avg_demolition > 50000:
        data_suggestions.append("🏆 拆塔能力出众！为团队创造了巨大优势")

    if avg_assists < 2:
        data_suggestions.extend([
            "🤝 助攻数较低，需要更多的团队配合意识",
            "⚡ 学会与队友形成连招，提升团队整体伤害",
            "🎯 不要单打独斗，团队配合才是胜利关键"
        ])
    elif avg_assists > 8:
        data_suggestions.append("👥 团队配合意识极强！真正的团队核心")

    if avg_heavy_injuries > 4:
        data_suggestions.extend([
            "💀 死亡次数过多，需要提升生存意识",
            "🛡️ 学会合理走位，避免被敌方集火",
            "⚡ 掌握技能的使用时机，关键时刻保命要紧"
        ])
    elif avg_heavy_injuries < 1:
        data_suggestions.append("🏆 生存能力极强！几乎不死的战神")

    # 战斗频率和经验建议
    experience_suggestions = []
    if total_battles < 3:
        experience_suggestions.extend([
            "📅 建议增加参战频率，实战是最好的老师",
            "🎮 多参与不同类型的战斗，积累经验",
            "🤝 观察老成员的战斗方式，学习实战技巧"
        ])
    elif total_battles < 10:
        experience_suggestions.extend([
            "📈 战斗经验正在积累，继续保持参战积极性",
            "🎯 可以尝试不同的战斗策略，找到适合自己的风格",
            "💡 总结每次战斗的得失，持续改进"
        ])
    elif total_battles >= 20:
        experience_suggestions.extend([
            "🏆 战斗经验丰富，是团队的老兵！",
            "👨‍🏫 可以考虑指导新成员，分享实战经验",
            "💎 尝试更高难度的战术挑战"
        ])

    # 随机添加一些通用建议
    general_suggestions = [
        "🎯 保持积极的战斗心态，每次失败都是学习机会",
        "⚡ 多观察敌方的行为模式，提前做出应对",
        "🤝 加强与队友的沟通，团队配合是胜利关键",
        "📊 定期回顾自己的战斗数据，找出改进方向",
        "🔥 保持对游戏的热情，享受每一次战斗",
        "💪 持续练习基本操作，熟练度决定发挥上限"
    ]

    # 从各类建议中随机选择，确保建议的多样性
    if data_suggestions:
        suggestions.extend(random.sample(data_suggestions, min(2, len(data_suggestions))))
    if experience_suggestions:
        suggestions.extend(random.sample(experience_suggestions, min(1, len(experience_suggestions))))

    suggestions.extend(random.sample(general_suggestions, min(2, len(general_suggestions))))

    # 确保建议数量合理（5-8条）
    if len(suggestions) > 8:
        suggestions = random.sample(suggestions, 8)
    elif len(suggestions) < 5:
        suggestions.extend(random.sample(general_suggestions, 5 - len(suggestions)))

    return suggestions

# 通知系统相关函数
def create_notification(guild_id, title, message, notification_type='info', target_roles=None):
    """创建通知"""
    if target_roles is None:
        target_roles = ['guild_leader']  # 默认发送给当家

    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()

        # 创建通知记录
        cursor.execute("""
            INSERT INTO notifications (guild_id, title, message, type, target_roles, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
        """, (guild_id, title, message, notification_type, ','.join(target_roles)))

        connection.commit()
        return True

    except Exception as e:
        print(f"创建通知失败: {e}")
        # 如果表不存在，尝试创建
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    guild_id VARCHAR(50),
                    title VARCHAR(200),
                    message TEXT,
                    type VARCHAR(20) DEFAULT 'info',
                    target_roles TEXT,
                    is_read BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    read_at TIMESTAMP NULL
                )
            """)
            connection.commit()

            # 重新尝试插入
            cursor.execute("""
                INSERT INTO notifications (guild_id, title, message, type, target_roles, created_at)
                VALUES (%s, %s, %s, %s, %s, NOW())
            """, (guild_id, title, message, notification_type, ','.join(target_roles)))
            connection.commit()
            return True

        except Exception as e2:
            print(f"创建通知表失败: {e2}")
            return False
    finally:
        return_db_connection(connection)

def get_user_notifications(user_role, guild_id, limit=10):
    """获取用户通知"""
    connection = get_db_connection()
    if not connection:
        return []

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        cursor.execute("""
            SELECT * FROM notifications
            WHERE guild_id = %s AND FIND_IN_SET(%s, target_roles) > 0
            ORDER BY created_at DESC
            LIMIT %s
        """, (guild_id, user_role, limit))

        return cursor.fetchall()

    except Exception as e:
        print(f"获取通知失败: {e}")
        return []
    finally:
        return_db_connection(connection)

@app.route('/user_profile', methods=['GET', 'POST'])
@login_required
def user_profile():
    """用户个人设置页面"""
    user = get_current_user()
    if not user or user['role'] == 'guest':
        flash('请先注册登录', 'error')
        return redirect(url_for('login'))

    # 获取用户ID和绑定角色
    connection = get_db_connection()
    if not connection:
        flash('数据库连接失败', 'error')
        return redirect(url_for('index'))

    try:
        cursor = connection.cursor()
        cursor.execute("SELECT id FROM users WHERE username = %s", (session['user_id'],))
        result = cursor.fetchone()
        if not result:
            flash('用户信息错误', 'error')
            return redirect(url_for('index'))

        user_id = result[0]
        bound_character = get_user_bound_character(user_id)

        # 获取角色详细信息
        character_info = None
        if bound_character:
            members = load_members()
            for member in members:
                if member['name'] == bound_character:
                    character_info = member
                    break

        if request.method == 'POST':
            action = request.form.get('action')

            if action == 'change_password':
                current_password = request.form.get('current_password')
                new_password = request.form.get('new_password')
                confirm_password = request.form.get('confirm_password')

                # 验证当前密码
                if not verify_password(current_password, user['password']):
                    flash('当前密码错误', 'error')
                elif new_password != confirm_password:
                    flash('新密码确认不匹配', 'error')
                elif len(new_password) < 6:
                    flash('新密码长度至少6位', 'error')
                else:
                    # 更新密码
                    new_password_hash = hash_password(new_password)
                    cursor.execute("""
                        UPDATE users SET password_hash = %s WHERE username = %s
                    """, (new_password_hash, session['user_id']))

                    # 清除用户缓存
                    cache.delete('users')

                    flash('密码修改成功', 'success')
                    return redirect(url_for('user_profile'))

            elif action == 'change_character_name':
                new_character_name = request.form.get('new_character_name', '').strip()

                if not new_character_name:
                    flash('请输入新的角色名', 'error')
                elif new_character_name == bound_character:
                    flash('新角色名与当前角色名相同', 'error')
                else:
                    # 检查新角色名是否已存在
                    members = load_members()
                    name_exists = any(member['name'] == new_character_name for member in members)

                    if name_exists:
                        flash('该角色名已存在', 'error')
                    else:
                        # 更新成员表中的角色名
                        cursor.execute("""
                            UPDATE guild_members SET name = %s
                            WHERE name = %s AND guild_id = %s
                        """, (new_character_name, bound_character, user['guild_id']))

                        # 更新角色绑定表中的角色名
                        cursor.execute("""
                            UPDATE character_bindings SET character_name = %s
                            WHERE character_name = %s AND user_id = %s AND status = 'approved'
                        """, (new_character_name, bound_character, user_id))

                        # 清除相关缓存
                        if user.get('guild_id'):
                            cache_key = f'members_{user["guild_id"]}'
                            cache.delete(cache_key)

                        # 发送通知给当家
                        create_notification(
                            guild_id=user['guild_id'],
                            title="成员角色名修改",
                            message=f"成员 {user['name']} 将角色名从 \"{bound_character}\" 修改为 \"{new_character_name}\"",
                            notification_type='info'
                        )

                        flash(f'角色名已从 "{bound_character}" 修改为 "{new_character_name}"', 'success')
                        return redirect(url_for('user_profile'))

            elif action == 'change_profession':
                new_profession = request.form.get('new_profession', '').strip()

                if not new_profession:
                    flash('请选择新的职业', 'error')
                elif not bound_character:
                    flash('您还没有绑定角色', 'error')
                else:
                    # 职业列表验证
                    valid_professions = ['素问', '潮光', '九灵', '铁衣', '玄机', '龙吟', '血河', '神相', '碎梦', '沧澜']
                    if new_profession not in valid_professions:
                        flash('无效的职业选择', 'error')
                    else:
                        old_profession = character_info.get('profession', '未知') if character_info else '未知'

                        # 更新成员表中的职业
                        cursor.execute("""
                            UPDATE guild_members SET profession = %s
                            WHERE name = %s AND guild_id = %s
                        """, (new_profession, bound_character, user['guild_id']))

                        # 清除相关缓存
                        if user.get('guild_id'):
                            cache_key = f'members_{user["guild_id"]}'
                            cache.delete(cache_key)

                        # 发送通知给当家
                        create_notification(
                            guild_id=user['guild_id'],
                            title="成员职业修改",
                            message=f"成员 {user['name']} (角色: {bound_character}) 将职业从 \"{old_profession}\" 修改为 \"{new_profession}\"",
                            notification_type='info'
                        )

                        flash(f'角色 "{bound_character}" 的职业已从 "{old_profession}" 修改为 "{new_profession}"', 'success')
                        return redirect(url_for('user_profile'))

        return render_template('user_profile.html',
                             user=user,
                             bound_character=bound_character,
                             character_info=character_info)

    except Exception as e:
        print(f"用户设置页面错误: {e}")
        flash('操作失败', 'error')
        return redirect(url_for('index'))
    finally:
        return_db_connection(connection)

@app.route('/notifications')
@admin_required
def notifications():
    """通知页面 - 只有当家可以查看"""
    user = get_current_user()
    if not user or not user.get('guild_id'):
        flash('无法获取帮会信息', 'error')
        return redirect(url_for('index'))

    # 获取通知
    notifications = get_user_notifications(user['role'], user['guild_id'], limit=50)

    return render_template('notifications.html', notifications=notifications)

@app.route('/mark_notification_read/<int:notification_id>')
@admin_required
def mark_notification_read(notification_id):
    """标记通知为已读"""
    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()
            cursor.execute("""
                UPDATE notifications SET is_read = TRUE, read_at = NOW()
                WHERE id = %s
            """, (notification_id,))
            connection.commit()
        except Exception as e:
            print(f"标记通知已读失败: {e}")
        finally:
            return_db_connection(connection)

    return redirect(url_for('notifications'))

@app.route('/admin_reset_password/<username>')
@admin_required
def admin_reset_password(username):
    """管理员重置用户密码"""
    # 生成临时密码
    import random
    import string
    temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))

    connection = get_db_connection()
    if connection:
        try:
            cursor = connection.cursor()
            temp_password_hash = hash_password(temp_password)
            cursor.execute("""
                UPDATE users SET password_hash = %s WHERE username = %s
            """, (temp_password_hash, username))
            connection.commit()

            # 清除用户缓存
            cache.delete('users')

            # 创建通知告知用户新密码
            user = get_current_user()
            if user and user.get('guild_id'):
                create_notification(
                    guild_id=user['guild_id'],
                    title="密码重置完成",
                    message=f"管理员已为用户 {username} 重置密码。临时密码：{temp_password}（请及时通知用户修改）",
                    notification_type='success'
                )

            flash(f'用户 {username} 的密码已重置。临时密码：{temp_password}', 'success')

        except Exception as e:
            print(f"管理员重置密码失败: {e}")
            flash('密码重置失败', 'error')
        finally:
            return_db_connection(connection)
    else:
        flash('数据库连接失败', 'error')

    return redirect(url_for('notifications'))

@app.route('/forgot_password', methods=['GET', 'POST'])
def forgot_password():
    """忘记密码页面"""
    if request.method == 'POST':
        action = request.form.get('action', 'verify_character')
        username = request.form.get('username', '').strip()

        if not username:
            flash('请输入用户名', 'error')
            return render_template('forgot_password.html')

        # 检查用户是否存在
        users = load_users()
        if username not in users:
            flash('用户名不存在', 'error')
            return render_template('forgot_password.html')

        user = users[username]

        if action == 'verify_character':
            character_name = request.form.get('character_name', '').strip()

            if not character_name:
                flash('请输入角色名', 'error')
                return render_template('forgot_password.html', username=username, step='verify')

            # 获取用户ID和绑定角色
            connection = get_db_connection()
            if connection:
                try:
                    cursor = connection.cursor()
                    cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
                    result = cursor.fetchone()
                    if result:
                        user_id = result[0]
                        bound_character = get_user_bound_character(user_id)

                        if bound_character and bound_character == character_name:
                            # 验证成功，允许重置密码
                            return render_template('forgot_password.html',
                                                 username=username,
                                                 step='reset',
                                                 verified=True)
                        else:
                            flash('角色名验证失败', 'error')
                            return render_template('forgot_password.html',
                                                 username=username,
                                                 step='verify',
                                                 show_admin_option=True)
                finally:
                    return_db_connection(connection)

            flash('验证过程出现错误', 'error')
            return render_template('forgot_password.html')

        elif action == 'reset_password':
            new_password = request.form.get('new_password', '')
            confirm_password = request.form.get('confirm_password', '')
            verified = request.form.get('verified') == 'true'

            if not verified:
                flash('未通过验证，无法重置密码', 'error')
                return redirect(url_for('forgot_password'))

            if not new_password or len(new_password) < 6:
                flash('新密码长度至少6位', 'error')
                return render_template('forgot_password.html',
                                     username=username,
                                     step='reset',
                                     verified=True)

            if new_password != confirm_password:
                flash('密码确认不匹配', 'error')
                return render_template('forgot_password.html',
                                     username=username,
                                     step='reset',
                                     verified=True)

            # 更新密码
            connection = get_db_connection()
            if connection:
                try:
                    cursor = connection.cursor()
                    new_password_hash = hash_password(new_password)
                    cursor.execute("""
                        UPDATE users SET password_hash = %s WHERE username = %s
                    """, (new_password_hash, username))
                    connection.commit()

                    # 清除用户缓存
                    cache.delete('users')

                    flash('密码重置成功，请使用新密码登录', 'success')
                    return redirect(url_for('login'))

                except Exception as e:
                    print(f"重置密码失败: {e}")
                    flash('密码重置失败，请稍后重试', 'error')
                finally:
                    return_db_connection(connection)

        elif action == 'request_admin_reset':
            reason = request.form.get('reason', '').strip()

            if not reason:
                flash('请填写申请理由', 'error')
                return render_template('forgot_password.html',
                                     username=username,
                                     step='verify',
                                     show_admin_option=True)

            # 创建管理员重置申请通知
            if user.get('guild_id'):
                success = create_notification(
                    guild_id=user['guild_id'],
                    title="密码重置申请",
                    message=f"用户 {username} 申请重置密码。理由：{reason}",
                    notification_type='warning'
                )

                if success:
                    flash('密码重置申请已提交，请等待管理员处理', 'info')
                    return redirect(url_for('login'))
                else:
                    flash('申请提交失败，请稍后重试', 'error')
            else:
                flash('无法确定您的帮会信息，请联系超级管理员', 'error')

    return render_template('forgot_password.html')

@app.route('/admin/clean_duplicates')
@admin_required
def clean_duplicates():
    """清理重复成员数据"""
    success = clean_duplicate_members()
    if success:
        flash('重复成员数据清理完成', 'success')
    else:
        flash('清理失败', 'error')
    return redirect(url_for('index'))

@app.route('/admin/create_roster_snapshot')
@admin_required
def create_manual_roster_snapshot():
    """手动创建排表快照"""
    success = create_roster_snapshot(snapshot_type='manual', notes='手动创建的排表快照')
    if success:
        flash('排表快照创建成功', 'success')
    else:
        flash('排表快照创建失败', 'error')
    return redirect(url_for('index'))

@app.route('/admin/roster_snapshots')
@admin_required
def view_roster_snapshots():
    """查看排表快照列表"""
    user = get_current_user()
    if not user or not user.get('guild_id'):
        flash('无法获取帮会信息', 'error')
        return redirect(url_for('index'))

    guild_id = user['guild_id']
    connection = get_db_connection()
    snapshots = []

    if connection:
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT rs.*, u.username as creator_name
                FROM roster_snapshots rs
                LEFT JOIN users u ON rs.created_by = u.id
                WHERE rs.guild_id = %s
                ORDER BY rs.snapshot_time DESC
                LIMIT 20
            """, (guild_id,))
            snapshots = cursor.fetchall()

            # 解析成员数据统计
            for snapshot in snapshots:
                try:
                    member_data = json.loads(snapshot['member_data'])
                    snapshot['member_count'] = len(member_data)

                    # 统计各团人数
                    team_stats = {}
                    for member in member_data:
                        main_group = member.get('main_group', '其他团')
                        if main_group not in team_stats:
                            team_stats[main_group] = 0
                        team_stats[main_group] += 1
                    snapshot['team_stats'] = team_stats
                except:
                    snapshot['member_count'] = 0
                    snapshot['team_stats'] = {}

        except Exception as e:
            print(f"获取快照列表失败: {e}")
            flash('获取快照列表失败', 'error')
        finally:
            return_db_connection(connection)

    return render_template('admin_roster_snapshots.html', snapshots=snapshots)

@app.route('/')
@guest_allowed
def index():
    """主页 - 根据用户角色显示不同内容"""
    user = get_current_user()

    # 游客显示专门的搜索页面
    if user and user['role'] == 'guest':
        return render_template('guest_index.html')

    # 超级管理员看到帮会管理界面
    if user and user['role'] == 'super_admin':
        return redirect(url_for('super_admin_dashboard'))

    # 普通用户没有帮会时，引导到帮会申请页面
    if user and user['role'] == 'user' and not user.get('guild_id'):
        return redirect(url_for('guild_application'))

    # 检查用户是否有绑定的角色，如果有则显示个人首页
    if user and user['role'] == 'user' and user.get('guild_id'):
        # 获取用户ID
        connection = get_db_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT id FROM users WHERE username = %s", (session['user_id'],))
                result = cursor.fetchone()
                if result:
                    user_id = result[0]
                    bound_character = get_user_bound_character(user_id)
                    if bound_character:
                        # 用户有绑定角色，显示个人首页
                        return redirect(url_for('personal_dashboard'))
            finally:
                return_db_connection(connection)

    # 获取当前用户的帮会信息
    guild_info = None
    if user and user.get('guild_id'):
        guilds = load_guilds()
        guild_info = guilds.get(user['guild_id'])

    # 帮会大当家和有帮会的用户看到帮会内部管理界面
    members = load_members()  # 会自动获取当前用户的guild_id
    battle_records = load_battle_records()  # 会自动获取当前用户的guild_id
    organization = get_organization_structure(members)

    # 基础统计 - 排除帮外成员
    guild_members = [m for m in members if not (m.get('main_group') == '其他团' and (m.get('sub_team') == '帮外' or m.get('squad') == '帮外'))]
    external_members = [m for m in members if m.get('main_group') == '其他团' and (m.get('sub_team') == '帮外' or m.get('squad') == '帮外')]

    total_members = len(guild_members)  # 只统计帮会成员
    external_count = len(external_members)  # 帮外成员单独统计
    active_members = len([m for m in guild_members if m.get('status', '主力') == '主力'])
    substitute_members = len([m for m in guild_members if m.get('status', '主力') == '替补'])

    # 职业统计
    profession_stats = {}
    for member in members:
        prof = member['profession']
        profession_stats[prof] = profession_stats.get(prof, 0) + 1

    # 战斗统计
    battle_count = len(battle_records)
    latest_battle = battle_records[-1] if battle_records else None

    # 计算参战成员和平均评分
    participated_members = 0
    total_scores = []

    if battle_records:
        # 获取所有参战成员
        all_participants = set()
        for record in battle_records:
            member_performance = record.get('member_performance', {})
            for member_name, performance in member_performance.items():
                if performance.get('battle_data'):  # 有战斗数据说明参战了
                    all_participants.add(member_name)
                    score = performance.get('score', 0)
                    if score > 0:
                        total_scores.append(score)

        participated_members = len(all_participants)

    participation_rate = round((participated_members / total_members * 100) if total_members > 0 else 0)
    avg_score = sum(total_scores) / len(total_scores) if total_scores else 0

    # 获取表现最佳的成员
    top_performers = get_top_performers(battle_records, limit=5)

    # 生成最近活动
    recent_activities = generate_recent_activities(battle_records, members)

    return render_template('index.html',
                         members=members,
                         organization=organization,
                         profession_stats=profession_stats,
                         total_members=total_members,
                         external_members=external_count,
                         active_members=active_members,
                         substitute_members=substitute_members,
                         battle_count=battle_count,
                         latest_battle=latest_battle,
                         participated_members=participated_members,
                         participation_rate=participation_rate,
                         avg_score=avg_score,
                         top_performers=top_performers,
                         recent_activities=recent_activities,
                         guild_info=guild_info)

@app.route('/members')
@guest_allowed
def members_list():
    """成员列表页面"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)  # 每页显示20个成员
    search = request.args.get('search', '', type=str)

    members = load_members()  # 会自动获取当前用户的guild_id

    # 🔒 游客搜索限制检查
    user = get_current_user()
    search_error = None

    if search and user and user['role'] == 'guest':
        # 检查是否为职业名搜索（批量搜索）
        professions = ['素问', '九灵', '潮光', '血河', '神相', '玄机', '铁衣', '龙吟', '碎梦', '沧澜']
        team_keywords = ['进攻', '防守', '一团', '二团', '三团', '四团', '五团', '替补', '请假']

        search_lower = search.lower().strip()

        # 如果搜索词是职业名或团队关键词，拒绝搜索
        if (search_lower in [p.lower() for p in professions] or
            any(keyword in search_lower for keyword in team_keywords)):
            search_error = '游客只能搜索具体的角色名称，不支持职业或团队批量搜索'
            search = ''  # 清空搜索词
        elif len(search_lower) < 2:
            search_error = '搜索词至少需要2个字符'
            search = ''  # 清空搜索词

    # 应用搜索筛选
    filtered_members = []
    for member in members:
        # 搜索筛选
        if search:
            search_lower = search.lower()
            # 游客只能搜索角色名
            if user and user['role'] == 'guest':
                if not (search_lower in member['name'].lower()):
                    continue
            else:
                # 注册用户可以搜索所有字段
                if not (search_lower in member['name'].lower() or
                       search_lower in member['profession'].lower() or
                       search_lower in member.get('main_group', '').lower() or
                       search_lower in member.get('sub_team', '').lower() or
                       search_lower in member.get('squad', '').lower()):
                    continue
        filtered_members.append(member)

    # 按名字排序
    filtered_members.sort(key=lambda x: x['name'])

    # 计算分页
    total_members = len(filtered_members)
    total_pages = (total_members + per_page - 1) // per_page
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_members = filtered_members[start_idx:end_idx]

    # 获取三层级组织结构（用于统计）
    organization = get_organization_structure(members)

    # 动态获取所有主分组（包括自定义的）
    all_main_groups = set()

    # 1. 从成员数据中获取主分组
    for member in members:
        main_group = member.get('main_group', '其他团')
        all_main_groups.add(main_group)

    # 2. 从数据库配置中获取主分组
    try:
        user = get_current_user()
        if user and user.get('guild_id'):
            connection = get_db_connection()
            if connection:
                cursor = connection.cursor()
                cursor.execute("""
                    SELECT DISTINCT main_group FROM guild_team_names
                    WHERE guild_id = %s
                """, (user['guild_id'],))

                db_main_groups = cursor.fetchall()
                for row in db_main_groups:
                    all_main_groups.add(row[0])

                return_db_connection(connection)
    except Exception as e:
        print(f"⚠️ 成员页面加载数据库主分组失败: {e}")

    # 3. 确保基础分组存在
    all_main_groups.update(['进攻团', '防守团', '其他团'])

    # 4. 按照指定顺序排序主分组
    ordered_main_groups = []
    # 首先添加基础分组（按指定顺序）
    for base_group in ['进攻团', '防守团', '其他团']:
        if base_group in all_main_groups:
            ordered_main_groups.append(base_group)
    # 然后添加自定义分组（按字母顺序）
    custom_groups = sorted([g for g in all_main_groups if g not in ['进攻团', '防守团', '其他团']])
    ordered_main_groups.extend(custom_groups)

    # 为了兼容，也保留旧的team分组（用于统计）
    teams = {
        '1团': [],
        '2团': [],
        '3团': [],
        '4团': [],
        '未分配': []
    }

    for member in members:
        team = member.get('team', '未分配')
        if team not in teams:
            teams[team] = []
        teams[team].append(member)

    # 按名字排序
    for team in teams:
        teams[team].sort(key=lambda x: x['name'])

    # 分页信息
    pagination = {
        'page': page,
        'per_page': per_page,
        'total': total_members,
        'total_pages': total_pages,
        'has_prev': page > 1,
        'has_next': page < total_pages,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if page < total_pages else None,
        'pages': list(range(max(1, page - 2), min(total_pages + 1, page + 3)))
    }

    print(f"📊 成员详情页面: 总成员{total_members}人，当前显示第{page}页")

    # 创建响应并添加防缓存头
    response = make_response(render_template('members.html',
                                           members=paginated_members,
                                           all_members=members,
                                           teams=teams,
                                           organization=organization,
                                           all_main_groups=ordered_main_groups,
                                           pagination=pagination,
                                           search=search,
                                           search_error=search_error))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@app.route('/teams')
def teams_view():
    """团队视图页面"""
    members = load_members()  # 会自动获取当前用户的guild_id
    stats = get_team_stats(members)
    
    # 按团队分组
    teams = {
        '1团': [],
        '2团': [],
        '3团': [],
        '4团': []
    }
    
    for member in members:
        team = member.get('team', '')
        if team in teams:
            teams[team].append(member)
    
    # 按职业排序
    for team in teams:
        teams[team].sort(key=lambda x: (x['profession'], x['name']))
    
    return render_template('teams.html', teams=teams, stats=stats)

@app.route('/edit_member/<member_name>')
def edit_member(member_name):
    """编辑成员页面"""
    members = load_members()  # 会自动获取当前用户的guild_id
    member = next((m for m in members if m['name'] == member_name), None)

    if not member:
        return redirect(url_for('members_list'))

    # 动态获取团队列表
    teams = set()
    squads = set()
    for m in members:
        if m.get('team') and m['team'] != '未分配':
            teams.add(m['team'])
        if m.get('squad') and m['squad'] != '未分配小队':
            squads.add(m['squad'])

    # 选项列表
    professions = ['素问', '潮光', '九灵', '铁衣', '玄机', '龙吟', '血河', '神相', '碎梦', '沧澜']
    teams = sorted(list(teams)) + ['新建团队']
    squads = sorted(list(squads)) + ['新建小队']
    positions = ['拆塔', '击杀', '人伤', '治疗', '扛伤']  # 更新为您要求的职责列表
    statuses = ['主力', '替补', '请假']

    return render_template('edit_member.html',
                         member=member,
                         professions=professions,
                         teams=teams,
                         squads=squads,
                         positions=positions,
                         statuses=statuses)

@app.route('/update_member', methods=['POST'])
@admin_required
def update_member():
    """更新成员信息"""
    members = load_members()  # 会自动获取当前用户的guild_id

    member_name = request.form['name']
    new_profession = request.form['profession']
    new_team = request.form['team']
    new_squad = request.form.get('squad', '未分配小队')
    new_position = request.form['position']
    new_status = request.form.get('status', '主力')

    # 处理新建团队
    if new_team == '新建团队':
        new_team_name = request.form.get('new_team_name', '').strip()
        if new_team_name:
            new_team = new_team_name

    # 处理新建小队
    if new_squad == '新建小队':
        new_squad_name = request.form.get('new_squad_name', '').strip()
        if new_squad_name:
            new_squad = new_squad_name

    # 查找并更新成员
    for member in members:
        if member['name'] == member_name:
            member['profession'] = new_profession
            member['team'] = new_team
            member['squad'] = new_squad
            member['position'] = new_position
            member['status'] = new_status
            break

    save_members(members)  # 会自动获取当前用户的guild_id
    return redirect(url_for('members_list'))

@app.route('/member_detail/<member_name>')
@guest_allowed
def member_detail(member_name):
    """成员详情页面 - 游客可以查看所有帮会成员的详情"""
    user = get_current_user()
    member = None

    # 游客可以查看所有帮会的成员
    if user and user['role'] == 'guest':
        all_members = load_all_guild_members()
        member = next((m for m in all_members if m['name'] == member_name), None)

        if not member:
            flash('未找到该成员信息', 'error')
            return redirect(url_for('index'))

        # 游客用户也可以查看完整的战斗数据，只是使用无导航栏的模板
        battle_history = get_member_battle_history(member_name, member.get('guild_id'))
        return render_template('guest_member_detail.html', member=member, battle_history=battle_history)
    else:
        # 登录用户只能查看自己帮会的成员
        members = load_members()
        member = next((m for m in members if m['name'] == member_name), None)

        if not member:
            return redirect(url_for('members_list'))

        # 获取该成员的战斗历史
        battle_history = get_member_battle_history(member_name, member.get('guild_id'))

        return render_template('member_detail.html',
                             member=member,
                             battle_history=battle_history)

def get_member_battle_history(member_name, guild_id=None):
    """获取成员的战斗历史记录"""
    try:
        # 获取当前用户信息
        user = get_current_user()
        is_guest = user and user.get('role') == 'guest'

        member_battles = []

        if is_guest:
            # 游客用户：搜索所有帮会的战斗记录
            all_battle_records = []

            # 从数据库加载所有帮会的战斗记录
            connection = get_db_connection()
            if connection:
                try:
                    cursor = connection.cursor(pymysql.cursors.DictCursor)
                    cursor.execute("""
                        SELECT * FROM battle_records
                        ORDER BY upload_time DESC
                    """)
                    db_records = cursor.fetchall()

                    # 转换为应用格式
                    for record in db_records:
                        member_performance = json.loads(record.get('member_performance', '{}'))
                        all_battle_records.append({
                            'battle_id': record['battle_id'],
                            'our_guild': record['our_guild'],
                            'enemy_guild': record['enemy_guild'],
                            'upload_time': record['upload_time'].isoformat() if record['upload_time'] else None,
                            'member_performance': member_performance
                        })

                    print(f"游客用户加载了 {len(all_battle_records)} 条战斗记录")

                except Exception as e:
                    print(f"从数据库加载所有战斗记录失败: {e}")
                finally:
                    return_db_connection(connection)

            # 在所有战斗记录中查找该成员
            for record in all_battle_records:
                member_performance = record.get('member_performance', {})
                if member_name in member_performance:
                    performance = member_performance[member_name]
                    battle_date = extract_battle_date(record)

                    battle_info = {
                        'battle_id': record['battle_id'],
                        'battle_date': battle_date,
                        'enemy_guild': record['enemy_guild'],
                        'score': performance.get('score', 0),
                        'rating': performance.get('rating', 'N/A'),
                        'battle_data': performance.get('battle_data', {}),
                        'bonus_items': performance.get('bonus_items', []),
                        'penalty_items': performance.get('penalty_items', []),
                        'details': performance.get('details', [])
                    }
                    member_battles.append(battle_info)
        else:
            # 注册用户：只查看指定帮会或自己帮会的战斗记录
            if guild_id:
                battle_records = load_battle_records_for_guild(guild_id)
            else:
                battle_records = load_battle_records()

            for record in battle_records:
                member_performance = record.get('member_performance', {})
                if member_name in member_performance:
                    performance = member_performance[member_name]
                    battle_date = extract_battle_date(record)

                    battle_info = {
                        'battle_id': record['battle_id'],
                        'battle_date': battle_date,
                        'enemy_guild': record['enemy_guild'],
                        'score': performance.get('score', 0),
                        'rating': performance.get('rating', 'N/A'),
                        'battle_data': performance.get('battle_data', {}),
                        'bonus_items': performance.get('bonus_items', []),
                        'penalty_items': performance.get('penalty_items', []),
                        'details': performance.get('details', [])
                    }
                    member_battles.append(battle_info)

        # 按日期排序（最早的在前）
        member_battles.sort(key=lambda x: x['battle_date'])

        print(f"为成员 {member_name} 找到 {len(member_battles)} 条战斗记录")
        return member_battles

    except Exception as e:
        print(f"获取成员战斗历史失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def extract_battle_date(record):
    """从战斗记录中提取日期"""
    try:
        # 尝试从battle_id中提取日期
        battle_id = record.get('battle_id', '')
        if battle_id and '_' in battle_id:
            date_part = battle_id.split('_')[0]
            if len(date_part) == 8 and date_part.isdigit():
                # 格式：20250531 -> 2025-05-31
                year = date_part[:4]
                month = date_part[4:6]
                day = date_part[6:8]
                return f"{year}-{month}-{day}"

        # 如果无法从battle_id提取，使用upload_time
        upload_time = record.get('upload_time', '')
        if upload_time:
            # 格式：2025-06-02T15:18:56.543590 -> 2025-06-02
            return upload_time.split('T')[0]

        return '未知日期'

    except Exception as e:
        print(f"提取战斗日期失败: {e}")
        return '未知日期'

@app.route('/api/members')
def api_members():
    """API: 获取所有成员"""
    members = load_members()  # 会自动获取当前用户的guild_id
    return jsonify(members)

@app.route('/api/team_stats')
def api_team_stats():
    """API: 获取团队统计"""
    members = load_members()  # 会自动获取当前用户的guild_id
    stats = get_team_stats(members)
    return jsonify(stats)

@app.route('/api/search_members')
@guest_allowed
def api_search_members():
    """API: 搜索成员 - 游客只能精确搜索人名，注册用户可以批量搜索"""
    query = request.args.get('q', '').lower().strip()
    user = get_current_user()

    # 🔒 游客搜索限制：防止恶意批量搜索挖人墙角
    if user and user['role'] == 'guest':
        # 游客只能进行精确的人名搜索，禁止职业等批量搜索
        if not query:
            return jsonify([])  # 游客不能获取全部成员列表

        # 检查是否为职业名搜索（批量搜索）
        professions = ['素问', '九灵', '潮光', '血河', '神相', '玄机', '铁衣', '龙吟', '碎梦', '沧澜']
        team_keywords = ['进攻', '防守', '一团', '二团', '三团', '四团', '五团', '替补', '请假']

        # 如果搜索词是职业名或团队关键词，拒绝搜索
        if (query in [p.lower() for p in professions] or
            any(keyword in query for keyword in team_keywords)):
            return jsonify({
                'error': '游客只能搜索具体的角色名称，不支持职业或团队批量搜索',
                'message': '请输入具体的角色名称进行搜索'
            })

        # 搜索词长度限制（防止过短的搜索词）
        if len(query) < 2:
            return jsonify({
                'error': '搜索词至少需要2个字符',
                'message': '请输入更具体的角色名称'
            })

        all_members = load_all_guild_members()

        # 游客只能精确匹配角色名
        results = []
        for member in all_members:
            if query in member['name'].lower():
                results.append(member)

        # 限制游客搜索结果数量，防止批量获取
        return jsonify(results[:10])

    else:
        # 注册用户保持原有搜索功能
        all_members = load_members()

        if not query:
            return jsonify(all_members[:50])  # 限制返回数量，避免数据过多

        # 注册用户可以使用全功能搜索
        results = []
        for member in all_members:
            if (query in member['name'].lower() or
                query in member['profession'].lower() or
                (member.get('main_group') and query in member['main_group'].lower()) or
                (member.get('sub_team') and query in member['sub_team'].lower())):
                results.append(member)

        return jsonify(results[:100])  # 限制搜索结果数量

def load_all_guild_members():
    """从数据库加载所有帮会的成员数据"""
    try:
        all_members = []

        # 优先从数据库加载所有帮会的成员
        connection = get_db_connection()
        if connection:
            try:
                cursor = connection.cursor(pymysql.cursors.DictCursor)

                # 查询所有成员及其帮会信息
                query = """
                SELECT
                    gm.name,
                    gm.profession,
                    gm.main_group,
                    gm.sub_team,
                    gm.squad,
                    gm.position,
                    gm.status,
                    g.name as guild_name,
                    g.id as guild_id
                FROM guild_members gm
                LEFT JOIN guilds g ON gm.guild_id = g.id
                WHERE g.status = 'active'
                ORDER BY g.name, gm.name
                """

                cursor.execute(query)
                db_members = cursor.fetchall()

                # 转换为应用格式
                for member in db_members:
                    all_members.append({
                        'name': member['name'],
                        'profession': member['profession'],
                        'main_group': member['main_group'] or '未分配',
                        'sub_team': member['sub_team'] or '未分配',
                        'squad': member['squad'] or '未分配',
                        'position': member['position'] or '未分配',
                        'status': member['status'] or '主力',
                        'guild_name': member['guild_name'] or '未知帮会',
                        'guild_id': member['guild_id'] or 'unknown',
                        'team': member['sub_team'] or '未分配'
                    })

                return_db_connection(connection)

                # 如果从数据库成功加载了数据，直接返回
                if all_members:
                    return all_members

            except Exception as e:
                print(f"从数据库加载成员数据失败: {e}")
                return_db_connection(connection)

        # 如果数据库没有数据，回退到文件加载
        main_members_file = 'data/guild_members.json'
        if os.path.exists(main_members_file):
            try:
                with open(main_members_file, 'r', encoding='utf-8') as f:
                    main_members = json.load(f)
                    # 为主帮会成员添加帮会信息
                    for member in main_members:
                        member['guild_name'] = '纸落云烟'
                        member['guild_id'] = 'zhiluoyunyan'
                    all_members.extend(main_members)
            except Exception as e:
                print(f"加载主帮会成员数据失败: {e}")

        return all_members

    except Exception as e:
        print(f"加载所有帮会成员数据失败: {e}")
        return []

def load_buzhoushan_members_from_csv():
    """从CSV文件加载不周山帮会成员数据"""
    try:
        members = []
        csv_file = 'data/20250603_201322_不周山_❤清韵音舞❤.csv'

        if not os.path.exists(csv_file):
            return []

        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            current_guild = None

            for row in reader:
                if not row:
                    continue

                # 检查是否是帮会名称行
                if len(row) == 2 and row[1].isdigit():
                    current_guild = row[0]
                    continue

                # 跳过表头
                if row[0] == '玩家名字':
                    continue

                # 处理成员数据 - 只加载不周山帮会的成员
                if len(row) >= 2 and row[0] and row[1] and current_guild == '不周山':
                    member = {
                        'name': row[0],
                        'profession': row[1],
                        'guild_name': current_guild,
                        'guild_id': 'buzhoushan',
                        'main_group': '未分配',
                        'sub_team': '未分配',
                        'squad': '未分配',
                        'position': '未分配',
                        'status': '主力',
                        'team': '未分配'
                    }
                    members.append(member)

        # 去重（基于角色名）
        seen_names = set()
        unique_members = []
        for member in members:
            if member['name'] not in seen_names:
                seen_names.add(member['name'])
                unique_members.append(member)

        return unique_members

    except Exception as e:
        print(f"从CSV加载不周山成员数据失败: {e}")
        return []

def load_battle_records_for_guild(guild_id):
    """加载指定帮会的战斗记录"""
    try:
        battle_file = f'data/battle_records_{guild_id}.json'
        if os.path.exists(battle_file):
            with open(battle_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"加载帮会 {guild_id} 战斗记录失败: {e}")
        return []

@app.route('/battle_analysis')
@guest_allowed
def battle_analysis():
    """战斗数据分析页面 - 显示所有战斗记录卡片"""
    # 加载所有战斗记录
    battle_records = load_battle_records()  # 会自动获取当前用户的guild_id

    # 按时间倒序排列（最新的在前）
    battle_records.sort(key=lambda x: x['upload_time'], reverse=True)

    return render_template('battle_analysis.html',
                         battle_records=battle_records)

@app.route('/upload_battle_data', methods=['GET', 'POST'])
@admin_required
def upload_battle_data():
    """上传战斗数据页面"""
    if request.method == 'POST':
        if 'battle_file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['battle_file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if file and file.filename.endswith('.csv'):
            # 保留原始文件名，不使用secure_filename以避免中文字符被截断
            original_filename = file.filename
            # 只进行基本的安全检查，移除路径分隔符
            filename = original_filename.replace('/', '_').replace('\\', '_').replace('..', '_')

            # 检查是否已存在相同的战斗记录（去重）
            battle_records = load_battle_records()
            existing_record = None
            for record in battle_records:
                if record['filename'] == filename:
                    existing_record = record
                    break

            if existing_record:
                return jsonify({'error': f'战斗记录 {filename} 已存在，请勿重复上传'})

            # 保存文件
            file_path = os.path.join('data', filename)
            file.save(file_path)

            try:
                print(f"开始处理文件: {filename}")

                # 解析CSV数据
                battle_data = parse_csv_battle_data(file_path)
                print(f"解析到战斗数据: {len(battle_data)} 条")

                if not battle_data:
                    os.remove(file_path)  # 删除无效文件
                    return jsonify({'error': '无法解析CSV文件或文件中没有有效的战斗数据'})

                # 加载成员数据
                members = load_members()
                print(f"加载成员数据: {len(members)} 人")

                # 🆕 自动添加新成员功能
                new_members_added = auto_add_new_members(battle_data, members)
                if new_members_added:
                    print(f"🆕 自动添加了 {len(new_members_added)} 个新成员: {[m['name'] for m in new_members_added]}")
                    # 重新加载成员数据（包含新添加的成员）
                    members = load_members()
                    print(f"重新加载成员数据: {len(members)} 人")

                # 分析战斗表现
                member_performance = analyze_member_performance(battle_data, members)
                print(f"分析完成，共 {len(member_performance)} 人的表现数据")

                if not member_performance:
                    os.remove(file_path)  # 删除无效文件
                    return jsonify({'error': '战斗表现分析失败，无法生成有效数据'})

                # 创建战斗记录
                battle_record = create_battle_record(filename, battle_data, member_performance)
                print(f"创建战斗记录: {battle_record['battle_id']}")

                # 保存到记录中
                battle_records.append(battle_record)
                save_battle_records(battle_records)  # 会自动获取当前用户的guild_id
                print("战斗记录保存成功")

                # 构建返回消息
                message_parts = [f'战斗数据 {filename} 分析完成']
                if new_members_added:
                    message_parts.append(f'自动添加了 {len(new_members_added)} 个新成员: {", ".join([m["name"] for m in new_members_added])}')

                return jsonify({
                    'success': True,
                    'message': '; '.join(message_parts),
                    'battle_id': battle_record['battle_id'],
                    'participated_members': battle_record['participated_members'],
                    'new_members': battle_record['new_members'],
                    'auto_added_members': [m['name'] for m in new_members_added] if new_members_added else []
                })

            except Exception as e:
                print(f"处理文件时出错: {e}")
                import traceback
                traceback.print_exc()

                # 如果处理失败，删除文件
                if os.path.exists(file_path):
                    os.remove(file_path)
                return jsonify({'error': f'处理文件时出错: {str(e)}'})
        else:
            return jsonify({'error': '只支持CSV文件'}), 400

    return render_template('upload_battle_data.html')

@app.route('/delete_battle_record', methods=['POST'])
@admin_required
def delete_battle_record():
    """删除战斗记录"""
    try:
        data = request.get_json()
        battle_id = data.get('battle_id')

        print(f"🔍 删除战斗记录请求: battle_id={battle_id}")
        print(f"🔍 请求数据: {data}")

        if not battle_id:
            print("❌ 缺少战斗ID")
            return jsonify({'error': '缺少战斗ID'}), 400

        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            print("❌ 无法确定帮会ID")
            return jsonify({'error': '无法确定帮会ID'}), 400

        guild_id = user['guild_id']
        print(f"🔍 当前帮会ID: {guild_id}")

        # 直接从数据库删除指定的战斗记录
        connection = get_db_connection()
        print(f"🔍 数据库连接状态: {connection is not None}")

        if connection:
            try:
                cursor = connection.cursor(pymysql.cursors.DictCursor)
                print(f"🔍 执行数据库查询: guild_id={guild_id}, battle_id={battle_id}")

                # 先查找记录以获取文件名
                cursor.execute("""
                    SELECT * FROM battle_records
                    WHERE guild_id = %s AND battle_id = %s
                """, (guild_id, battle_id))
                record_to_delete = cursor.fetchone()
                print(f"🔍 数据库查询结果: {record_to_delete}")

                if not record_to_delete:
                    print(f"❌ 数据库中未找到记录，尝试备用方法")
                    # 不要直接返回，继续尝试备用方法
                else:
                    # 删除数据库记录
                    cursor.execute("""
                        DELETE FROM battle_records
                        WHERE guild_id = %s AND battle_id = %s
                    """, (guild_id, battle_id))

                    connection.commit()

                    # 删除CSV文件（如果存在）
                    if 'filename' in record_to_delete:
                        csv_file_path = os.path.join('data', record_to_delete['filename'])
                        if os.path.exists(csv_file_path):
                            os.remove(csv_file_path)
                            print(f"已删除CSV文件: {csv_file_path}")

                    # 清除缓存
                    cache_key = f'battle_records_{guild_id}'
                    cache.delete(cache_key)

                    print(f"成功删除战斗记录: {battle_id}")

                    return jsonify({
                        'success': True,
                        'message': f'战斗记录 {record_to_delete["our_guild"]} VS {record_to_delete["enemy_guild"]} 已删除'
                    })

            except Exception as e:
                connection.rollback()
                print(f"❌ 数据库删除失败: {e}")
                import traceback
                traceback.print_exc()
                # 不要直接返回错误，继续尝试其他方法
            finally:
                return_db_connection(connection)
        else:
            print("❌ 无法获取数据库连接，尝试备用方法")

        # 如果数据库操作失败，尝试通过load_battle_records和save_battle_records删除
        try:
            print(f"数据库删除失败，尝试通过缓存/文件系统删除战斗记录: {battle_id}")
            battle_records = load_battle_records(guild_id)
            print(f"🔍 加载了 {len(battle_records)} 条战斗记录")

            # 打印所有战斗记录的ID用于调试
            for i, record in enumerate(battle_records):
                print(f"🔍 记录 {i}: battle_id='{record.get('battle_id', 'N/A')}'")

            # 查找要删除的记录
            record_to_delete = None
            for record in battle_records:
                if record['battle_id'] == battle_id:
                    record_to_delete = record
                    break

            if not record_to_delete:
                print(f"❌ 未找到指定的战斗记录: {battle_id}")
                print(f"🔍 可用的战斗记录ID: {[r.get('battle_id', 'N/A') for r in battle_records]}")
                return jsonify({'error': '未找到指定的战斗记录'}), 404

            # 删除CSV文件
            if 'filename' in record_to_delete:
                csv_file_path = os.path.join('data', record_to_delete['filename'])
                if os.path.exists(csv_file_path):
                    os.remove(csv_file_path)
                    print(f"已删除CSV文件: {csv_file_path}")

            # 从记录中移除
            battle_records.remove(record_to_delete)

            # 使用save_battle_records保存更新后的记录（会自动处理数据库和文件）
            if save_battle_records(battle_records, guild_id):
                print(f"成功通过save_battle_records删除战斗记录: {battle_id}")
                return jsonify({
                    'success': True,
                    'message': f'战斗记录 {record_to_delete["our_guild"]} VS {record_to_delete["enemy_guild"]} 已删除'
                })
            else:
                return jsonify({'error': '保存更新后的战斗记录失败'}), 500

        except Exception as e:
            print(f"备用删除方法失败: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'删除失败: {str(e)}'}), 500

    except Exception as e:
        print(f"删除战斗记录异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@app.route('/organization_chart')
@guest_allowed
def organization_chart():
    """组织架构图页面"""
    members = load_members()  # 会自动获取当前用户的guild_id

    # 获取三层级组织结构
    organization = get_organization_structure(members)

    # 动态获取所有主分组（包括自定义的）
    all_main_groups = set()

    # 1. 从成员数据中获取主分组
    for member in members:
        main_group = member.get('main_group', '其他团')
        all_main_groups.add(main_group)

    # 2. 从数据库配置中获取主分组
    try:
        user = get_current_user()
        if user and user.get('guild_id'):
            connection = get_db_connection()
            if connection:
                cursor = connection.cursor()
                cursor.execute("""
                    SELECT DISTINCT main_group FROM guild_team_names
                    WHERE guild_id = %s
                """, (user['guild_id'],))

                db_main_groups = cursor.fetchall()
                for row in db_main_groups:
                    all_main_groups.add(row[0])

                return_db_connection(connection)
    except Exception as e:
        print(f"⚠️ 组织架构页面加载数据库主分组失败: {e}")

    # 3. 确保基础分组存在
    all_main_groups.update(['进攻团', '防守团', '其他团'])

    # 4. 按照指定顺序排序主分组
    ordered_main_groups = []
    # 首先添加基础分组（按指定顺序）
    for base_group in ['进攻团', '防守团', '其他团']:
        if base_group in all_main_groups:
            ordered_main_groups.append(base_group)
    # 然后添加自定义分组（按字母顺序）
    custom_groups = sorted([g for g in all_main_groups if g not in ['进攻团', '防守团', '其他团']])
    ordered_main_groups.extend(custom_groups)

    return render_template('organization_chart.html',
                         members=members,
                         organization=organization,
                         all_main_groups=ordered_main_groups)

@app.route('/clean_member_data', methods=['POST'])
@admin_required
def clean_member_data():
    """清理当前帮会的成员数据"""
    try:
        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            return jsonify({'error': '无法确定帮会ID'}), 400

        guild_id = user['guild_id']

        # 执行清理
        success = clean_guild_member_data(guild_id)

        if success:
            return jsonify({
                'success': True,
                'message': f'成功清理帮会 {guild_id} 的成员数据'
            })
        else:
            return jsonify({'error': '清理失败'}), 500

    except Exception as e:
        print(f"清理成员数据异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'清理失败: {str(e)}'}), 500

@app.route('/update_team_name', methods=['POST'])
@admin_required
def update_team_name():
    """更新团队显示名称"""
    try:
        data = request.get_json()
        main_group = data.get('main_group')
        sub_team = data.get('sub_team')
        display_name = data.get('display_name')

        if not all([main_group, sub_team, display_name]):
            return jsonify({'error': '缺少必要参数'}), 400

        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            return jsonify({'error': '无法确定帮会ID'}), 400

        guild_id = user['guild_id']

        # 保存到数据库
        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        try:
            cursor = connection.cursor()
            cursor.execute("""
                INSERT INTO guild_team_names (guild_id, main_group, sub_team, display_name)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                display_name = VALUES(display_name),
                updated_at = CURRENT_TIMESTAMP
            """, (guild_id, main_group, sub_team, display_name))

            connection.commit()

            # 清除相关缓存
            cache_key = f'team_names_{guild_id}'
            cache.delete(cache_key)

            return jsonify({
                'success': True,
                'message': f'团队名称已更新为: {display_name}'
            })

        except Exception as e:
            connection.rollback()
            print(f"更新团队名称失败: {e}")
            return jsonify({'error': f'更新失败: {str(e)}'}), 500
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"更新团队名称异常: {e}")
        return jsonify({'error': f'更新失败: {str(e)}'}), 500

@app.route('/get_team_names')
def get_team_names():
    """获取当前帮会的团队名称配置"""
    try:
        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            return jsonify({'team_names': {}})

        guild_id = user['guild_id']

        # 检查缓存
        cache_key = f'team_names_{guild_id}'
        cached_names = cache.get(cache_key)
        if cached_names:
            return jsonify({'team_names': cached_names})

        # 从数据库加载
        connection = get_db_connection()
        if not connection:
            return jsonify({'team_names': {}})

        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT main_group, sub_team, display_name
                FROM guild_team_names
                WHERE guild_id = %s
            """, (guild_id,))

            results = cursor.fetchall()
            team_names = {}

            for row in results:
                key = f"{row['main_group']}-{row['sub_team']}"
                team_names[key] = row['display_name']

            # 缓存结果
            cache.set(cache_key, team_names)

            return jsonify({'team_names': team_names})

        except Exception as e:
            print(f"获取团队名称失败: {e}")
            return jsonify({'team_names': {}})
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"获取团队名称异常: {e}")
        return jsonify({'team_names': {}})

@app.route('/create_main_group', methods=['POST'])
@admin_required
def create_main_group():
    """创建新的主分组"""
    print("🆕 收到创建主分组请求")
    try:
        data = request.get_json()
        print(f"📊 请求数据: {data}")

        main_group_name = data.get('name') if data else None
        group_type = data.get('type', 'special') if data else 'special'
        description = data.get('description', '') if data else ''

        print(f"📝 解析参数: name={main_group_name}, type={group_type}, description={description}")

        if not main_group_name:
            print("❌ 主分组名称为空")
            return jsonify({'error': '主分组名称不能为空'}), 400

        # 获取当前用户的帮会ID
        user = get_current_user()
        print(f"👤 当前用户: {user}")

        if not user or not user.get('guild_id'):
            print("❌ 无法确定帮会ID")
            return jsonify({'error': '无法确定帮会ID'}), 400

        guild_id = user['guild_id']
        print(f"🏰 帮会ID: {guild_id}")

        # 检查主分组名称是否已存在
        connection = get_db_connection()
        if not connection:
            print("❌ 数据库连接失败")
            return jsonify({'error': '数据库连接失败'}), 500

        try:
            cursor = connection.cursor()
            print("📊 数据库连接成功，开始检查重复")

            # 检查是否已存在同名主分组
            cursor.execute("""
                SELECT COUNT(*) as count FROM guild_team_names
                WHERE guild_id = %s AND main_group = %s
            """, (guild_id, main_group_name))

            result = cursor.fetchone()
            print(f"🔍 重复检查结果: {result}")

            if result and result[0] > 0:
                print(f"❌ 主分组已存在: {main_group_name}")
                return jsonify({'error': f'主分组 "{main_group_name}" 已存在'}), 400

            print("✅ 主分组名称可用，开始插入数据")

            # 保存主分组配置
            cursor.execute("""
                INSERT INTO guild_team_names (guild_id, main_group, sub_team, display_name)
                VALUES (%s, %s, %s, %s)
            """, (guild_id, main_group_name, main_group_name, main_group_name))

            connection.commit()
            print("✅ 数据库插入成功")

            # 清除缓存
            cache_key = f'team_names_{guild_id}'
            cache.delete(cache_key)
            print("✅ 缓存清除成功")

            response_data = {
                'success': True,
                'message': f'主分组 "{main_group_name}" 创建成功',
                'main_group': main_group_name,
                'type': group_type
            }
            print(f"✅ 返回成功响应: {response_data}")
            return jsonify(response_data)

        except Exception as e:
            connection.rollback()
            print(f"创建主分组失败: {e}")
            return jsonify({'error': f'创建失败: {str(e)}'}), 500
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"创建主分组异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'创建失败: {str(e)}'}), 500

@app.route('/delete_main_group', methods=['POST'])
@admin_required
def delete_main_group():
    """删除主分组"""
    print("🗑️ 收到删除主分组请求")
    try:
        data = request.get_json()
        main_group_name = data.get('name') if data else None

        print(f"📊 请求数据: {data}")
        print(f"📝 要删除的主分组: {main_group_name}")

        if not main_group_name:
            print("❌ 主分组名称为空")
            return jsonify({'error': '主分组名称不能为空'}), 400

        # 检查是否为基础分组
        if main_group_name in ['进攻团', '防守团', '其他团']:
            print(f"❌ 不能删除基础分组: {main_group_name}")
            return jsonify({'error': '不能删除基础分组'}), 400

        # 获取当前用户的帮会ID
        user = get_current_user()
        print(f"👤 当前用户: {user}")

        if not user or not user.get('guild_id'):
            print("❌ 无法确定帮会ID")
            return jsonify({'error': '无法确定帮会ID'}), 400

        guild_id = user['guild_id']
        print(f"🏰 帮会ID: {guild_id}")

        connection = get_db_connection()
        if not connection:
            print("❌ 数据库连接失败")
            return jsonify({'error': '数据库连接失败'}), 500

        try:
            cursor = connection.cursor()
            print("📊 数据库连接成功，开始删除操作")

            # 1. 将该主分组下的所有成员移动到替补
            cursor.execute("""
                UPDATE guild_members
                SET main_group = '其他团', sub_team = '替补', squad = '替补', status = '替补'
                WHERE guild_id = %s AND main_group = %s
            """, (guild_id, main_group_name))

            affected_members = cursor.rowcount
            print(f"✅ 移动了 {affected_members} 个成员到替补区域")

            # 2. 删除主分组配置
            cursor.execute("""
                DELETE FROM guild_team_names
                WHERE guild_id = %s AND main_group = %s
            """, (guild_id, main_group_name))

            deleted_configs = cursor.rowcount
            print(f"✅ 删除了 {deleted_configs} 个团队配置")

            connection.commit()
            print("✅ 数据库操作提交成功")

            # 清除缓存
            cache_key_members = f'members_{guild_id}'
            cache_key_teams = f'team_names_{guild_id}'
            cache.delete(cache_key_members)
            cache.delete(cache_key_teams)
            print("✅ 缓存清除成功")

            response_data = {
                'success': True,
                'message': f'主分组 "{main_group_name}" 已删除，{affected_members} 个成员已移至替补',
                'affected_members': affected_members,
                'deleted_configs': deleted_configs
            }
            print(f"✅ 返回成功响应: {response_data}")
            return jsonify(response_data)

        except Exception as e:
            connection.rollback()
            print(f"❌ 删除主分组失败: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'删除失败: {str(e)}'}), 500
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"❌ 删除主分组异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'删除失败: {str(e)}'}), 500



@app.route('/list_main_groups', methods=['GET'])
def list_main_groups():
    """列出当前帮会的所有主分组"""
    try:
        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            return jsonify({'error': '无法确定帮会ID'}), 400

        guild_id = user['guild_id']

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT DISTINCT main_group, COUNT(*) as count
                FROM guild_team_names
                WHERE guild_id = %s
                GROUP BY main_group
                ORDER BY main_group
            """, (guild_id,))

            results = cursor.fetchall()

            return jsonify({
                'success': True,
                'guild_id': guild_id,
                'main_groups': results,
                'total': len(results)
            })

        except Exception as e:
            print(f"查询主分组失败: {e}")
            return jsonify({'error': f'查询失败: {str(e)}'}), 500
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"列出主分组异常: {e}")
        return jsonify({'error': f'查询失败: {str(e)}'}), 500

@app.route('/api/organization_data')
def api_organization_data():
    """API接口：获取组织架构数据"""
    try:
        members = load_members()  # 会自动获取当前用户的guild_id
        organization = get_organization_structure(members)

        return jsonify({
            'success': True,
            'organization': organization
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/drag_board')
@admin_required
def drag_board():
    """拖拽排表页面"""
    members = load_members()  # 会自动获取当前用户的guild_id

    # 获取组织结构
    organization = get_organization_structure(members)

    # 动态获取所有主分组（包括自定义的）
    all_main_groups = set()

    # 1. 从成员数据中获取主分组
    for member in members:
        main_group = member.get('main_group', '其他团')
        all_main_groups.add(main_group)

    # 2. 从数据库配置中获取主分组和子团队配置
    all_team_configs = []
    try:
        user = get_current_user()
        if user and user.get('guild_id'):
            connection = get_db_connection()
            if connection:
                cursor = connection.cursor()

                # 获取所有团队配置
                cursor.execute("""
                    SELECT main_group, sub_team, display_name FROM guild_team_names
                    WHERE guild_id = %s
                    ORDER BY main_group, sub_team
                """, (user['guild_id'],))

                db_teams = cursor.fetchall()
                for row in db_teams:
                    main_group, sub_team, display_name = row
                    all_main_groups.add(main_group)
                    all_team_configs.append({
                        'main_group': main_group,
                        'sub_team': sub_team,
                        'display_name': display_name
                    })

                return_db_connection(connection)
                print(f"📊 从数据库加载的团队配置: {len(all_team_configs)} 个")
                print(f"📊 从数据库加载的主分组: {sorted(set(config['main_group'] for config in all_team_configs))}")
    except Exception as e:
        print(f"⚠️ 加载数据库团队配置失败: {e}")

    # 3. 确保基础分组存在
    all_main_groups.update(['进攻团', '防守团', '其他团'])

    # 4. 按照指定顺序排序主分组
    ordered_main_groups = []
    # 首先添加基础分组（按指定顺序）
    for base_group in ['进攻团', '防守团', '其他团']:
        if base_group in all_main_groups:
            ordered_main_groups.append(base_group)
    # 然后添加自定义分组（按字母顺序）
    custom_groups = sorted([g for g in all_main_groups if g not in ['进攻团', '防守团', '其他团']])
    ordered_main_groups.extend(custom_groups)

    print(f"📊 最终的所有主分组: {ordered_main_groups}")

    return render_template('drag_board.html',
                         members=members,
                         organization=organization,
                         all_main_groups=ordered_main_groups,
                         all_team_configs=all_team_configs)

# 移除智能position推断功能，小队编号与职能位置无关

@app.route('/update_member_position', methods=['POST'])
@admin_required
def update_member_position():
    """更新成员位置（拖拽后）- 联动更新相关字段"""
    print("🔄 收到成员位置更新请求")
    data = request.get_json()
    print(f"📊 请求数据: {data}")

    member_name = data.get('member_name')
    new_main_group = data.get('main_group')
    new_sub_team = data.get('sub_team')
    new_squad = data.get('squad')

    print(f"📝 解析参数: {member_name} -> {new_main_group}/{new_sub_team}/{new_squad}")

    # 🔧 使用原子数据库操作，避免并发问题
    user = get_current_user()
    if not user or not user.get('guild_id'):
        return jsonify({'error': '无法确定帮会ID'}), 400

    guild_id = user['guild_id']

    # 直接在数据库中更新，避免加载整个列表
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': '数据库连接失败'}), 500

    try:
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 先查询当前成员信息
        cursor.execute("""
            SELECT * FROM guild_members
            WHERE guild_id = %s AND name = %s
        """, (guild_id, member_name))

        member = cursor.fetchone()
        if not member:
            return jsonify({'error': f'未找到成员: {member_name}'}), 404

        # 记录旧值
        old_info = {
            'main_group': member.get('main_group', ''),
            'sub_team': member.get('sub_team', ''),
            'squad': member.get('squad', ''),
            'status': member.get('status', '主力')
        }

        # 计算新的status
        new_status = '主力'
        if new_sub_team == '请假' or new_squad == '请假':
            new_status = '请假'
        elif new_sub_team == '替补' or new_squad == '替补':
            new_status = '替补'
        elif new_sub_team == '帮外' or new_squad == '帮外':
            new_status = '帮外'

        # 原子更新操作
        cursor.execute("""
            UPDATE guild_members
            SET main_group = %s, sub_team = %s, squad = %s, status = %s
            WHERE guild_id = %s AND name = %s
        """, (new_main_group, new_sub_team, new_squad, new_status, guild_id, member_name))

        connection.commit()

        # 清除缓存
        cache_key = f'members_{guild_id}'
        cache.delete(cache_key)

        # 记录变更信息
        changes = []
        updated_fields = []

        if old_info['main_group'] != new_main_group:
            changes.append(f"main_group: {old_info['main_group']} → {new_main_group}")
            updated_fields.append('main_group')
        if old_info['sub_team'] != new_sub_team:
            changes.append(f"sub_team: {old_info['sub_team']} → {new_sub_team}")
            updated_fields.append('sub_team')
        if old_info['squad'] != new_squad:
            changes.append(f"squad: {old_info['squad']} → {new_squad}")
            updated_fields.append('squad')
        if old_info['status'] != new_status:
            changes.append(f"status: {old_info['status']} → {new_status}")
            updated_fields.append('status')

        print(f"✅ 原子更新成员 {member_name}: {', '.join(changes)}")

    except Exception as e:
        connection.rollback()
        print(f"❌ 数据库更新失败: {e}")
        return jsonify({'error': f'更新失败: {str(e)}'}), 500
    finally:
        return_db_connection(connection)

    return jsonify({
        'success': True,
        'message': f'位置更新成功，联动更新了: {", ".join(updated_fields)}',
        'updated_fields': updated_fields,
        'changes': changes if 'changes' in locals() else []
    })

@app.route('/add_member', methods=['POST'])
@admin_required
def add_member():
    """新建成员"""
    try:
        data = request.get_json()
        member_name = data.get('name')
        profession = data.get('profession')
        position = data.get('position', '拆塔')

        print(f"🆕 新建成员: {member_name} ({profession}) - {position}")

        if not member_name or not profession:
            return jsonify({'success': False, 'error': '缺少必要参数'})

        # 读取现有数据
        members = load_members()  # 会自动获取当前用户的guild_id

        # 检查成员是否已存在
        for member in members:
            if member['name'] == member_name:
                return jsonify({'success': False, 'error': f'成员 {member_name} 已存在'})

        # 创建新成员 - 默认归属替补
        new_member = {
            'name': member_name,
            'profession': profession,
            'position': position,
            'main_group': '其他团',
            'sub_team': '替补',
            'squad': '替补',
            'status': '替补'
        }

        # 添加到成员列表
        members.append(new_member)

        # 保存数据
        save_members(members)  # 会自动获取当前用户的guild_id
        print(f"✅ 成员 {member_name} 创建成功")

        return jsonify({'success': True, 'message': f'成员 {member_name} 创建成功'})

    except Exception as e:
        print(f"❌ 创建成员时出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/test_connection', methods=['GET'])
def test_connection():
    """测试连接"""
    print("🔗 测试连接请求")
    return jsonify({'success': True, 'message': '连接正常'})

@app.route('/save_team_config', methods=['POST'])
@admin_required
def save_team_config():
    """保存团队配置"""
    print("🏗️ 收到团队配置保存请求")
    data = request.get_json()
    print(f"📊 请求数据: {data}")

    # 兼容两种参数格式
    team_type = data.get('team_type') or data.get('main_group')  # '进攻团' 或 '防守团' 或自定义主分组
    sub_team = data.get('sub_team')    # '四团' 或 '防守二团' 或自定义子团

    print(f"📝 解析参数: team_type={team_type}, sub_team={sub_team}")

    if not team_type or not sub_team:
        print("❌ 缺少必要参数")
        return jsonify({'success': False, 'error': '缺少必要参数'})

    try:
        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            print("❌ 无法确定帮会ID")
            return jsonify({'success': False, 'error': '无法确定帮会ID'})

        guild_id = user['guild_id']
        print(f"🏰 帮会ID: {guild_id}")

        # 使用数据库存储团队配置
        connection = get_db_connection()
        if not connection:
            print("❌ 数据库连接失败")
            return jsonify({'success': False, 'error': '数据库连接失败'})

        try:
            cursor = connection.cursor()

            # 检查是否已存在
            cursor.execute("""
                SELECT id FROM guild_team_names
                WHERE guild_id = %s AND main_group = %s AND sub_team = %s
            """, (guild_id, team_type, sub_team))

            existing_team = cursor.fetchone()
            if existing_team:
                print(f"⚠️ 团队已存在: {team_type}-{sub_team}")
                return jsonify({'success': False, 'error': f'{sub_team}已存在'})

            # 添加新团队配置
            cursor.execute("""
                INSERT INTO guild_team_names (guild_id, main_group, sub_team, display_name)
                VALUES (%s, %s, %s, %s)
            """, (guild_id, team_type, sub_team, sub_team))

            connection.commit()
            print(f"✅ 团队配置保存成功: {team_type}-{sub_team}")

            # 清除缓存
            cache_key_teams = f'team_names_{guild_id}'
            cache.delete(cache_key_teams)

            return jsonify({
                'success': True,
                'message': f'{sub_team}配置已保存',
                'team': {
                    'main_group': team_type,
                    'sub_team': sub_team,
                    'created_time': datetime.now().isoformat()
                }
            })

        except Exception as e:
            connection.rollback()
            print(f"❌ 数据库操作失败: {e}")
            return jsonify({'success': False, 'error': f'保存失败: {str(e)}'})
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"❌ 保存团队配置失败: {e}")
        return jsonify({'success': False, 'error': f'保存失败: {str(e)}'})

@app.route('/load_team_config')
def load_team_config():
    """加载团队配置"""
    try:
        config_file = os.path.join(DATA_DIR, 'team_config.json')
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return jsonify({'success': True, 'config': config})
        else:
            return jsonify({'success': True, 'config': {'custom_teams': []}})
    except Exception as e:
        print(f"❌ 加载团队配置失败: {e}")
        return jsonify({'success': False, 'error': f'加载失败: {str(e)}'})

@app.route('/delete_team_config', methods=['POST'])
@admin_required
def delete_team_config():
    """删除团队配置"""
    print("🗑️ 收到团队配置删除请求")
    data = request.get_json()
    print(f"📊 请求数据: {data}")

    # 兼容两种参数格式
    team_type = data.get('team_type') or data.get('main_group')  # '进攻团' 或 '防守团' 或自定义主分组
    sub_team = data.get('sub_team')    # '四团' 或 '防守二团' 或自定义子团

    print(f"📝 解析参数: team_type={team_type}, sub_team={sub_team}")

    if not team_type or not sub_team:
        print("❌ 缺少必要参数")
        return jsonify({'success': False, 'error': '缺少必要参数'})

    try:
        # 获取当前用户的帮会ID
        user = get_current_user()
        if not user or not user.get('guild_id'):
            print("❌ 无法确定帮会ID")
            return jsonify({'success': False, 'error': '无法确定帮会ID'})

        guild_id = user['guild_id']
        print(f"🏰 帮会ID: {guild_id}")

        # 使用数据库删除团队配置
        connection = get_db_connection()
        if not connection:
            print("❌ 数据库连接失败")
            return jsonify({'success': False, 'error': '数据库连接失败'})

        try:
            cursor = connection.cursor()

            # 检查团队是否存在
            cursor.execute("""
                SELECT id FROM guild_team_names
                WHERE guild_id = %s AND main_group = %s AND sub_team = %s
            """, (guild_id, team_type, sub_team))

            existing_team = cursor.fetchone()
            if not existing_team:
                print(f"⚠️ 团队不存在: {team_type}-{sub_team}")
                return jsonify({'success': False, 'error': f'{sub_team}不存在'})

            # 删除团队配置
            cursor.execute("""
                DELETE FROM guild_team_names
                WHERE guild_id = %s AND main_group = %s AND sub_team = %s
            """, (guild_id, team_type, sub_team))

            connection.commit()
            print(f"✅ 团队配置删除成功: {team_type}-{sub_team}")

            # 清除缓存
            cache_key_teams = f'team_names_{guild_id}'
            cache.delete(cache_key_teams)

            return jsonify({
                'success': True,
                'message': f'{sub_team}配置已删除'
            })

        except Exception as e:
            connection.rollback()
            print(f"❌ 数据库操作失败: {e}")
            return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})
        finally:
            return_db_connection(connection)

    except Exception as e:
        print(f"❌ 删除团队配置失败: {e}")
        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})

@app.route('/update_member_position_only', methods=['POST'])
@admin_required
def update_member_position_only():
    """仅更新成员职责位置"""
    print("=" * 50)
    print("🔄 收到职责更新请求")
    print(f"请求方法: {request.method}")
    print(f"Content-Type: {request.content_type}")
    try:
        data = request.get_json()
        print(f"接收到的数据: {data}")

        member_name = data.get('member_name') if data else None
        new_position = data.get('position') if data else None

        print(f"解析参数: member_name={member_name}, new_position={new_position}")

        if not member_name or not new_position:
            print(f"参数检查失败: member_name={member_name}, new_position={new_position}")
            return jsonify({'success': False, 'error': f'缺少必要参数: member_name={member_name}, position={new_position}'})

        # 🔧 使用原子数据库操作，避免并发问题
        user = get_current_user()
        if not user or not user.get('guild_id'):
            return jsonify({'success': False, 'error': '无法确定帮会ID'})

        guild_id = user['guild_id']

        # 直接在数据库中更新
        connection = get_db_connection()
        if not connection:
            return jsonify({'success': False, 'error': '数据库连接失败'})

        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            # 先查询当前成员信息
            cursor.execute("""
                SELECT position FROM guild_members
                WHERE guild_id = %s AND name = %s
            """, (guild_id, member_name))

            member = cursor.fetchone()
            if not member:
                return jsonify({'success': False, 'error': f'未找到成员: {member_name}'})

            old_position = member.get('position', '未知')

            # 原子更新操作
            cursor.execute("""
                UPDATE guild_members
                SET position = %s
                WHERE guild_id = %s AND name = %s
            """, (new_position, guild_id, member_name))

            connection.commit()

            # 清除缓存
            cache_key = f'members_{guild_id}'
            cache.delete(cache_key)

            print(f"✅ 原子更新成员职责: {member_name} {old_position} → {new_position}")

        except Exception as e:
            connection.rollback()
            print(f"❌ 数据库更新失败: {e}")
            return jsonify({'success': False, 'error': f'更新失败: {str(e)}'})
        finally:
            return_db_connection(connection)

        response_data = {
            'success': True,
            'message': f'{member_name} 的职责已更新为: {new_position}'
        }
        print(f"返回响应: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        print(f"更新成员职责失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/test_add_member')
def test_add_member():
    """测试新建成员功能页面"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新建成员功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>测试新建成员功能</h1>

    <form id="testForm">
        <div class="form-group">
            <label for="name">成员姓名 *</label>
            <input type="text" id="name" name="name" required>
        </div>

        <div class="form-group">
            <label for="profession">职业 *</label>
            <select id="profession" name="profession" required>
                <option value="">请选择职业</option>
                <option value="素问">素问</option>
                <option value="九灵">九灵</option>
                <option value="潮光">潮光</option>
                <option value="血河">血河</option>
                <option value="神相">神相</option>
                <option value="玄机">玄机</option>
                <option value="铁衣">铁衣</option>
                <option value="龙吟">龙吟</option>
                <option value="碎梦">碎梦</option>
            </select>
        </div>

        <div class="form-group">
            <label for="position">职责</label>
            <select id="position" name="position">
                <option value="拆塔">拆塔</option>
                <option value="击杀">击杀</option>
                <option value="人伤">人伤</option>
                <option value="治疗">治疗</option>
                <option value="扛伤">扛伤</option>
                <option value="辅助">辅助</option>
            </select>
        </div>

        <button type="submit">创建成员</button>
    </form>

    <div id="result" class="result"></div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('name').value.trim(),
                profession: document.getElementById('profession').value,
                position: document.getElementById('position').value
            };

            console.log('提交数据:', formData);

            if (!formData.name) {
                showResult('请输入成员姓名！', 'error');
                return;
            }

            if (!formData.profession) {
                showResult('请选择职业！', 'error');
                return;
            }

            // 发送请求
            fetch('/add_member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                console.log('响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('响应数据:', data);
                if (data.success) {
                    showResult(data.message, 'success');
                    document.getElementById('testForm').reset();
                } else {
                    showResult('创建失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                showResult('请求失败: ' + error.message, 'error');
            });
        });

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';

            // 3秒后隐藏
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
    '''

@app.route('/update_member_info', methods=['POST'])
@admin_required
def update_member_info():
    """更新成员信息"""
    try:
        data = request.get_json()
        old_name = data.get('old_name')
        new_name = data.get('new_name')
        profession = data.get('profession')
        position = data.get('position')

        print(f"更新成员信息: {old_name} -> {new_name}, {profession}, {position}")

        if not old_name or not new_name or not profession:
            return jsonify({'success': False, 'error': '缺少必要参数'})

        members = load_members()  # 会自动获取当前用户的guild_id

        # 查找要更新的成员
        member_found = False
        for member in members:
            if member.get('name') == old_name:
                member['name'] = new_name
                member['profession'] = profession
                member['position'] = position
                member_found = True
                break

        if not member_found:
            return jsonify({'success': False, 'error': '未找到指定成员'})

        # 保存更新后的数据
        save_members(members)  # 会自动获取当前用户的guild_id

        return jsonify({'success': True, 'message': '成员信息更新成功'})

    except Exception as e:
        print(f"更新成员信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/delete_member', methods=['POST'])
@admin_required
def delete_member():
    """删除成员"""
    try:
        data = request.get_json()
        member_name = data.get('member_name')

        print(f"删除成员: {member_name}")

        if not member_name:
            return jsonify({'success': False, 'error': '缺少成员姓名'})

        members = load_members()  # 会自动获取当前用户的guild_id

        # 查找并删除成员
        original_count = len(members)
        members = [member for member in members if member.get('name') != member_name]

        if len(members) == original_count:
            return jsonify({'success': False, 'error': '未找到指定成员'})

        # 保存更新后的数据
        save_members(members)  # 会自动获取当前用户的guild_id

        return jsonify({'success': True, 'message': '成员删除成功'})

    except Exception as e:
        print(f"删除成员失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/export_data')
def export_data():
    """导出数据"""
    members = load_members()  # 会自动获取当前用户的guild_id

    # 获取当前用户的帮会名称
    user = get_current_user()
    guild_name = "未知帮会"
    if user and user.get('guild_id'):
        guilds = load_guilds()
        guild_info = guilds.get(user['guild_id'])
        if guild_info:
            guild_name = guild_info['name']

    # 创建导出数据
    export_data = {
        'guild_name': guild_name,
        'export_time': datetime.now().isoformat(),
        'total_members': len(members),
        'members': members
    }

    return jsonify(export_data)

if __name__ == '__main__':
    # 确保工作目录正确
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录设置为: {os.getcwd()}")

    # 初始化数据库
    print("正在初始化数据库...")
    if init_database():
        print("✅ 数据库连接正常")


    else:
        print("⚠️ 数据库连接失败，将使用文件存储模式")

    # 确保必要目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('data', exist_ok=True)

    # 预热缓存
    warm_up_cache()

    # 🕐 启动自动快照定时任务
    try:
        schedule_weekly_snapshots()
    except Exception as e:
        print(f"启动定时任务失败: {e}")

    print("=" * 60)
    print("🚀 逆水寒帮会辅助管理系统启动中...")
    print("=" * 60)
    print("🏛️ 平台功能包括:")
    print("   - 多帮会管理 (超级管理员)")
    print("   - 用户权限管理")
    print("   - 帮会申请审批")
    print("   - 成员数据管理")
    print("   - 战斗数据分析")
    print("   - 系统设置管理")
    print("   - 📸 排表快照管理 (新功能)")
    print()
    print("👤 支持用户注册和帮会申请")
    print()
    print("🌐 访问地址: http://localhost:5888")
    print("=" * 60)

    # 生产环境配置
    import os
    debug_mode = os.environ.get('FLASK_ENV') == 'development'
    port = int(os.environ.get('PORT', 5888))
    host = os.environ.get('HOST', '0.0.0.0')

    # 🆕 技能标签管理API
    @app.route('/api/update_member_skills', methods=['POST'])
    @admin_required
    def update_member_skills():
        """更新成员技能标签"""
        data = request.get_json()
        member_name = data.get('member_name')
        skills = data.get('skills', [])

        if not member_name:
            return jsonify({'error': '成员名称不能为空'}), 400

        # 获取当前用户的帮会ID
        current_user = get_current_user()
        guild_id = current_user.get('guild_id')

        if not guild_id:
            return jsonify({'error': '用户未绑定帮会'}), 400

        connection = get_db_connection()
        if not connection:
            return jsonify({'error': '数据库连接失败'}), 500

        try:
            cursor = connection.cursor()

            # 将技能列表转换为JSON格式
            skills_json = json.dumps(skills, ensure_ascii=False)

            cursor.execute("""
                UPDATE guild_members
                SET skills = %s, updated_time = CURRENT_TIMESTAMP
                WHERE name = %s AND guild_id = %s
            """, (skills_json, member_name, guild_id))

            if cursor.rowcount == 0:
                return jsonify({'error': '成员不存在或无权限修改'}), 404

            # 提交事务
            connection.commit()

            # 清除缓存
            cache.delete(f'members_{guild_id}')

            return jsonify({'success': True, 'message': '技能标签更新成功'})

        except Exception as e:
            print(f"更新成员技能失败: {e}")
            return jsonify({'error': '更新失败'}), 500
        finally:
            return_db_connection(connection)

    def load_skill_presets():
        """加载技能预设"""
        skill_presets_file = os.path.join(DATA_DIR, 'skill_presets.json')
        if os.path.exists(skill_presets_file):
            try:
                with open(skill_presets_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return []
        return []

    def save_skill_presets(presets):
        """保存技能预设"""
        skill_presets_file = os.path.join(DATA_DIR, 'skill_presets.json')
        try:
            with open(skill_presets_file, 'w', encoding='utf-8') as f:
                json.dump(presets, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False

    @app.route('/api/get_skill_presets', methods=['GET'])
    def get_skill_presets():
        """获取技能预设列表"""
        skill_presets = load_skill_presets()
        return jsonify({'presets': skill_presets})

    @app.route('/api/add_skill_preset', methods=['POST'])
    def add_skill_preset():
        """添加技能预设"""
        try:
            data = request.get_json()
            skill = data.get('skill', '').strip()

            if not skill:
                return jsonify({'success': False, 'error': '技能名称不能为空'})

            # 加载现有预设
            presets = load_skill_presets()

            # 检查是否已存在
            if skill in presets:
                return jsonify({'success': False, 'error': '该技能预设已存在'})

            # 添加新预设
            presets.append(skill)

            # 保存
            if save_skill_presets(presets):
                return jsonify({'success': True, 'presets': presets})
            else:
                return jsonify({'success': False, 'error': '保存失败'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/api/delete_skill_preset', methods=['POST'])
    def delete_skill_preset():
        """删除技能预设"""
        try:
            data = request.get_json()
            skill = data.get('skill', '').strip()

            if not skill:
                return jsonify({'success': False, 'error': '技能名称不能为空'})

            # 加载现有预设
            presets = load_skill_presets()

            # 检查是否存在
            if skill not in presets:
                return jsonify({'success': False, 'error': '该技能预设不存在'})

            # 删除预设
            presets.remove(skill)

            # 保存
            if save_skill_presets(presets):
                return jsonify({'success': True, 'presets': presets})
            else:
                return jsonify({'success': False, 'error': '保存失败'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/api/clear_skill_presets', methods=['POST'])
    def clear_skill_presets():
        """清空所有技能预设"""
        try:
            # 保存空列表
            if save_skill_presets([]):
                return jsonify({'success': True, 'presets': []})
            else:
                return jsonify({'success': False, 'error': '清空失败'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    app.run(debug=debug_mode, host=host, port=port)
