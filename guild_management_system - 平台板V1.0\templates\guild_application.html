{% extends "base.html" %}

{% block title %}帮会申请 - 逆水寒帮会辅助管理系统{% endblock %}

{% block content %}
<style>
    .application-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .application-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .application-header h2 {
        color: #2c3e50;
        font-size: 2em;
        margin-bottom: 10px;
    }

    .application-header p {
        color: #7f8c8d;
        font-size: 1.1em;
    }

    .application-tabs {
        display: flex;
        margin-bottom: 30px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 5px;
    }

    .application-tab {
        flex: 1;
        padding: 15px;
        text-align: center;
        border: none;
        background: transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 16px;
        font-weight: bold;
    }

    .application-tab.active {
        background: #667eea;
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .application-tab:hover:not(.active) {
        background: rgba(102, 126, 234, 0.1);
    }

    .application-form {
        display: none;
    }

    .application-form.active {
        display: block;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #555;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 10px rgba(102, 126, 234, 0.2);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }

    .submit-btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .guild-list {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .guild-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        margin: 10px 0;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .guild-info h4 {
        margin: 0 0 5px 0;
        color: #2c3e50;
    }

    .guild-info p {
        margin: 0;
        color: #7f8c8d;
        font-size: 14px;
    }

    .guild-stats {
        text-align: right;
        color: #666;
        font-size: 14px;
    }

    .flash-messages {
        margin-bottom: 20px;
    }

    .flash-message {
        padding: 12px 20px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .flash-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .flash-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .flash-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
</style>

<!-- Flash消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="flash-messages">
            {% for category, message in messages %}
                <div class="flash-message flash-{{ category }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="application-container">
    <div class="application-header">
        <h2>🏛️ 欢迎来到逆水寒帮会辅助管理系统</h2>
        <p>您还没有加入任何帮会，请选择创建新帮会或加入现有帮会</p>
        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
            <p style="margin: 0; color: #1565c0; font-size: 14px;">
                💡 <strong>提示：</strong>创建帮会需要超级管理员审批，加入帮会需要帮会大当家审批
            </p>
        </div>
    </div>

    <div class="application-tabs">
        <button class="application-tab active" onclick="showApplicationForm('create')">
            🆕 创建帮会
        </button>
        <button class="application-tab" onclick="showApplicationForm('join')">
            🤝 加入帮会
        </button>
    </div>

    <!-- 创建帮会表单 -->
    <div id="create-form" class="application-form active">
        <form method="POST">
            <input type="hidden" name="application_type" value="create_guild">
            
            <div class="form-group">
                <label for="guild_name">帮会名称</label>
                <input type="text" id="guild_name" name="guild_name" required 
                       placeholder="请输入帮会名称" maxlength="50">
            </div>

            <div class="form-group">
                <label for="guild_id">帮会ID</label>
                <input type="text" id="guild_id" name="guild_id" required 
                       placeholder="请输入帮会ID（英文字母和数字）" maxlength="20" pattern="[a-zA-Z0-9_]+">
                <small style="color: #666;">帮会ID用于系统识别，只能包含字母、数字和下划线</small>
            </div>

            <div class="form-group">
                <label for="create_description">申请说明</label>
                <textarea id="create_description" name="description" 
                          placeholder="请说明创建帮会的原因和规划..."></textarea>
            </div>

            <button type="submit" class="submit-btn">
                🚀 提交创建申请
            </button>
        </form>
    </div>

    <!-- 加入帮会表单 -->
    <div id="join-form" class="application-form">
        {% if guilds %}
        <div class="guild-list">
            <h3>📋 现有帮会列表</h3>
            {% for guild_id, guild in guilds.items() %}
            <div class="guild-item">
                <div class="guild-info">
                    <h4>{{ guild.name }}</h4>
                    <p>大当家: {{ guild.leader }} | ID: {{ guild_id }}</p>
                    <p>{{ guild.description or '暂无描述' }}</p>
                </div>
                <div class="guild-stats">
                    <div>成员: {{ guild.members_count }}/{{ guild.max_members }}</div>
                    <div>创建时间: {{ guild.created_time[:10] if guild.created_time else '未知' }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <form method="POST">
            <input type="hidden" name="application_type" value="join_guild">
            
            <div class="form-group">
                <label for="join_guild_id">选择帮会</label>
                <select id="join_guild_id" name="guild_id" required>
                    <option value="">请选择要加入的帮会</option>
                    {% for guild_id, guild in guilds.items() %}
                    <option value="{{ guild_id }}">{{ guild.name }} ({{ guild_id }})</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <label for="join_description">申请说明</label>
                <textarea id="join_description" name="description" 
                          placeholder="请说明加入帮会的原因和个人情况..."></textarea>
            </div>

            <button type="submit" class="submit-btn">
                🤝 提交加入申请
            </button>
        </form>
    </div>
</div>

<script>
    function showApplicationForm(type) {
        // 移除所有active类
        document.querySelectorAll('.application-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.application-form').forEach(form => {
            form.classList.remove('active');
        });

        // 添加active类
        event.target.classList.add('active');
        
        if (type === 'create') {
            document.getElementById('create-form').classList.add('active');
        } else if (type === 'join') {
            document.getElementById('join-form').classList.add('active');
        }
    }

    // 帮会ID验证
    document.getElementById('guild_id').addEventListener('input', function() {
        const value = this.value;
        const regex = /^[a-zA-Z0-9_]+$/;
        
        if (value && !regex.test(value)) {
            this.setCustomValidity('帮会ID只能包含字母、数字和下划线');
        } else {
            this.setCustomValidity('');
        }
    });
</script>
{% endblock %}
