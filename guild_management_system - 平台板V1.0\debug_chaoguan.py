#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试潮光清泉数据问题
"""

import json
import os

def debug_chaoguan_data():
    """调试潮光清泉数据"""
    print("=" * 60)
    print("🔍 调试潮光清泉数据问题")
    print("=" * 60)
    
    # 加载战斗记录
    battle_records_file = 'data/battle_records.json'
    if not os.path.exists(battle_records_file):
        print("❌ 战斗记录文件不存在")
        return
    
    with open(battle_records_file, 'r', encoding='utf-8') as f:
        battle_records = json.load(f)
    
    print(f"📊 总共有 {len(battle_records)} 场战斗记录")
    
    # 查找潮光成员
    chaoguan_members = []
    for record in battle_records:
        for member_name, performance in record.get('member_performance', {}).items():
            if performance.get('profession') == '潮光':
                battle_data = performance.get('battle_data', {})
                chaoguan_members.append({
                    'name': member_name,
                    'qingquan': battle_data.get('qingquan', 0),
                    'resurrections': battle_data.get('resurrections', 0),
                    'position': performance.get('position', '未知'),
                    'score': performance.get('score', 0),
                    'details': performance.get('details', []),
                    'bonus_items': performance.get('bonus_items', []),
                    'penalty_items': performance.get('penalty_items', [])
                })
    
    print(f"🌊 找到 {len(chaoguan_members)} 个潮光成员数据")
    
    # 分析潮光数据
    for i, member in enumerate(chaoguan_members):
        print(f"\n--- 潮光成员 {i+1}: {member['name']} ---")
        print(f"清泉数据: {member['qingquan']}")
        print(f"羽化数据: {member['resurrections']}")
        print(f"职责位置: {member['position']}")
        print(f"评分: {member['score']:.1f}")
        print(f"评分详情: {member['details']}")
        print(f"加分项: {member['bonus_items']}")
        print(f"扣分项: {member['penalty_items']}")
    
    # 统计清泉数据
    qingquan_values = [m['qingquan'] for m in chaoguan_members]
    if qingquan_values:
        print(f"\n📈 清泉数据统计:")
        print(f"最小值: {min(qingquan_values)}")
        print(f"最大值: {max(qingquan_values)}")
        print(f"平均值: {sum(qingquan_values) / len(qingquan_values):.2f}")
        print(f"非零数量: {len([v for v in qingquan_values if v > 0])}")
    
    # 检查CSV原始数据
    print(f"\n🔍 检查CSV原始数据:")
    csv_file = 'data/20250531_203611__.csv'
    if os.path.exists(csv_file):
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
        
        current_guild = None
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            parts = [part.strip().replace('"', '') for part in line.split(',')]
            
            # 检查帮会名称
            if len(parts) >= 1 and parts[0] == '纸落云烟':
                current_guild = '纸落云烟'
                continue
            elif len(parts) >= 1 and parts[0] == '初影未来':
                break
            
            # 检查表头
            if len(parts) >= 2 and parts[0] == '玩家名字':
                continue
            
            # 处理成员数据
            if current_guild == '纸落云烟' and len(parts) >= 12:
                name = parts[0]
                profession = parts[1]
                if profession == '潮光':
                    qingquan = int(parts[10]) if parts[10].isdigit() else 0
                    resurrections = int(parts[10]) if parts[10].isdigit() else 0  # 同一列
                    print(f"CSV中潮光 {name}: 第10列={parts[10]}, 清泉={qingquan}")

if __name__ == '__main__':
    debug_chaoguan_data()
