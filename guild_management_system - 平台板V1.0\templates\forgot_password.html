<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘记密码 - 逆水寒帮会辅助管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .forgot-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            margin: 20px;
        }

        .forgot-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .forgot-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .forgot-header p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            color: #bdc3c7;
            font-size: 12px;
        }

        .step.active {
            color: #3498db;
            font-weight: 600;
        }

        .step.completed {
            color: #27ae60;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #bdc3c7;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 10px;
        }

        .step.active .step-number {
            background: #3498db;
        }

        .step.completed .step-number {
            background: #27ae60;
        }

        .step-divider {
            width: 40px;
            height: 2px;
            background: #bdc3c7;
            margin: 0 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .help-text {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .warning-box h4 {
            color: #856404;
            margin-bottom: 8px;
        }

        .warning-box p {
            color: #856404;
            font-size: 13px;
            margin: 0;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #3498db;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .admin-option {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }

        .admin-option h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .admin-option p {
            color: #6c757d;
            font-size: 13px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-header">
            <h1>🔐 忘记密码</h1>
            <p>通过验证绑定角色来重置密码</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step {% if not step or step == 'username' %}active{% elif step in ['verify', 'reset'] %}completed{% endif %}">
                <div class="step-number">1</div>
                <span>输入用户名</span>
            </div>
            <div class="step-divider"></div>
            <div class="step {% if step == 'verify' %}active{% elif step == 'reset' %}completed{% endif %}">
                <div class="step-number">2</div>
                <span>验证角色</span>
            </div>
            <div class="step-divider"></div>
            <div class="step {% if step == 'reset' %}active{% endif %}">
                <div class="step-number">3</div>
                <span>重置密码</span>
            </div>
        </div>

        <!-- 显示消息 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 第一步：输入用户名 -->
        {% if not step or step == 'username' %}
        <form method="POST">
            <input type="hidden" name="action" value="verify_character">
            
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" value="{{ username or '' }}" required>
            </div>
            
            <button type="submit" class="btn btn-primary">下一步</button>
        </form>
        {% endif %}

        <!-- 第二步：验证角色名 -->
        {% if step == 'verify' %}
        <div class="warning-box">
            <h4>⚠️ 安全验证</h4>
            <p>请输入您绑定的角色名进行身份验证。角色名必须完全匹配，区分大小写。</p>
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="verify_character">
            <input type="hidden" name="username" value="{{ username }}">
            
            <div class="form-group">
                <label for="character_name">绑定的角色名</label>
                <input type="text" id="character_name" name="character_name" required 
                       placeholder="请输入完整的角色名">
                <div class="help-text">请输入您在系统中绑定的游戏角色名称</div>
            </div>
            
            <button type="submit" class="btn btn-primary">验证角色</button>
        </form>

        <!-- 管理员重置选项 -->
        {% if show_admin_option %}
        <div class="admin-option">
            <h4>🆘 申请管理员重置</h4>
            <p>如果您忘记了角色名或角色名验证失败，可以申请管理员帮助重置密码。</p>
            
            <form method="POST">
                <input type="hidden" name="action" value="request_admin_reset">
                <input type="hidden" name="username" value="{{ username }}">
                
                <div class="form-group">
                    <label for="reason">申请理由</label>
                    <textarea id="reason" name="reason" rows="3" required 
                              placeholder="请说明为什么需要管理员重置密码..."></textarea>
                </div>
                
                <button type="submit" class="btn btn-warning">申请管理员重置</button>
            </form>
        </div>
        {% endif %}
        {% endif %}

        <!-- 第三步：重置密码 -->
        {% if step == 'reset' and verified %}
        <div class="alert alert-success">
            ✅ 角色验证成功！现在可以设置新密码。
        </div>

        <form method="POST">
            <input type="hidden" name="action" value="reset_password">
            <input type="hidden" name="username" value="{{ username }}">
            <input type="hidden" name="verified" value="true">
            
            <div class="form-group">
                <label for="new_password">新密码</label>
                <input type="password" id="new_password" name="new_password" required minlength="6">
                <div class="help-text">密码长度至少6位</div>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">确认新密码</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
            
            <button type="submit" class="btn btn-primary">重置密码</button>
        </form>
        {% endif %}

        <div class="back-link">
            <a href="{{ url_for('login') }}">← 返回登录页面</a>
        </div>
    </div>

    <script>
        // 密码确认验证
        const confirmPassword = document.getElementById('confirm_password');
        if (confirmPassword) {
            confirmPassword.addEventListener('input', function() {
                const newPassword = document.getElementById('new_password').value;
                const confirmValue = this.value;
                
                if (newPassword !== confirmValue) {
                    this.setCustomValidity('密码确认不匹配');
                } else {
                    this.setCustomValidity('');
                }
            });
            
            document.getElementById('new_password').addEventListener('input', function() {
                if (confirmPassword.value) {
                    confirmPassword.dispatchEvent(new Event('input'));
                }
            });
        }
    </script>
</body>
</html>
