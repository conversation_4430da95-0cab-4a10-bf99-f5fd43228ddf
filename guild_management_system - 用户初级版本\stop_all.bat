@echo off
chcp 65001 >nul

echo 🛑 停止纸落云烟帮会管理系统...

REM 停止Python进程
echo 🔍 查找并停止相关进程...

REM 停止管理后台
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq 管理后台*" /fo csv ^| find "cmd.exe"') do (
    echo ✅ 停止管理后台进程 %%i
    taskkill /pid %%i /f >nul 2>&1
)

REM 停止API服务
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq API服务*" /fo csv ^| find "cmd.exe"') do (
    echo ✅ 停止API服务进程 %%i
    taskkill /pid %%i /f >nul 2>&1
)

REM 停止成员访问服务
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq 成员访问*" /fo csv ^| find "cmd.exe"') do (
    echo ✅ 停止成员访问服务进程 %%i
    taskkill /pid %%i /f >nul 2>&1
)

REM 停止所有相关的Python进程
tasklist | find "python.exe" >nul
if not errorlevel 1 (
    echo 🔍 停止所有Python进程...
    taskkill /f /im python.exe >nul 2>&1
)

echo.
echo 🎉 所有服务已停止！
pause
