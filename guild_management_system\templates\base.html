<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}逆水寒帮会辅助管理系统{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .nav-tabs {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-tab {
            display: inline-block;
            padding: 12px 25px;
            margin: 0 5px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: #555;
        }

        .nav-tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        /* 下拉菜单样式 */
        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dropdown-arrow {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .dropdown-toggle.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 180px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-menu a {
            display: block;
            padding: 12px 16px;
            color: #2c3e50;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .dropdown-menu a:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(5px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .team-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-5px);
        }

        .team-header {
            padding: 20px;
            color: white;
            font-weight: bold;
            font-size: 1.3em;
            text-align: center;
        }

        .team-1 .team-header { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .team-2 .team-header { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .team-3 .team-header { background: linear-gradient(135deg, #45b7d1, #96c93d); }
        .team-4 .team-header { background: linear-gradient(135deg, #96ceb4, #ffecd2); color: #333; }

        .team-content {
            padding: 20px;
        }

        .member-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .member-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .member-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .member-details {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .profession-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 5px;
        }

        .profession-素问 { background: #ffebee; color: #c62828; }
        .profession-铁衣 { background: #fff3e0; color: #ef6c00; }
        .profession-潮光 { background: #e1f5fe; color: #0277bd; }
        .profession-九灵 { background: #f3e5f5; color: #7b1fa2; }
        .profession-龙吟 { background: #e8f5e8; color: #2e7d32; }
        .profession-血河 { background: #ffebee; color: #d32f2f; }
        .profession-碎梦 { background: #e0f2f1; color: #00695c; }
        .profession-玄机 { background: #fff9c4; color: #f57f17; }
        .profession-神相 { background: #e8eaf6; color: #3f51b5; }
        .profession-沧澜 { background: #f1f8e9; color: #558b2f; }

        .edit-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .edit-btn:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .search-box {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .search-box:focus {
            outline: none;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .hidden {
            display: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h1>🏮 逆水寒帮会辅助管理系统</h1>
                    {% set current_user = get_current_user() %}
                    {% if is_super_admin() %}
                        <div class="subtitle">帮会管理 | 申请审批 | 用户管理</div>
                    {% elif current_user and current_user.role == 'user' and not current_user.guild_id %}
                        <div class="subtitle">创建帮会 | 申请加入 | 帮会管理</div>
                    {% else %}
                        <div class="subtitle">成员详情 | 团队配置 | 数据统计</div>
                    {% endif %}
                </div>
                {% if get_current_user() %}
                <div style="text-align: right;">
                    <div style="margin-bottom: 10px;">
                        {% set bound_character = get_user_bound_character_name() %}
                        {% if bound_character %}
                            <span style="color: #667eea; font-weight: bold;">{{ bound_character }}</span>
                            <span style="margin-left: 5px; color: #7f8c8d; font-size: 12px;">({{ get_current_user().name }})</span>
                        {% else %}
                            <span style="color: #667eea; font-weight: bold;">{{ get_current_user().name }}</span>
                        {% endif %}
                        <span style="margin-left: 10px; padding: 4px 8px; background: {% if is_super_admin() %}#dc3545{% elif is_guild_leader() %}#ff6b6b{% elif is_guest() %}#6c757d{% else %}#4ecdc4{% endif %}; color: white; border-radius: 12px; font-size: 12px;">
                            {% if is_super_admin() %}超级管理员{% elif is_guild_leader() %}大当家{% elif is_guest() %}游客{% else %}普通用户{% endif %}
                        </span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        {% if not is_guest() %}
                        <a href="{{ url_for('user_profile') }}" style="color: #3498db; text-decoration: none; font-size: 14px; margin-right: 15px;">
                            ⚙️ 个人设置
                        </a>
                        {% endif %}
                        <a href="{{ url_for('logout') }}" style="color: #dc3545; text-decoration: none; font-size: 14px;">
                            🚪 退出登录
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="nav-tabs">
            {% if is_super_admin() %}
                <!-- 超级管理员导航 -->
                <button class="nav-tab" onclick="showPage('super_admin')">🏛️ 帮会管理</button>
                <button class="nav-tab" onclick="showPage('admin_applications')">📋 申请审批</button>
                <button class="nav-tab" onclick="showPage('admin_users')">👥 用户管理</button>
                <button class="nav-tab" onclick="showPage('admin_settings')">⚙️ 系统设置</button>
            {% else %}
                {% set current_user = get_current_user() %}
                {% if current_user and current_user.role == 'user' and not current_user.guild_id %}
                    <!-- 无帮会的普通用户只能看到帮会申请 -->
                    <button class="nav-tab" onclick="showPage('guild_application')">🏛️ 帮会申请</button>
                {% else %}
                    <!-- 有帮会的用户或游客可以看到帮会管理功能 -->

                    <!-- 帮会管理下拉菜单 -->
                    <div class="nav-dropdown">
                        <button class="nav-tab dropdown-toggle" onclick="toggleDropdown(this)">
                            📊 帮会管理 <span class="dropdown-arrow">▼</span>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" onclick="showPage('index')">📈 数据总览</a>
                            {% if can_edit() %}
                            <a href="#" onclick="showPage('drag')">🎯 拖拽排表</a>
                            {% endif %}
                            <a href="#" onclick="showPage('members')">👥 成员详情</a>
                            <a href="#" onclick="showPage('organization')">🏗️ 组织架构</a>
                        </div>
                    </div>

                    <!-- 战斗分析（独立） -->
                    <button class="nav-tab" onclick="showPage('battle')">⚔️ 战斗分析</button>

                    <!-- 用户中心下拉菜单 -->
                    {% if current_user and current_user.role != 'guest' %}
                    <div class="nav-dropdown">
                        <button class="nav-tab dropdown-toggle" onclick="toggleDropdown(this)">
                            👤 用户中心 <span class="dropdown-arrow">▼</span>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" onclick="showPage('character_binding')">🔗 角色绑定</a>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 系统管理下拉菜单（仅管理员可见） -->
                    {% if is_guild_leader() %}
                    <div class="nav-dropdown">
                        <button class="nav-tab dropdown-toggle" onclick="toggleDropdown(this)">
                            ⚙️ 系统管理 <span class="dropdown-arrow">▼</span>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" onclick="showPage('admin_applications')">📋 申请管理</a>
                            <a href="#" onclick="showPage('admin_character_bindings')">👤 绑定审核</a>
                            <a href="#" onclick="showPage('admin_kick_management')">🚪 踢出管理</a>
                            <a href="#" onclick="showPage('notifications')">📢 通知中心</a>
                        </div>
                    </div>
                    {% endif %}
                {% endif %}
            {% endif %}
        </div>

        {% block content %}{% endblock %}
    </div>

    {% block scripts %}{% endblock %}

    <script>
        // 下拉菜单功能
        function toggleDropdown(button) {
            const dropdown = button.nextElementSibling;
            const isOpen = dropdown.classList.contains('show');

            // 关闭所有其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
                toggle.classList.remove('active');
            });

            // 切换当前下拉菜单
            if (!isOpen) {
                dropdown.classList.add('show');
                button.classList.add('active');
            }
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.nav-dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
                document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
                    toggle.classList.remove('active');
                });
            }
        });

        function showPage(page) {
            // 关闭所有下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
            document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
                toggle.classList.remove('active');
            });

            // 移除所有active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 添加active类到当前标签
            if (event.target) {
                event.target.classList.add('active');
            }

            // 根据页面类型跳转
            if (page === 'index') {
                window.location.href = '/';
            } else if (page === 'super_admin') {
                window.location.href = '/super_admin';
            } else if (page === 'drag') {
                window.location.href = '/drag_board';
            } else if (page === 'members') {
                window.location.href = '/members';
            } else if (page === 'organization') {
                window.location.href = '/organization_chart';
            } else if (page === 'battle') {
                window.location.href = '/battle_analysis';
            } else if (page === 'guild_application') {
                window.location.href = '/guild_application';
            } else if (page === 'character_binding') {
                window.location.href = '/character_binding';
            } else if (page === 'admin_applications') {
                window.location.href = '/admin/applications';
            } else if (page === 'admin_character_bindings') {
                window.location.href = '/admin/character_bindings';
            } else if (page === 'admin_kick_management') {
                window.location.href = '/admin/kick_management';
            } else if (page === 'admin_users') {
                window.location.href = '/admin/users';
            } else if (page === 'admin_settings') {
                window.location.href = '/admin/settings';
            } else if (page === 'notifications') {
                window.location.href = '/notifications';
            }
        }

        // 根据当前页面设置active标签
        function setActiveTab() {
            const path = window.location.pathname;
            const tabs = document.querySelectorAll('.nav-tab');

            tabs.forEach(tab => tab.classList.remove('active'));

            // 根据按钮的onclick属性来匹配，而不是索引
            tabs.forEach(tab => {
                const onclick = tab.getAttribute('onclick');
                if (!onclick) return;

                if ((path === '/' || path === '/index') && onclick.includes("'index'")) {
                    tab.classList.add('active');  // 总览
                } else if (path === '/super_admin' && onclick.includes("'super_admin'")) {
                    tab.classList.add('active');  // 超级管理员
                } else if (path === '/drag_board' && onclick.includes("'drag'")) {
                    tab.classList.add('active');  // 拖拽排表
                } else if (path === '/members' && onclick.includes("'members'")) {
                    tab.classList.add('active');  // 成员详情
                } else if (path === '/organization_chart' && onclick.includes("'organization'")) {
                    tab.classList.add('active');  // 组织架构
                } else if (path === '/battle_analysis' && onclick.includes("'battle'")) {
                    tab.classList.add('active');  // 战斗分析
                } else if (path === '/guild_application' && onclick.includes("'guild_application'")) {
                    tab.classList.add('active');  // 帮会申请
                } else if (path === '/character_binding' && onclick.includes("'character_binding'")) {
                    tab.classList.add('active');  // 角色绑定
                } else if (path === '/admin/applications' && onclick.includes("'admin_applications'")) {
                    tab.classList.add('active');  // 申请管理
                } else if (path === '/admin/character_bindings' && onclick.includes("'admin_character_bindings'")) {
                    tab.classList.add('active');  // 绑定审核
                } else if (path === '/admin/kick_management' && onclick.includes("'admin_kick_management'")) {
                    tab.classList.add('active');  // 踢出管理
                } else if (path === '/admin/users' && onclick.includes("'admin_users'")) {
                    tab.classList.add('active');  // 用户管理
                } else if (path === '/admin/settings' && onclick.includes("'admin_settings'")) {
                    tab.classList.add('active');  // 系统设置
                } else if (path === '/notifications' && onclick.includes("'notifications'")) {
                    tab.classList.add('active');  // 通知中心
                }
            });
        }

        // 页面加载时设置active标签
        document.addEventListener('DOMContentLoaded', setActiveTab);
    </script>
</body>
</html>
