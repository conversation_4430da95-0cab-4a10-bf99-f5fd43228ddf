# 纸落云烟帮会管理系统 - 权限系统

## 🎉 权限系统已成功实现！

现在系统使用统一的应用，通过登录权限控制不同用户看到不同的功能，这比之前创建两套系统要简洁高效得多。

## 🔐 用户账号

### 管理员账号（完整权限）
- **admin** / admin123 - 系统管理员
- **danggjia** / danggjia123 - 当家

### 普通用户账号（只读权限）
- **user** / user123 - 普通成员

### 游客访问（无需登录）
- **游客访问** - 点击按钮即可，无需密码

## ✨ 权限功能对比

### 👑 管理员权限（admin/danggjia）
- ✅ 查看所有页面（总览、成员详情、组织架构、战斗分析、拖拽排表）
- ✅ 成员管理（添加、编辑、删除成员）
- ✅ 拖拽排表功能
- ✅ 战斗数据上传和删除
- ✅ 团队配置管理
- ✅ 所有编辑操作

### 👤 普通用户权限（user）
- ✅ 查看页面（总览、成员详情、组织架构、战斗分析）
- ❌ 拖拽排表（导航中隐藏）
- ❌ 战斗数据上传（显示查看模式横幅）
- ❌ 删除战斗记录（按钮隐藏）
- ❌ 成员编辑操作（API返回403）
- ❌ 团队配置管理（API返回403）

### 👁️ 游客权限（guest）
- ✅ 查看页面（总览、成员详情、组织架构、战斗分析）
- ❌ 拖拽排表（导航中隐藏）
- ❌ 战斗数据上传（显示游客模式横幅）
- ❌ 删除战斗记录（按钮隐藏）
- ❌ 所有编辑操作（API返回403）
- 🔄 无需登录，直接访问

## 🛡️ 安全机制

### 1. 路由级权限控制
- `@login_required` - 需要登录才能访问
- `@admin_required` - 需要管理员权限才能访问

### 2. 模板权限控制
- `{% if is_admin() %}` - 管理员功能显示控制
- `{% if get_current_user() %}` - 登录状态检查

### 3. API权限保护
- 所有编辑类API都需要管理员权限
- 未授权访问返回403错误和友好提示

## 🎨 界面特性

### 用户信息显示
- 右上角显示当前用户名和角色
- 管理员显示红色标签，普通用户显示蓝色标签
- 退出登录链接

### 导航菜单
- 管理员：显示所有5个功能页面
- 普通用户：隐藏拖拽排表，只显示4个页面

### 战斗分析页面
- 管理员：显示上传功能和删除按钮
- 普通用户：显示查看模式横幅，隐藏上传和删除功能

## 🚀 使用方法

### 1. 启动系统
```bash
# Windows
python app.py

# 或使用现有脚本
start.bat
```

### 2. 访问系统
- 地址：http://localhost:5888
- 首次访问会自动跳转到登录页面

### 3. 登录测试
- 使用管理员账号体验完整功能
- 使用普通用户账号体验只读模式

## 🔧 技术实现

### Session管理
- 使用Flask Session存储登录状态
- 安全密钥：`zhiluoyunyan_guild_management_2024`

### 权限装饰器
```python
@login_required  # 需要登录
@admin_required  # 需要管理员权限
```

### 模板函数
```python
get_current_user()  # 获取当前用户信息
is_admin()         # 检查是否为管理员
```

## 📋 修改的文件

### 核心文件
- `app.py` - 添加认证系统和权限控制
- `templates/login.html` - 新增登录页面
- `templates/base.html` - 添加用户信息和权限控制
- `templates/battle_analysis.html` - 权限控制更新

### 权限控制
- 所有需要管理员权限的路由都添加了 `@admin_required` 装饰器
- 所有页面都添加了 `@login_required` 装饰器
- 模板中使用 `{% if is_admin() %}` 控制功能显示

## 🎯 优势

1. **统一系统** - 不再需要维护两套代码
2. **灵活权限** - 可以轻松添加更多用户和权限级别
3. **安全可靠** - 多层权限保护，防止未授权操作
4. **用户友好** - 清晰的权限提示和界面区分
5. **易于扩展** - 可以轻松添加新的权限级别和功能

## 🔮 未来扩展

- 可以添加更多权限级别（如副帮主、队长等）
- 可以实现更细粒度的权限控制（如只能管理特定团队）
- 可以添加用户注册和密码修改功能
- 可以集成LDAP或其他认证系统

现在您有了一个完整的权限管理系统，既保证了数据安全，又提供了良好的用户体验！
