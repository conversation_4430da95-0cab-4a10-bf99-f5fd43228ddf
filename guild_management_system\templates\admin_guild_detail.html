{% extends "base.html" %}

{% block title %}{{ guild.name }} - 帮会管理{% endblock %}

{% block content %}
<style>
    .guild-detail-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .guild-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .guild-title {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .guild-title h1 {
        color: #2c3e50;
        margin: 0;
        font-size: 2.2em;
    }

    .guild-id {
        background: #e9ecef;
        color: #495057;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
    }

    .back-btn {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        display: inline-block;
        transition: background 0.3s ease;
    }

    .back-btn:hover {
        background: #5a6268;
        color: white;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
    }

    .stat-card h3 {
        margin: 0 0 5px 0;
        font-size: 2em;
    }

    .stat-card p {
        margin: 0;
        opacity: 0.9;
    }

    .section {
        margin-bottom: 40px;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.5em;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .info-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .info-card h4 {
        margin: 0 0 10px 0;
        color: #2c3e50;
    }

    .info-card p {
        margin: 5px 0;
        color: #666;
    }

    .members-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .member-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #28a745;
    }

    .member-card.banned {
        border-left-color: #dc3545;
    }

    .member-name {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .member-role {
        font-size: 12px;
        color: #666;
    }

    .applications-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .application-card {
        background: #fff3cd;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #ffc107;
    }

    .application-card.approved {
        background: #d4edda;
        border-left-color: #28a745;
    }

    .application-card.rejected {
        background: #f8d7da;
        border-left-color: #dc3545;
    }

    .no-data {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
    }
</style>

<div class="guild-detail-container">
    <div class="guild-header">
        <div class="guild-title">
            <h1>🏰 {{ guild.name }}</h1>
            <span class="guild-id">{{ guild_id }}</span>
        </div>
        <a href="{{ url_for('super_admin_dashboard') }}" class="back-btn">
            ← 返回帮会列表
        </a>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>{{ stats.total_members }}</h3>
            <p>👥 总成员数</p>
        </div>
        <div class="stat-card">
            <h3>{{ stats.active_members }}</h3>
            <p>✅ 活跃成员</p>
        </div>
        <div class="stat-card">
            <h3>{{ stats.pending_applications }}</h3>
            <p>⏳ 待审申请</p>
        </div>
        <div class="stat-card">
            <h3>{{ stats.total_applications }}</h3>
            <p>📋 总申请数</p>
        </div>
    </div>

    <!-- 帮会信息 -->
    <div class="section">
        <h2 class="section-title">📊 帮会信息</h2>
        <div class="info-grid">
            <div class="info-card">
                <h4>基本信息</h4>
                <p><strong>帮会名称:</strong> {{ guild.name }}</p>
                <p><strong>帮会ID:</strong> {{ guild_id }}</p>
                <p><strong>大当家:</strong> {{ guild.leader }}</p>
                <p><strong>状态:</strong> {{ '活跃' if guild.status == 'active' else '停用' }}</p>
            </div>
            <div class="info-card">
                <h4>成员统计</h4>
                <p><strong>当前成员:</strong> {{ stats.total_members }}</p>
                <p><strong>最大成员:</strong> {{ guild.max_members or 100 }}</p>
                <p><strong>使用率:</strong> {{ "%.1f" | format((stats.total_members / (guild.max_members or 100)) * 100) }}%</p>
            </div>
            <div class="info-card">
                <h4>时间信息</h4>
                <p><strong>创建时间:</strong> {{ guild.created_time[:10] if guild.created_time else '未知' }}</p>
                <p><strong>描述:</strong> {{ guild.description or '暂无描述' }}</p>
            </div>
        </div>
    </div>

    <!-- 帮会成员 -->
    <div class="section">
        <h2 class="section-title">👥 帮会成员</h2>
        {% if guild_users %}
            <div class="members-list">
                {% for user in guild_users %}
                <div class="member-card {{ 'banned' if user.status == 'banned' else '' }}">
                    <div class="member-name">{{ user.name or user.username }}</div>
                    <div class="member-role">
                        {% if user.role == 'guild_leader' %}🏆 大当家
                        {% elif user.role == 'super_admin' %}👑 超级管理员
                        {% else %}👤 普通成员{% endif %}
                    </div>
                    {% if user.email %}
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">{{ user.email }}</div>
                    {% endif %}
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        状态: {{ '正常' if user.status == 'active' else '封禁' if user.status == 'banned' else '停用' }}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-data">
                <h3>👤 暂无成员</h3>
                <p>该帮会还没有任何成员</p>
            </div>
        {% endif %}
    </div>

    <!-- 申请记录 -->
    <div class="section">
        <h2 class="section-title">📋 申请记录</h2>
        {% if guild_applications %}
            <div class="applications-list">
                {% for app in guild_applications %}
                <div class="application-card {{ app.status }}">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong>{{ app.username }}</strong>
                        <span style="font-size: 12px; color: #666;">
                            {{ app.created_time.strftime('%Y-%m-%d %H:%M') if app.created_time else '未知时间' }}
                        </span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>申请类型:</strong> 
                        {% if app.application_type == 'join_guild' %}加入帮会{% else %}创建帮会{% endif %}
                    </div>
                    {% if app.description %}
                    <div style="margin-bottom: 10px;">
                        <strong>申请说明:</strong> {{ app.description }}
                    </div>
                    {% endif %}
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="font-weight: bold;">
                            状态: 
                            {% if app.status == 'pending' %}⏳ 待审批
                            {% elif app.status == 'approved' %}✅ 已通过
                            {% else %}❌ 已拒绝{% endif %}
                        </span>
                        {% if app.status != 'pending' %}
                        <span style="font-size: 12px; color: #666;">
                            处理人: {{ app.processed_by or '未知' }}
                        </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-data">
                <h3>📭 暂无申请</h3>
                <p>该帮会还没有任何申请记录</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
