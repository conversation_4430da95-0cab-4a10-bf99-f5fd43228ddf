{% extends "base.html" %}

{% block title %}组织架构 - 纸落云烟{% endblock %}

{% block content %}
<style>
    .readonly-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .readonly-notice {
        background: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
        text-align: center;
        border: 1px solid #c3e6cb;
    }
    
    .organization-container {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }
    
    .main-group {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .main-group:hover {
        border-color: #28a745;
        transform: translateY(-2px);
    }
    
    .group-header {
        padding: 20px;
        font-size: 20px;
        font-weight: bold;
        color: white;
        text-align: center;
        position: relative;
    }
    
    .attack-group .group-header {
        background: linear-gradient(135deg, #dc3545, #c82333);
    }
    
    .defense-group .group-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
    }
    
    .other-group .group-header {
        background: linear-gradient(135deg, #6c757d, #545b62);
    }
    
    .group-content {
        padding: 20px;
    }
    
    .sub-teams {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    
    .sub-team {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .sub-team:hover {
        border-color: #28a745;
        background: #e8f5e8;
    }
    
    .sub-team-header {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #dee2e6;
        text-align: center;
    }
    
    .squads {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .squad {
        background: white;
        border-radius: 8px;
        padding: 12px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .squad:hover {
        border-color: #28a745;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
    }
    
    .squad-header {
        font-size: 14px;
        font-weight: bold;
        color: #495057;
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .squad-count {
        background: #28a745;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
    }
    
    .members-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .member-tag {
        background: linear-gradient(135deg, #e9ecef, #f8f9fa);
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .member-tag:hover {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        transform: translateY(-1px);
    }
    
    .profession-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .empty-squad {
        color: #6c757d;
        font-style: italic;
        text-align: center;
        padding: 10px;
    }
    
    .stats-summary {
        background: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        text-align: center;
    }
    
    .stat-item {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .stat-item:hover {
        border-color: #28a745;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #28a745;
    }
    
    .stat-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
</style>

<div class="readonly-header">
    <h2><i class="fas fa-sitemap"></i> 组织架构</h2>
    <p>查看公会组织结构和人员分配 | 只读模式</p>
</div>

<div class="readonly-notice">
    <i class="fas fa-info-circle"></i> 当前为只读模式，仅可查看组织架构，无法进行编辑操作
</div>

<!-- 统计摘要 -->
<div class="stats-summary">
    <h4 style="text-align: center; margin-bottom: 15px;">组织架构统计</h4>
    <div class="stats-grid">
        {% set attack_count = organization.get('进攻团', {}).values() | sum(attribute='values') | sum(attribute='length') %}
        {% set defense_count = organization.get('防守团', {}).values() | sum(attribute='values') | sum(attribute='length') %}
        {% set other_count = organization.get('其他团', {}).values() | sum(attribute='values') | sum(attribute='length') %}
        
        <div class="stat-item">
            <div class="stat-number">{{ members | length }}</div>
            <div class="stat-label">总成员数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ organization.keys() | length }}</div>
            <div class="stat-label">主要团队</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">
                {% set total_teams = 0 %}
                {% for group in organization.values() %}
                    {% set total_teams = total_teams + group.keys() | length %}
                {% endfor %}
                {{ total_teams }}
            </div>
            <div class="stat-label">子团队数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">
                {% set total_squads = 0 %}
                {% for group in organization.values() %}
                    {% for team in group.values() %}
                        {% set total_squads = total_squads + team.keys() | length %}
                    {% endfor %}
                {% endfor %}
                {{ total_squads }}
            </div>
            <div class="stat-label">小队数量</div>
        </div>
    </div>
</div>

<!-- 组织架构 -->
<div class="organization-container">
    {% for main_group, sub_teams in organization.items() %}
    <div class="main-group {% if main_group == '进攻团' %}attack-group{% elif main_group == '防守团' %}defense-group{% else %}other-group{% endif %}">
        <div class="group-header">
            {% if main_group == '进攻团' %}
                <i class="fas fa-sword"></i>
            {% elif main_group == '防守团' %}
                <i class="fas fa-shield-alt"></i>
            {% else %}
                <i class="fas fa-users"></i>
            {% endif %}
            {{ main_group }}
            <span style="font-size: 14px; opacity: 0.8; margin-left: 10px;">
                ({{ sub_teams.values() | sum(attribute='values') | sum(attribute='length') }} 人)
            </span>
        </div>
        
        <div class="group-content">
            {% if sub_teams %}
            <div class="sub-teams">
                {% for sub_team, squads in sub_teams.items() %}
                <div class="sub-team">
                    <div class="sub-team-header">
                        {{ sub_team }}
                        <span style="font-size: 12px; color: #666;">
                            ({{ squads.values() | sum(attribute='length') }} 人)
                        </span>
                    </div>
                    
                    <div class="squads">
                        {% for squad, members_in_squad in squads.items() %}
                        <div class="squad">
                            <div class="squad-header">
                                <span>{{ squad }}</span>
                                <span class="squad-count">{{ members_in_squad | length }}</span>
                            </div>
                            
                            {% if members_in_squad %}
                            <div class="members-list">
                                {% for member in members_in_squad %}
                                <div class="member-tag" title="{{ member.profession }} - {{ member.position }}">
                                    <span class="profession-indicator" 
                                          style="background-color: {{ get_profession_color(member.profession) }};"></span>
                                    {{ member.name }}
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="empty-squad">暂无成员</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div style="text-align: center; color: #666; padding: 20px;">
                <i class="fas fa-users-slash" style="font-size: 48px; margin-bottom: 10px;"></i>
                <p>该团队暂无成员</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>

<script>
// 成员标签点击事件
document.querySelectorAll('.member-tag').forEach(tag => {
    tag.addEventListener('click', function() {
        const memberName = this.textContent.trim();
        // 在只读模式下，只显示提示信息
        alert(`成员：${memberName}\n当前为只读模式，如需查看详细信息请访问成员详情页面`);
    });
});

// 只读模式提示
console.log('🛡️ 组织架构 - 只读模式');
</script>

{% endblock %}
