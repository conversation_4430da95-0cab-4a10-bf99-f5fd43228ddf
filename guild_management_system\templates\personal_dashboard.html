{% extends "base.html" %}

{% block title %}个人首页 - {{ character.name }} - 逆水寒帮会辅助管理系统{% endblock %}

{% block content %}
<style>
    .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .welcome-banner {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .character-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
        margin-bottom: 20px;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-icon {
        font-size: 40px;
        margin-bottom: 15px;
    }
    
    .stat-value {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #2c3e50;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-size: 14px;
    }
    
    .trend-up { color: #27ae60; }
    .trend-down { color: #e74c3c; }
    .trend-stable { color: #f39c12; }
    
    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }
    
    .info-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .info-card h3 {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .character-info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ecf0f1;
    }
    
    .character-info-item:last-child {
        border-bottom: none;
    }
    
    .profession-badge {
        padding: 6px 12px;
        border-radius: 15px;
        color: white;
        font-size: 12px;
        font-weight: bold;
    }
    
    .recent-battles {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .battle-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .battle-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
    
    .battle-score {
        font-size: 18px;
        font-weight: bold;
        padding: 8px 15px;
        border-radius: 20px;
        color: white;
    }
    
    .score-excellent { background: #27ae60; }
    .score-good { background: #f39c12; }
    .score-average { background: #95a5a6; }
    
    .radar-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .achievement-badge {
        display: inline-block;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #8b7500;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: bold;
        margin: 5px;
        box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    }
    
    .suggestion-card {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 15px;
        padding: 25px;
        margin-top: 20px;
        border-left: 5px solid #2196f3;
    }
    
    .suggestion-title {
        color: #1976d2;
        font-weight: bold;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }
</style>

<div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
        <div class="character-avatar">
            🎮
        </div>
        <h1 style="margin: 0 0 10px 0; font-size: 32px;">欢迎回来，{{ character.name }}！</h1>
        <p style="margin: 0; font-size: 18px; opacity: 0.9;">
            {{ character.profession }} | {{ character.main_group or '未分配' }} - {{ character.sub_team or '未分配' }}
        </p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">⚔️</div>
            <div class="stat-value">{{ stats.total_battles }}</div>
            <div class="stat-label">参与战斗</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-value">{{ stats.avg_score }}</div>
            <div class="stat-label">平均评分</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                {% if stats.recent_trend == 'improving' %}
                    <span class="trend-up">📈</span>
                {% elif stats.recent_trend == 'declining' %}
                    <span class="trend-down">📉</span>
                {% else %}
                    <span class="trend-stable">📊</span>
                {% endif %}
            </div>
            <div class="stat-value">
                {% if stats.recent_trend == 'improving' %}
                    上升
                {% elif stats.recent_trend == 'declining' %}
                    下降
                {% else %}
                    稳定
                {% endif %}
            </div>
            <div class="stat-label">近期趋势</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">🏆</div>
            <div class="stat-value">{{ stats.profession_rank }}</div>
            <div class="stat-label">职业排名</div>
        </div>
    </div>
    
    <!-- 信息网格 -->
    <div class="info-grid">
        <!-- 角色信息 -->
        <div class="info-card">
            <h3><i class="fas fa-user"></i> 角色信息</h3>
            
            <div class="character-info-item">
                <span>角色名称</span>
                <strong>{{ character.name }}</strong>
            </div>
            
            <div class="character-info-item">
                <span>职业</span>
                <span class="profession-badge" style="background-color: 
                    {% if character.profession == '素问' %}#e91e63
                    {% elif character.profession == '九灵' %}#9c27b0
                    {% elif character.profession == '潮光' %}#2196f3
                    {% elif character.profession == '血河' %}#f44336
                    {% elif character.profession == '神相' %}#3f51b5
                    {% elif character.profession == '玄机' %}#ff9800
                    {% elif character.profession == '铁衣' %}#ff5722
                    {% elif character.profession == '龙吟' %}#4caf50
                    {% elif character.profession == '碎梦' %}#009688
                    {% else %}#6c757d{% endif %}">
                    {{ character.profession }}
                </span>
            </div>
            
            <div class="character-info-item">
                <span>所属团队</span>
                <span>{{ character.main_group or '未分配' }}</span>
            </div>
            
            <div class="character-info-item">
                <span>子团队</span>
                <span>{{ character.sub_team or '未分配' }}</span>
            </div>
            
            <div class="character-info-item">
                <span>小队</span>
                <span>{{ character.squad or '未分配' }}</span>
            </div>
            
            <div class="character-info-item">
                <span>位置</span>
                <span>{{ character.position or '未分配' }}</span>
            </div>
        </div>
        
        <!-- 成就展示 -->
        <div class="info-card">
            <h3><i class="fas fa-trophy"></i> 个人成就</h3>
            
            {% if stats.best_performance %}
            <div style="margin-bottom: 20px;">
                <h5 style="color: #f39c12; margin-bottom: 10px;">🌟 最佳表现</h5>
                <p style="margin: 0; font-size: 14px; color: #7f8c8d;">
                    {{ stats.best_performance.battle_date or '未知日期' }} - 评分: {{ stats.best_performance.total_score }}
                </p>
            </div>
            {% endif %}
            
            <div>
                <h5 style="color: #27ae60; margin-bottom: 10px;">🏅 获得徽章</h5>
                {% if stats.total_battles >= 10 %}
                <span class="achievement-badge">战斗老兵</span>
                {% endif %}
                {% if stats.avg_score >= 80 %}
                <span class="achievement-badge">精英战士</span>
                {% endif %}
                {% if stats.recent_trend == 'improving' %}
                <span class="achievement-badge">进步之星</span>
                {% endif %}
                {% if not (stats.total_battles >= 10 or stats.avg_score >= 80 or stats.recent_trend == 'improving') %}
                <p style="color: #7f8c8d; font-style: italic; margin: 0;">继续努力，获得更多成就！</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 最近战斗记录 -->
    <div class="recent-battles">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50; display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-history"></i> 最近战斗记录
        </h3>
        
        {% if battles %}
        {% for battle in battles[-5:] %}
        <div class="battle-item">
            <div>
                <strong>{{ battle.battle_date or '未知日期' }} vs {{ battle.enemy_guild or '未知对手' }}</strong>
                <div style="font-size: 14px; color: #7f8c8d; margin-top: 4px;">
                    击杀: {{ battle.kills or 0 }} | 助攻: {{ battle.assists or 0 }} | 人伤: {{ (battle.player_damage or 0) // 10000 }}万
                </div>
                <div style="font-size: 12px; color: #95a5a6; margin-top: 2px;">
                    拆塔: {{ (battle.demolition or 0) // 10000 }}万 | 治疗: {{ (battle.healing or 0) // 10000 }}万 | 重伤: {{ battle.heavy_injuries or 0 }}次
                </div>
            </div>
            <div class="battle-score
                {% if battle.total_score|float >= 80 %}score-excellent
                {% elif battle.total_score|float >= 60 %}score-good
                {% else %}score-average{% endif %}">
                {{ battle.total_score or 0 }}
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="empty-state">
            <i class="fas fa-chart-line"></i>
            <h4>暂无战斗记录</h4>
            <p>参与帮战后，您的战斗数据将在这里显示</p>
        </div>
        {% endif %}
    </div>
    
    <!-- 个性化建议 -->
    {% if battles %}
    <div class="suggestion-card">
        <div class="suggestion-title">
            <i class="fas fa-lightbulb"></i> 个性化建议
        </div>
        <div style="font-size: 14px; line-height: 1.8; color: #2c3e50;">
            {% if stats.suggestions %}
                {% for suggestion in stats.suggestions %}
                <div style="margin-bottom: 8px; padding: 6px 0; border-left: 3px solid #3498db; padding-left: 12px;">
                    {{ suggestion }}
                </div>
                {% endfor %}
            {% else %}
                <div style="color: #7f8c8d; font-style: italic;">
                    🎯 继续努力，积累更多战斗经验！
                </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
