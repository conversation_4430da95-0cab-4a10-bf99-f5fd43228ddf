# 🎮 拖拽排表联动功能说明

## 📋 功能概述

拖拽排表保存时，**会自动联动更新成员的相关信息**，实现数据的双向同步。

## ✅ **联动更新的字段**

当您在拖拽排表中移动成员位置时，系统会自动更新以下字段：

### 🏗️ **三层级组织字段**
- **main_group**: 主要团队 (进攻团/防守团/其他团)
- **sub_team**: 子团队 (一团/二团/三团/防守团)
- **squad**: 小队编号 (1队/2队/3队/4队/5队)

### 🔄 **兼容性字段**
- **team**: 兼容旧系统的团队字段 (1团/2团/3团/4团/其他团)

## ❌ **不会自动更新的字段**

根据您的要求，以下字段**不会**被自动更新：

- **position**: 职能位置 (治疗/坦克/输出/拆塔/辅助/控制)
- **profession**: 职业 (素问/铁衣/血河等)
- **status**: 状态 (主力/替补/请假)

> **原因**: 小队编号(1队/2队/3队/4队/5队)与职能位置(治疗/坦克/输出等)没有任何关系，是纯粹的团队结构划分。

## 🎯 **联动示例**

### 示例1：跨主团移动
```
操作: 把"风悔"从进攻团一团1队 拖到 防守团防守团3队

联动更新:
✅ main_group: 进攻团 → 防守团
✅ sub_team: 一团 → 防守团  
✅ squad: 1队 → 3队
✅ team: 1团 → 4团
❌ position: 输出 (保持不变)
```

### 示例2：同团内换小队
```
操作: 把"风悔"从防守团防守团3队 拖到 防守团防守团5队

联动更新:
✅ squad: 3队 → 5队
❌ main_group: 防守团 (无变化)
❌ sub_team: 防守团 (无变化)
❌ team: 4团 (无变化)
❌ position: 输出 (保持不变)
```

## 🔍 **数据验证**

系统会记录所有变更并提供详细的变更日志：

```json
{
  "success": true,
  "message": "位置更新成功，联动更新了: main_group, sub_team, squad, team",
  "updated_fields": ["main_group", "sub_team", "squad", "team"],
  "changes": [
    "main_group: 进攻团 → 防守团",
    "sub_team: 一团 → 防守团", 
    "squad: 1队 → 3队",
    "team: 1团 → 4团"
  ]
}
```

## 🎨 **用户体验**

### 📱 **实时反馈**
- 拖拽完成后立即保存到数据库
- 显示成功通知提示
- 控制台输出详细的变更日志

### 🎯 **数据一致性**
- 三层级字段与兼容字段保持同步
- 历史战斗分析数据不受影响
- 成员管理页面数据实时更新

## 🔧 **技术实现**

### 后端联动逻辑
```python
# 更新三层级字段
member['main_group'] = new_main_group
member['sub_team'] = new_sub_team  
member['squad'] = new_squad

# 自动更新兼容字段
if new_main_group == '进攻团':
    if new_sub_team == '一团':
        member['team'] = '1团'
    elif new_sub_team == '二团':
        member['team'] = '2团'
    # ...
elif new_main_group == '防守团':
    member['team'] = '4团'
```

### 前端实时通知
```javascript
// 显示更新成功提示
showUpdateNotification(memberName, '位置已更新');

// 记录详细变更
console.log('联动更新字段:', updatedFields);
console.log('详细变更:', changes);
```

## 🎉 **总结**

✅ **完整联动**: 拖拽排表会自动更新所有相关的组织结构字段  
✅ **数据一致性**: 新旧字段保持同步，确保系统兼容性  
✅ **职能独立**: position字段独立管理，不受小队编号影响  
✅ **实时反馈**: 立即保存并显示更新结果  
✅ **历史保护**: 不影响现有的战斗分析数据  

现在您可以放心使用拖拽排表功能，系统会自动处理所有相关数据的联动更新！🚀
