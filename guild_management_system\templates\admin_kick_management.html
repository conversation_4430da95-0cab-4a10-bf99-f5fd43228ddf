{% extends "base.html" %}

{% block title %}踢出管理{% endblock %}

{% block content %}
<style>
    .kick-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .kick-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .kick-header h1 {
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .actions-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        font-size: 14px;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
    }
    
    .btn-danger {
        background: #e74c3c;
        color: white;
    }
    
    .btn-danger:hover {
        background: #c0392b;
    }
    
    .kick-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #e74c3c;
        transition: all 0.3s ease;
    }
    
    .kick-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .kick-header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .member-name {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .countdown {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        background: #fadbd8;
        color: #e74c3c;
    }
    
    .kick-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .info-item {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
    }
    
    .info-label {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 5px;
    }
    
    .info-value {
        font-size: 14px;
        font-weight: 500;
        color: #2c3e50;
    }
    
    .manual-kick-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #f39c12;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #2c3e50;
    }
    
    .form-group input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 20px;
        display: block;
    }
    
    .status-pending {
        color: #f39c12;
    }
    
    .status-bound {
        color: #27ae60;
    }
    
    .status-unbound {
        color: #95a5a6;
    }
</style>

<div class="kick-container">
    <div class="kick-header">
        <h1>🚪 踢出管理</h1>
        <p style="color: #6c757d;">管理帮外成员的自动踢出和手动踢出</p>
    </div>
    
    <!-- 手动踢出区域 -->
    <div class="manual-kick-section">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50;">
            <i class="fas fa-user-times"></i> 手动踢出成员
        </h3>
        
        <form method="POST" action="{{ url_for('admin_manual_kick_member') }}">
            <div style="display: grid; grid-template-columns: 1fr 1fr 100px; gap: 15px; align-items: end;">
                <div class="form-group" style="margin: 0;">
                    <label for="member_name">成员名称</label>
                    <input type="text" id="member_name" name="member_name" placeholder="输入要踢出的成员名称" required>
                </div>
                
                <div class="form-group" style="margin: 0;">
                    <label for="reason">踢出原因</label>
                    <input type="text" id="reason" name="reason" placeholder="踢出原因（可选）" value="管理员直接踢出">
                </div>
                
                <button type="submit" class="btn btn-danger" onclick="return confirm('确定要踢出该成员吗？此操作不可撤销！')">
                    <i class="fas fa-user-times"></i> 踢出
                </button>
            </div>
        </form>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 15px; font-size: 14px;">
            <strong>⚠️ 注意：</strong>手动踢出会立即删除成员记录，无法撤销。建议先将成员移至帮外，让系统自动处理。
        </div>
    </div>
    
    <!-- 待处理踢出列表 -->
    <div class="actions-bar">
        <div>
            <span style="color: #6c757d;">待踢出成员：{{ pending_kicks|length }} 个</span>
        </div>
        <div>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                返回首页
            </a>
        </div>
    </div>
    
    {% if pending_kicks %}
        {% for kick in pending_kicks %}
        <div class="kick-card">
            <div class="kick-header-info">
                <h3 class="member-name">
                    {{ kick.member_name }}
                    {% if kick.bound_username %}
                        <small style="color: #27ae60;">(绑定用户: {{ kick.bound_username }})</small>
                    {% else %}
                        <small style="color: #95a5a6;">(未绑定账户)</small>
                    {% endif %}
                </h3>
                <span class="countdown">
                    {% set now = moment() %}
                    {% set deadline = moment(kick.kick_deadline) %}
                    {% set remaining = deadline.diff(now, 'hours') %}
                    {% if remaining > 0 %}
                        {{ remaining }}小时后踢出
                    {% else %}
                        即将踢出
                    {% endif %}
                </span>
            </div>
            
            <div class="kick-info">
                <div class="info-item">
                    <div class="info-label">移至帮外时间</div>
                    <div class="info-value">
                        {{ kick.moved_to_outside_time.strftime('%Y-%m-%d %H:%M:%S') if kick.moved_to_outside_time else '未知' }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">踢出截止时间</div>
                    <div class="info-value">
                        {{ kick.kick_deadline.strftime('%Y-%m-%d %H:%M:%S') if kick.kick_deadline else '未知' }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">操作人</div>
                    <div class="info-value">{{ kick.kicked_by or '未知' }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">绑定状态</div>
                    <div class="info-value">
                        {% if kick.user_id %}
                            <span class="status-bound">✅ 已绑定账户</span>
                        {% else %}
                            <span class="status-unbound">❌ 未绑定账户</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            {% if kick.reason %}
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-style: italic; color: #6c757d; margin-top: 10px;">
                📝 {{ kick.reason }}
            </div>
            {% endif %}
            
            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee; font-size: 14px; color: #6c757d;">
                {% if kick.user_id %}
                    <strong>⏰ 自动踢出：</strong>该成员已绑定账户，到期后将自动踢出帮会
                {% else %}
                    <strong>⏭️ 自动跳过：</strong>该成员未绑定账户，到期后将自动取消踢出
                {% endif %}
            </div>
        </div>
        {% endfor %}
        
    {% else %}
        <div class="empty-state">
            <i class="fas fa-users"></i>
            <h3>暂无待踢出成员</h3>
            <p>当前没有成员在踢出倒计时中</p>
            <p style="font-size: 14px; color: #95a5a6;">将成员拖动到"帮外"区域即可开始3天倒计时</p>
        </div>
    {% endif %}
</div>

<script>
// 添加moment.js支持（如果需要）
function moment(date) {
    if (!date) return new Date();
    return new Date(date);
}
</script>
{% endblock %}
