{% extends "base.html" %}

{% block title %}申请管理 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<style>
    .admin-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .admin-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .admin-header h2 {
        color: #2c3e50;
        font-size: 2em;
        margin-bottom: 10px;
    }

    .admin-header p {
        color: #7f8c8d;
        font-size: 1.1em;
    }

    .applications-list {
        margin-top: 20px;
    }

    .application-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 5px solid #667eea;
    }

    .application-item.create-guild {
        border-left-color: #28a745;
    }

    .application-item.join-guild {
        border-left-color: #17a2b8;
    }

    .application-item.pending {
        background: #fff3cd;
        border-left-color: #ffc107;
    }

    .application-item.approved {
        background: #d4edda;
        border-left-color: #28a745;
    }

    .application-item.rejected {
        background: #f8d7da;
        border-left-color: #dc3545;
    }

    .application-header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .application-type {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        color: white;
    }

    .application-type.create-guild {
        background: #28a745;
    }

    .application-type.join-guild {
        background: #17a2b8;
    }

    .application-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        color: white;
    }

    .application-status.pending {
        background: #ffc107;
        color: #212529;
    }

    .application-status.approved {
        background: #28a745;
    }

    .application-status.rejected {
        background: #dc3545;
    }

    .application-details {
        margin-bottom: 15px;
    }

    .application-details h4 {
        margin: 0 0 10px 0;
        color: #2c3e50;
    }

    .application-details p {
        margin: 5px 0;
        color: #666;
    }

    .application-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .action-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .approve-btn {
        background: #28a745;
        color: white;
    }

    .approve-btn:hover {
        background: #218838;
    }

    .reject-btn {
        background: #dc3545;
        color: white;
    }

    .reject-btn:hover {
        background: #c82333;
    }

    .no-applications {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
    }

    .no-applications i {
        font-size: 3em;
        margin-bottom: 20px;
        display: block;
    }

    .flash-messages {
        margin-bottom: 20px;
    }

    .flash-message {
        padding: 12px 20px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .flash-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .flash-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .flash-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
</style>

<!-- Flash消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="flash-messages">
            {% for category, message in messages %}
                <div class="flash-message flash-{{ category }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="admin-container">
    <div class="admin-header">
        <h2>📋 申请管理</h2>
        <p>
            {% if is_super_admin() %}
                管理所有帮会申请
            {% else %}
                管理 {{ get_current_user().guild_id }} 帮会的加入申请
            {% endif %}
        </p>
    </div>

    <div class="applications-list">
        {% if applications %}
            {% for app in applications %}
            <div class="application-item {{ app.application_type }} {{ app.status }}">
                <div class="application-header-info">
                    <div>
                        <span class="application-type {{ app.application_type }}">
                            {% if app.application_type == 'create_guild' %}
                                🆕 创建帮会
                            {% else %}
                                🤝 加入帮会
                            {% endif %}
                        </span>
                        <span class="application-status {{ app.status }}">
                            {% if app.status == 'pending' %}
                                ⏳ 待审批
                            {% elif app.status == 'approved' %}
                                ✅ 已通过
                            {% else %}
                                ❌ 已拒绝
                            {% endif %}
                        </span>
                    </div>
                    <div style="color: #666; font-size: 14px;">
                        {{ app.created_time.strftime('%Y-%m-%d %H:%M') if app.created_time else '未知时间' }}
                    </div>
                </div>

                <div class="application-details">
                    <h4>申请人: {{ app.username }}</h4>
                    {% if app.application_type == 'create_guild' %}
                        <p><strong>帮会名称:</strong> {{ app.guild_name }}</p>
                        <p><strong>帮会ID:</strong> {{ app.guild_id }}</p>
                    {% else %}
                        <p><strong>目标帮会:</strong> {{ app.guild_id }}</p>
                    {% endif %}
                    {% if app.description %}
                        <p><strong>申请说明:</strong> {{ app.description }}</p>
                    {% endif %}
                    {% if app.status != 'pending' %}
                        <p><strong>处理人:</strong> {{ app.processed_by or '未知' }}</p>
                        <p><strong>处理时间:</strong> {{ app.processed_time.strftime('%Y-%m-%d %H:%M') if app.processed_time else '未知' }}</p>
                    {% endif %}
                </div>

                {% if app.status == 'pending' %}
                <div class="application-actions">
                    <button class="action-btn approve-btn" onclick="processApplication({{ app.id }}, 'approve')">
                        ✅ 通过
                    </button>
                    <button class="action-btn reject-btn" onclick="processApplication({{ app.id }}, 'reject')">
                        ❌ 拒绝
                    </button>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-applications">
                <i>📭</i>
                <h3>暂无申请</h3>
                <p>当前没有待处理的申请</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    function processApplication(appId, action) {
        if (!confirm(`确定要${action === 'approve' ? '通过' : '拒绝'}这个申请吗？`)) {
            return;
        }

        const formData = new FormData();
        formData.append('app_id', appId);
        formData.append('action', action);

        fetch('/admin/process_application', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('操作失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请稍后重试');
        });
    }
</script>
{% endblock %}
