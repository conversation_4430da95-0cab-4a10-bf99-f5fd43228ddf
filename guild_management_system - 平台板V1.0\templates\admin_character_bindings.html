{% extends "base.html" %}

{% block title %}角色绑定审核 - 逆水寒帮会辅助管理系统{% endblock %}

{% block content %}
<style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .admin-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .pending-section {
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .binding-item {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        color: #2c3e50;
    }
    
    .binding-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .character-info {
        flex: 1;
    }
    
    .character-name {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .user-info {
        font-size: 14px;
        color: #6c757d;
    }
    
    .verification-section {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        text-align: center;
    }
    
    .verification-code {
        font-size: 24px;
        font-weight: bold;
        color: #1976d2;
        background: white;
        padding: 10px 20px;
        border-radius: 5px;
        display: inline-block;
        letter-spacing: 2px;
        margin: 10px 0;
    }
    
    .action-buttons {
        display: flex;
        gap: 10px;
    }
    
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
    }
    
    .btn-approve {
        background: #28a745;
        color: white;
    }
    
    .btn-approve:hover {
        background: #218838;
        transform: translateY(-1px);
    }
    
    .btn-reject {
        background: #dc3545;
        color: white;
    }
    
    .btn-reject:hover {
        background: #c82333;
        transform: translateY(-1px);
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: #ffc107;
        color: #856404;
    }
    
    .status-approved {
        background: #28a745;
        color: white;
    }
    
    .status-rejected {
        background: #dc3545;
        color: white;
    }
    
    .history-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    
    .history-table th,
    .history-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .history-table th {
        background: #f8f9fa;
        font-weight: bold;
        color: #495057;
    }
    
    .history-table tr:hover {
        background: #f8f9fa;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }
</style>

<div class="admin-container">
    <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
        <i class="fas fa-user-check"></i> 角色绑定审核管理
    </h2>
    
    <!-- 待审核的绑定 -->
    {% if pending_bindings %}
    <div class="pending-section">
        <h3 style="margin: 0 0 20px 0; font-size: 24px;">
            <i class="fas fa-clock"></i> 待审核申请 ({{ pending_bindings|length }})
        </h3>
        
        {% for binding in pending_bindings %}
        <div class="binding-item">
            <div class="binding-header">
                <div class="character-info">
                    <div class="character-name">{{ binding.character_name }}</div>
                    <div class="user-info">
                        申请用户: {{ binding.username }} | 
                        申请时间: {{ binding.created_at.strftime('%Y-%m-%d %H:%M') if binding.created_at else '未知' }}
                    </div>
                </div>
                <div class="action-buttons">
                    <a href="{{ url_for('approve_binding', binding_id=binding.id) }}" 
                       class="btn btn-approve"
                       onclick="return confirm('确认通过该角色绑定申请？')">
                        <i class="fas fa-check"></i> 通过
                    </a>
                    <a href="{{ url_for('reject_binding', binding_id=binding.id) }}" 
                       class="btn btn-reject"
                       onclick="return confirm('确认拒绝该角色绑定申请？')">
                        <i class="fas fa-times"></i> 拒绝
                    </a>
                </div>
            </div>
            
            {% if binding.verification_code %}
            <div class="verification-section">
                <div style="font-size: 14px; margin-bottom: 8px; color: #1976d2; font-weight: bold;">
                    <i class="fas fa-key"></i> 验证码
                </div>
                <div class="verification-code">{{ binding.verification_code }}</div>
                <div style="font-size: 12px; color: #666; margin-top: 8px;">
                    请核实用户是否能提供此验证码
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="admin-card">
        <div class="empty-state">
            <i class="fas fa-check-circle"></i>
            <h4>暂无待审核的角色绑定申请</h4>
            <p>所有申请都已处理完毕</p>
        </div>
    </div>
    {% endif %}
    
    <!-- 所有绑定记录 -->
    <div class="admin-card">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50;">
            <i class="fas fa-list"></i> 所有绑定记录
        </h3>
        
        {% if all_bindings %}
        <table class="history-table">
            <thead>
                <tr>
                    <th>角色名称</th>
                    <th>申请用户</th>
                    <th>状态</th>
                    <th>申请时间</th>
                    <th>处理时间</th>
                    <th>处理人</th>
                </tr>
            </thead>
            <tbody>
                {% for binding in all_bindings %}
                <tr>
                    <td>
                        <strong>{{ binding.character_name }}</strong>
                        {% if binding.verification_code and binding.status == 'pending' %}
                        <br><small style="color: #1976d2;">验证码: {{ binding.verification_code }}</small>
                        {% endif %}
                    </td>
                    <td>{{ binding.username }}</td>
                    <td>
                        <span class="status-badge status-{{ binding.status }}">
                            {% if binding.status == 'pending' %}
                                ⏳ 待审核
                            {% elif binding.status == 'approved' %}
                                ✅ 已通过
                            {% elif binding.status == 'rejected' %}
                                ❌ 已拒绝
                            {% endif %}
                        </span>
                    </td>
                    <td>{{ binding.created_at.strftime('%Y-%m-%d %H:%M') if binding.created_at else '未知' }}</td>
                    <td>
                        {% if binding.approved_at %}
                            {{ binding.approved_at.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>{{ binding.approved_by or '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-database"></i>
            <h4>暂无绑定记录</h4>
            <p>还没有用户申请角色绑定</p>
        </div>
        {% endif %}
    </div>
    
    <!-- 操作说明 -->
    <div class="admin-card">
        <h3 style="margin: 0 0 15px 0; color: #2c3e50;">
            <i class="fas fa-info-circle"></i> 审核说明
        </h3>
        <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; font-size: 14px; line-height: 1.6;">
            <p style="margin: 0 0 10px 0;"><strong>审核流程：</strong></p>
            <ol style="margin: 0; padding-left: 20px;">
                <li>用户提交角色绑定申请后，系统会生成验证码</li>
                <li>请联系申请用户，要求其提供验证码进行身份验证</li>
                <li>核实验证码正确后，点击"通过"按钮审核通过</li>
                <li>如发现异常或验证码不符，可点击"拒绝"按钮拒绝申请</li>
                <li>绑定通过后，用户即可查看个人战斗数据分析</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %}
