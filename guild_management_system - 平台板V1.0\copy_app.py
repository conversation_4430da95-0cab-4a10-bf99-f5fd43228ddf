#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复制app.py为member_readonly.py的脚本
"""

def copy_app_to_readonly():
    """复制app.py到member_readonly.py并修改配置"""

    try:
        # 读取app.py内容
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"✅ 读取app.py成功，文件大小: {len(content)} 字符")

        # 修改标题
        content = content.replace(
            '纸落云烟帮会管理系统 - Web应用',
            '纸落云烟帮会管理系统 - 成员只读版本'
        )

        # 修改端口
        content = content.replace('port=5888', 'port=5000')

        # 添加只读模式配置
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'app = Flask(__name__)' in line:
                lines.insert(i+1, '')
                lines.insert(i+2, '# 配置只读模式')
                lines.insert(i+3, 'app.config["READONLY_MODE"] = True')
                lines.insert(i+4, 'app.jinja_env.globals["readonly_mode"] = True')
                break

        content = '\n'.join(lines)

        # 写入member_readonly.py
        with open('member_readonly.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print('✅ 文件复制完成: app.py -> member_readonly.py')
        print('✅ 已添加只读模式配置')
        print('✅ 已修改端口为5000')

        # 验证文件
        with open('member_readonly.py', 'r', encoding='utf-8') as f:
            new_content = f.read()
        print(f"✅ 验证成功，新文件大小: {len(new_content)} 字符")

    except Exception as e:
        print(f"❌ 复制失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    copy_app_to_readonly()
