#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import traceback

print("Starting debug script...")
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")

try:
    print("Importing Flask...")
    from flask import Flask
    print("✅ Flask imported successfully")
    
    print("Importing app module...")
    import app
    print("✅ app module imported successfully")
    
    print("Starting Flask app...")
    if hasattr(app, 'app'):
        print("Found Flask app instance")
        app.app.run(host='0.0.0.0', port=5888, debug=True)
    else:
        print("❌ No Flask app instance found")
        
except Exception as e:
    print(f"❌ Error: {e}")
    print("Full traceback:")
    traceback.print_exc()
