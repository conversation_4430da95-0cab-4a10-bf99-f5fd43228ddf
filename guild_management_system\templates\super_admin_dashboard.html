{% extends "base.html" %}

{% block title %}超级管理员 - 逆水寒帮会辅助管理系统{% endblock %}

{% block content %}
<style>
    .admin-dashboard {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .dashboard-header h1 {
        color: #2c3e50;
        font-size: 2.5em;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .dashboard-header p {
        color: #7f8c8d;
        font-size: 1.2em;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5em;
        font-weight: bold;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1em;
        opacity: 0.9;
    }

    .guilds-section {
        margin-top: 40px;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.8em;
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 3px solid #667eea;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .guilds-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;
    }

    .guild-card {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .guild-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .guild-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .guild-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .guild-name {
        font-size: 1.4em;
        font-weight: bold;
        color: #2c3e50;
        margin: 0;
    }

    .guild-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        background: #28a745;
    }

    .guild-status.inactive {
        background: #dc3545;
    }

    .guild-info {
        margin-bottom: 15px;
    }

    .guild-info p {
        margin: 5px 0;
        color: #666;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .guild-info strong {
        color: #2c3e50;
    }

    .guild-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .action-btn {
        flex: 1;
        padding: 8px 12px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        display: inline-block;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-primary:hover {
        background: #5a6fd8;
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
    }

    .btn-warning {
        background: #ffc107;
        color: #212529;
    }

    .btn-warning:hover {
        background: #e0a800;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-top: 5px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .no-guilds {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }

    .no-guilds i {
        font-size: 4em;
        margin-bottom: 20px;
        display: block;
    }

    .flash-messages {
        margin-bottom: 20px;
    }

    .flash-message {
        padding: 12px 20px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .flash-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .flash-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .flash-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
</style>

<!-- Flash消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="flash-messages">
            {% for category, message in messages %}
                <div class="flash-message flash-{{ category }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="admin-dashboard">
    <div class="dashboard-header">
        <h1>🏛️ 逆水寒帮会辅助管理系统</h1>
        <p>超级管理员控制中心 - 多帮会管理平台</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>{{ total_guilds }}</h3>
            <p>📊 总帮会数</p>
        </div>
        <div class="stat-card">
            <h3>{{ total_users }}</h3>
            <p>👥 总用户数</p>
        </div>
        <div class="stat-card">
            <h3>{{ pending_applications }}</h3>
            <p>⏳ 待审批申请</p>
        </div>
    </div>

    <!-- 帮会列表 -->
    <div class="guilds-section">
        <h2 class="section-title">
            🏰 帮会管理
        </h2>

        {% if guilds %}
            <div class="guilds-grid">
                {% for guild in guilds %}
                <div class="guild-card">
                    <div class="guild-header">
                        <h3 class="guild-name">{{ guild.name }}</h3>
                        <span class="guild-status {{ 'active' if guild.status == 'active' else 'inactive' }}">
                            {{ '活跃' if guild.status == 'active' else '停用' }}
                        </span>
                    </div>

                    <div class="guild-info">
                        <p>
                            <strong>帮会ID:</strong>
                            <span>{{ guild.id }}</span>
                        </p>
                        <p>
                            <strong>大当家:</strong>
                            <span>{{ guild.leader }}</span>
                        </p>
                        <p>
                            <strong>成员数量:</strong>
                            <span>{{ guild.members_count }}/{{ guild.max_members }}</span>
                        </p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ (guild.members_count / guild.max_members * 100) if guild.max_members > 0 else 0 }}%"></div>
                        </div>
                        <p>
                            <strong>待审申请:</strong>
                            <span>{{ guild.pending_applications }} 个</span>
                        </p>
                        <p>
                            <strong>创建时间:</strong>
                            <span>{{ guild.created_time[:10] if guild.created_time else '未知' }}</span>
                        </p>
                    </div>

                    <div class="guild-actions">
                        <a href="/admin/guild/{{ guild.id }}" class="action-btn btn-primary">
                            📊 管理
                        </a>
                        <a href="/admin/guild/{{ guild.id }}/members" class="action-btn btn-secondary">
                            👥 成员
                        </a>
                        {% if guild.pending_applications > 0 %}
                        <a href="/admin/applications?guild={{ guild.id }}" class="action-btn btn-warning">
                            ⏳ 申请
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-guilds">
                <i>🏰</i>
                <h3>暂无帮会</h3>
                <p>系统中还没有任何帮会，等待用户申请创建</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
