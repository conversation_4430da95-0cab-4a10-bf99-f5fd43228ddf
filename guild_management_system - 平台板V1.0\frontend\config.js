// 前端配置文件
const CONFIG = {
    // API服务器地址
    API_BASE_URL: 'http://localhost:5002',
    
    // 应用配置
    APP_NAME: '纸落云烟公会管理系统',
    VERSION: '1.0.0',
    
    // 认证配置
    AUTH: {
        TOKEN_KEY: 'guild_token',
        USER_KEY: 'guild_user'
    },
    
    // API端点
    ENDPOINTS: {
        // 认证相关
        LOGIN: '/api/auth/login',
        AUTH_CONFIG: '/api/auth/config',
        
        // 成员管理
        MEMBERS: '/api/members',
        MEMBER_BY_NAME: '/api/members/{name}',
        
        // 统计数据
        OVERVIEW_STATS: '/api/stats/overview'
    }
};

// 工具函数
const API = {
    // 构建完整的API URL
    url: (endpoint) => {
        return CONFIG.API_BASE_URL + endpoint;
    },
    
    // 发送API请求
    request: async (endpoint, options = {}) => {
        const url = API.url(endpoint);
        const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };
        
        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            return { success: false, error: error.message };
        }
    },
    
    // GET请求
    get: (endpoint) => API.request(endpoint),
    
    // POST请求
    post: (endpoint, data) => API.request(endpoint, {
        method: 'POST',
        body: JSON.stringify(data)
    }),
    
    // PUT请求
    put: (endpoint, data) => API.request(endpoint, {
        method: 'PUT',
        body: JSON.stringify(data)
    }),
    
    // DELETE请求
    delete: (endpoint) => API.request(endpoint, {
        method: 'DELETE'
    })
};

// 认证工具
const Auth = {
    // 保存登录信息
    saveLogin: (token, user) => {
        localStorage.setItem(CONFIG.AUTH.TOKEN_KEY, token);
        localStorage.setItem(CONFIG.AUTH.USER_KEY, JSON.stringify(user));
    },
    
    // 获取当前用户
    getCurrentUser: () => {
        const userStr = localStorage.getItem(CONFIG.AUTH.USER_KEY);
        return userStr ? JSON.parse(userStr) : null;
    },
    
    // 获取token
    getToken: () => {
        return localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
    },
    
    // 检查是否已登录
    isLoggedIn: () => {
        return !!Auth.getToken();
    },
    
    // 退出登录
    logout: () => {
        localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
        localStorage.removeItem(CONFIG.AUTH.USER_KEY);
    }
};
