#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽排表联动功能
"""

import requests
import json
import time

def test_position_linkage():
    """测试位置联动功能"""
    print("🧪 测试拖拽排表联动功能...")
    
    # 测试用例：不同职业在不同小队的联动效果
    test_cases = [
        {
            'name': '有姝丶',
            'profession': '素问',
            'target': {'main_group': '进攻团', 'sub_team': '一团', 'squad': '1队'},
            'expected_position': '治疗'
        },
        {
            'name': '有姝丶', 
            'profession': '素问',
            'target': {'main_group': '进攻团', 'sub_team': '一团', 'squad': '2队'},
            'expected_position': '治疗'  # 素问即使在输出队也应该是治疗
        },
        {
            'name': '有姝丶',
            'profession': '素问', 
            'target': {'main_group': '防守团', 'sub_team': '防守团', 'squad': '4队'},
            'expected_position': '治疗'  # 素问即使在坦克队也应该是治疗
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n📋 测试用例 {i+1}: {case['name']} ({case['profession']}) -> {case['target']['squad']}")
        
        try:
            # 发送拖拽更新请求
            response = requests.post(
                'http://localhost:5000/update_member_position',
                json={
                    'member_name': case['name'],
                    'main_group': case['target']['main_group'],
                    'sub_team': case['target']['sub_team'],
                    'squad': case['target']['squad']
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 更新成功")
                    print(f"   联动更新字段: {result.get('updated_fields', [])}")
                    
                    if result.get('changes'):
                        print("   详细变更:")
                        for change in result['changes']:
                            print(f"     - {change}")
                    
                    # 验证position是否正确更新
                    if 'position' in result.get('updated_fields', []):
                        print(f"   ✅ position字段已联动更新")
                    else:
                        print(f"   ⚠️ position字段未更新")
                        
                else:
                    print(f"❌ 更新失败: {result.get('message')}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
        
        time.sleep(1)  # 避免请求过快

def test_different_professions():
    """测试不同职业的智能position推断"""
    print("\n🎯 测试不同职业的智能position推断...")
    
    # 读取当前成员数据
    try:
        with open('data/guild_members.json', 'r', encoding='utf-8') as f:
            members = json.load(f)
    except:
        print("❌ 无法读取成员数据")
        return
    
    # 选择不同职业的成员进行测试
    profession_tests = {}
    for member in members:
        prof = member['profession']
        if prof not in profession_tests and len(profession_tests) < 5:
            profession_tests[prof] = member['name']
    
    print(f"📊 将测试以下职业: {list(profession_tests.keys())}")
    
    for profession, member_name in profession_tests.items():
        print(f"\n🔍 测试 {profession} ({member_name}):")
        
        # 测试不同小队的联动效果
        for squad in ['1队', '2队', '3队', '4队', '5队']:
            try:
                response = requests.post(
                    'http://localhost:5000/update_member_position',
                    json={
                        'member_name': member_name,
                        'main_group': '进攻团',
                        'sub_team': '一团',
                        'squad': squad
                    },
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') and 'position' in result.get('updated_fields', []):
                        # 从changes中提取position变更
                        position_change = None
                        for change in result.get('changes', []):
                            if 'position:' in change:
                                position_change = change.split('→')[-1].strip()
                                break
                        
                        print(f"   {squad}: position = {position_change}")
                    else:
                        print(f"   {squad}: 无position更新")
                        
            except Exception as e:
                print(f"   {squad}: 测试失败 - {e}")
            
            time.sleep(0.5)

def verify_data_integrity():
    """验证数据完整性"""
    print("\n🔍 验证数据完整性...")
    
    try:
        with open('data/guild_members.json', 'r', encoding='utf-8') as f:
            members = json.load(f)
        
        print(f"📊 总成员数: {len(members)}")
        
        # 检查必要字段
        required_fields = ['name', 'profession', 'main_group', 'sub_team', 'squad', 'team', 'position']
        missing_fields = []
        
        for member in members:
            for field in required_fields:
                if field not in member:
                    missing_fields.append(f"{member['name']}: {field}")
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields[:5]}...")  # 只显示前5个
        else:
            print("✅ 所有成员都有必要字段")
        
        # 统计position分布
        position_stats = {}
        for member in members:
            pos = member.get('position', '未知')
            position_stats[pos] = position_stats.get(pos, 0) + 1
        
        print("📊 position分布:")
        for pos, count in sorted(position_stats.items()):
            print(f"   {pos}: {count}人")
            
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试拖拽排表联动功能...")
    print("=" * 60)
    
    # 等待服务器
    print("⏳ 等待服务器响应...")
    time.sleep(2)
    
    # 运行测试
    verify_data_integrity()
    test_position_linkage()
    test_different_professions()
    
    print("=" * 60)
    print("🎉 联动功能测试完成！")

if __name__ == '__main__':
    main()
