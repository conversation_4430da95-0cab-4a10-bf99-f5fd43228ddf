<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ member.name }} - 成员详情 - 逆水寒帮会辅助管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .profession-badge {
            padding: 6px 12px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            display: inline-block;
        }
        .profession-素问 { background-color: #ff6b9d; }
        .profession-九灵 { background-color: #8e44ad; }
        .profession-潮光 { background-color: #3498db; }
        .profession-血河 { background-color: #e74c3c; }
        .profession-神相 { background-color: #2980b9; }
        .profession-玄机 { background-color: #f39c12; }
        .profession-铁衣 { background-color: #e67e22; }
        .profession-龙吟 { background-color: #27ae60; }
        .profession-碎梦 { background-color: #16a085; }
        .profession-沧澜 { background-color: #6c5ce7; }
    </style>
</head>
<body>
<div class="container">

<!-- 游客注册引导横幅 -->
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 25px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="font-size: 48px;">🎮</div>
        <div style="flex: 1;">
            <h3 style="margin: 0 0 8px 0; font-size: 22px;">想要查看完整的战斗数据分析？</h3>
            <p style="margin: 0 0 15px 0; font-size: 16px; opacity: 0.9;">注册账号即可解锁详细的战斗表现、能力雷达图、成长趋势等强大功能</p>
            <div style="display: flex; gap: 15px;">
                <a href="{{ url_for('register') }}" style="background: rgba(255, 255, 255, 0.2); border: 2px solid rgba(255, 255, 255, 0.3); color: white; padding: 10px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 8px;">
                    <i class="fas fa-user-plus"></i> 立即注册
                </a>
                <a href="{{ url_for('login') }}" style="background: transparent; border: 2px solid rgba(255, 255, 255, 0.3); color: white; padding: 10px 25px; border-radius: 25px; text-decoration: none; font-weight: bold; transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 8px;">
                    <i class="fas fa-sign-in-alt"></i> 已有账号
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 返回按钮 -->
<div style="margin-bottom: 20px;">
    <button onclick="history.back()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
        ← 返回搜索
    </button>
</div>

<!-- 成员基本信息卡片 -->
<div style="position: relative; margin-bottom: 30px; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.15);">
    <!-- 背景渐变 -->
    <div style="
        background: linear-gradient(135deg,
            {% if member.profession == '素问' %}#ff6b9d, #c44569{% elif member.profession == '九灵' %}#8e44ad, #9b59b6{% elif member.profession == '潮光' %}#3498db, #2980b9{% elif member.profession == '血河' %}#e74c3c, #c0392b{% elif member.profession == '神相' %}#2980b9, #3498db{% elif member.profession == '玄机' %}#f39c12, #e67e22{% elif member.profession == '铁衣' %}#e67e22, #d35400{% elif member.profession == '龙吟' %}#27ae60, #2ecc71{% elif member.profession == '碎梦' %}#16a085, #1abc9c{% else %}#6c5ce7, #a29bfe{% endif %}
        );
        padding: 30px;
        color: white;
        position: relative;
    ">
        <!-- 装饰性图案 -->
        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.2;"></div>

        <!-- 头部标题 -->
        <div style="position: relative; z-index: 2;">
            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 25px;">
                <div style="
                    background: rgba(255,255,255,0.2);
                    padding: 12px 20px;
                    border-radius: 25px;
                    font-size: 16px;
                    font-weight: bold;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.3);
                ">
                    {{ member.profession }}
                </div>
                <div style="flex: 1;">
                    <h1 style="margin: 0; font-size: 28px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                        {{ member.name }}
                    </h1>
                    <div style="font-size: 16px; opacity: 0.9; margin-top: 5px;">
                        {{ member.guild_name or '逆水寒' }}帮会成员档案
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细信息区域 -->
    <div style="background: white; padding: 30px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
            <div>
                <h4 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 18px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📋 基本信息
                </h4>
                <div style="font-size: 15px; line-height: 2; background: #f8f9fa; padding: 20px; border-radius: 12px;">
                    {% if member.guild_name %}
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">所属帮会：</strong>
                        <span class="profession-badge" style="background: #667eea; color: white; font-size: 13px;">{{ member.guild_name }}</span>
                    </div>
                    {% endif %}
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">职业：</strong>
                        <span class="profession-badge profession-{{ member.profession }}" style="font-size: 13px;">{{ member.profession }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">主团：</strong>
                        <span class="profession-badge" style="background: {% if member.get('main_group') == '进攻团' %}#28a745{% elif member.get('main_group') == '防守团' %}#dc3545{% else %}#6c757d{% endif %}; color: white; font-size: 13px;">{{ member.get('main_group', '其他团') }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">子团：</strong>
                        <span class="profession-badge" style="background: #667eea; color: white; font-size: 13px;">{{ member.get('sub_team', '一团') }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">小队：</strong>
                        <span class="profession-badge" style="background: #17a2b8; color: white; font-size: 13px;">{{ member.get('squad', '1队') }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <strong style="color: #495057;">位置：</strong>
                        <span class="profession-badge" style="background: #6c757d; color: white; font-size: 13px;">{{ member.position }}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <strong style="color: #495057;">状态：</strong>
                        <span class="profession-badge" style="background: {% if member.status == '主力' %}#007bff{% elif member.status == '替补' %}#ffc107{% else %}#6c757d{% endif %}; color: white; font-size: 13px;">{{ member.status or '主力' }}</span>
                    </div>
                </div>
            </div>
        
            {% if battle_history %}
            <div>
                <h4 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 18px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📊 战斗数据概览
                </h4>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 12px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #667eea;">{{ battle_history|length }}</div>
                            <div style="font-size: 12px; color: #6c757d;">参战次数</div>
                        </div>
                        {% if battle_history %}
                        {% set avg_score = (battle_history|map(attribute='score')|sum / battle_history|length) %}
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: {% if avg_score >= 70 %}#28a745{% elif avg_score >= 50 %}#ffc107{% else %}#dc3545{% endif %};">{{ "%.1f"|format(avg_score) }}</div>
                            <div style="font-size: 12px; color: #6c757d;">平均评分</div>
                        </div>
                        {% set max_score = battle_history|map(attribute='score')|max %}
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 24px; font-weight: bold; color: #f39c12;">{{ "%.1f"|format(max_score) }}</div>
                            <div style="font-size: 12px; color: #6c757d;">最高评分</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% else %}
            <div>
                <h4 style="margin: 0 0 20px 0; color: #2c3e50; font-size: 18px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                    📊 战斗数据概览
                </h4>
                <div style="background: #f8f9fa; padding: 30px; border-radius: 12px; text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;">📊</div>
                    <h5 style="margin: 0 0 10px 0; color: #6c757d;">暂无战斗数据</h5>
                    <p style="margin: 0; color: #6c757d; font-size: 14px;">该成员还未参与帮战或数据尚未上传</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 多维度分析 -->
{% if battle_history %}
<div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 6px 20px rgba(0,0,0,0.1); margin-bottom: 25px;">
    <div style="text-align: center; margin-bottom: 25px;">
        <h3 style="margin: 0; color: #2c3e50; font-size: 20px; font-weight: bold;">📊 多维度表现分析</h3>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
        <!-- 评分表现 -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 12px; color: white;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                📈 评分表现
            </h4>
            {% set scores = battle_history|map(attribute='score')|list %}
            {% if scores|length >= 1 %}
                {% set latest_score = scores[-1] %}
                {% set avg_score = (scores|sum / scores|length) %}
                {% set max_score = scores|max %}
                {% set min_score = scores|min %}

                {% if scores|length >= 2 %}
                    {% set previous_score = scores[-2] %}
                    {% set trend = latest_score - previous_score %}
                {% else %}
                    {% set trend = 0 %}
                {% endif %}

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 13px;">
                    <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                        <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">当前评分</div>
                        <div style="font-size: 16px; font-weight: bold;">{{ "%.1f"|format(latest_score) }}分</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                        <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">平均评分</div>
                        <div style="font-size: 16px; font-weight: bold;">{{ "%.1f"|format(avg_score) }}分</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                        <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">
                            {% if scores|length >= 2 %}最佳表现{% else %}首次表现{% endif %}
                        </div>
                        <div style="font-size: 16px; font-weight: bold;">{{ "%.1f"|format(max_score) }}分</div>
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px;">
                        <div style="font-size: 11px; opacity: 0.8; margin-bottom: 4px;">近期趋势</div>
                        <div style="font-size: 14px; font-weight: bold;">
                            {% if scores|length < 2 %}
                                🆕 首次参战
                            {% elif trend > 0 %}
                                📈 +{{ "%.1f"|format(trend) }}
                            {% elif trend < 0 %}
                                📉 {{ "%.1f"|format(trend) }}
                            {% else %}
                                ➡️ 持平
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% else %}
                <div style="text-align: center; padding: 20px; opacity: 0.8;">
                    <div style="font-size: 24px; margin-bottom: 10px;">📊</div>
                    <div style="font-size: 14px;">暂无战斗记录</div>
                    <div style="font-size: 12px; margin-top: 5px;">参与帮战后将显示评分数据</div>
                </div>
            {% endif %}
        </div>

        <!-- 职责表现 -->
        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); padding: 20px; border-radius: 12px; color: white;">
            <h4 style="margin: 0 0 15px 0; font-size: 16px; font-weight: bold; display: flex; align-items: center; gap: 8px;">
                🎯 职责表现
            </h4>
            <div style="margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <span style="font-size: 13px; opacity: 0.9;">主要职责</span>
                    <span style="background: rgba(255,255,255,0.3); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">{{ member.position }}</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-size: 13px; opacity: 0.9;">职业</span>
                    <span style="background: rgba(255,255,255,0.3); padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">{{ member.profession }}</span>
                </div>
            </div>

            {% if battle_history %}
                {% set latest_battle = battle_history[-1] %}
                {% if latest_battle.bonus_items %}
                    <div style="margin-bottom: 12px;">
                        <div style="font-size: 12px; opacity: 0.9; margin-bottom: 6px;">✨ 优势表现：</div>
                        {% for bonus in latest_battle.bonus_items[:3] %}
                            <div style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 6px; font-size: 11px; margin-bottom: 3px;">
                                ✓ {{ bonus }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                {% if latest_battle.penalty_items %}
                    <div>
                        <div style="font-size: 12px; opacity: 0.9; margin-bottom: 6px;">💡 改进空间：</div>
                        {% for penalty in latest_battle.penalty_items[:2] %}
                            <div style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 6px; font-size: 11px; margin-bottom: 3px;">
                                ⚠️ {{ penalty }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

</div>
</body>
</html>
