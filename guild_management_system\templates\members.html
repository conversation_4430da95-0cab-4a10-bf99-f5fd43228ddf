{% extends "base.html" %}

{% block title %}成员详情 - 纸落云烟{% endblock %}

{% block content %}

<!-- 搜索栏 -->
<form method="GET" action="/members" id="searchForm" autocomplete="off">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 25px; border-radius: 15px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);">
        <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: white; margin: 0; font-size: 24px; font-weight: bold;">📋 成员详情列表</h2>
            <p style="color: rgba(255, 255, 255, 0.8); margin: 10px 0 0 0; font-size: 16px;">共 {{ pagination.total }} 名成员</p>
        </div>

        <!-- 搜索框 -->
        <div style="display: flex; justify-content: center;">
            <div style="position: relative; max-width: 500px; width: 100%;">
                {% set current_user = get_current_user() %}
                {% if current_user and current_user.role == 'guest' %}
                    <input type="text" name="search" id="searchInput" placeholder="🔍 搜索具体的角色名称（游客限制：不支持职业批量搜索）" value="{{ search }}"
                           style="width: 100%; padding: 15px 50px 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                                  font-size: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;
                                  backdrop-filter: blur(10px);">
                {% else %}
                    <input type="text" name="search" id="searchInput" placeholder="🔍 搜索成员姓名、职业或团队..." value="{{ search }}"
                           style="width: 100%; padding: 15px 50px 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                                  font-size: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;
                                  backdrop-filter: blur(10px);">
                {% endif %}
                {% if search %}
                <a href="/members" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%);
                                         color: #6c757d; text-decoration: none; font-size: 18px; font-weight: bold;">✕</a>
                {% endif %}
            </div>
        </div>

        <!-- 搜索错误提示 -->
        {% if search_error %}
        <div style="text-align: center; margin-top: 15px;">
            <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 8px; padding: 12px; color: #dc3545; font-size: 14px;">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>搜索限制：</strong>{{ search_error }}
            </div>
        </div>
        {% endif %}

        <!-- 游客搜索限制提示 -->
        {% if current_user and current_user.role == 'guest' %}
        <div style="text-align: center; margin-top: 15px;">
            <div style="background: rgba(255, 193, 7, 0.2); border: 1px solid rgba(255, 193, 7, 0.5); border-radius: 8px; padding: 12px; color: rgba(255, 255, 255, 0.9); font-size: 14px;">
                <i class="fas fa-info-circle"></i>
                <strong>游客搜索限制：</strong>只能搜索具体的角色名称，不支持"素问"、"铁衣"等职业批量搜索
                <br>
                <span style="font-size: 13px; opacity: 0.8;">
                    <a href="{{ url_for('register') }}" style="color: #fff; text-decoration: underline;">注册账号</a> 解锁完整搜索功能
                </span>
            </div>
        </div>
        {% endif %}

        <!-- 搜索提示 -->
        {% if search %}
        <div style="text-align: center; margin-top: 15px;">
            <span style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
                🔍 搜索结果："{{ search }}" - 找到 {{ pagination.total }} 条记录
            </span>
        </div>
        {% endif %}
    </div>
</form>

<!-- 成员列表表格 -->
<div class="stat-card">
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">序号</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">成员姓名</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">职业</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">主团</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">子团</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">小队</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">位置</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">状态</th>
                    <th style="padding: 15px; text-align: center; font-weight: bold; color: #495057;">详细信息</th>
                </tr>
            </thead>
            <tbody id="memberTableBody">
                {% for member in members %}
                <tr class="member-row" style="border-bottom: 1px solid #dee2e6; transition: background-color 0.3s ease;">
                    <td style="padding: 15px; color: #6c757d;">{{ (pagination.page - 1) * pagination.per_page + loop.index }}</td>
                    <td style="padding: 15px;">
                        <div style="font-weight: bold; color: #2c3e50; font-size: 16px; cursor: pointer;" onclick="viewMemberDetail('{{ member.name }}')">
                            {{ member.name }}
                            <span style="font-size: 12px; color: #007bff; margin-left: 5px;">📊</span>
                        </div>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge profession-{{ member.profession }}">{{ member.profession }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: {% if member.get('main_group') == '进攻团' %}#28a745{% elif member.get('main_group') == '防守团' %}#dc3545{% else %}#6c757d{% endif %}; color: white;">{{ member.get('main_group', '其他团') }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #667eea; color: white;">{% set sub_team = member.get('sub_team', '一团') %}{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #17a2b8; color: white;">{{ member.get('squad', '1队') }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #f0f0f0; color: #666;">{{ member.position }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: {% if member.status == '主力' %}#007bff{% elif member.status == '替补' %}#ffc107{% else %}#6c757d{% endif %}; color: white;">{{ member.status or '主力' }}</span>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <button class="detail-btn" onclick="viewMemberDetail('{{ member.name }}')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                            查看详情
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 分页控件 -->
    {% if pagination.total_pages > 1 %}
    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
        <!-- 分页信息 -->
        <div style="color: #6c757d; font-size: 14px;">
            {% set end_item = pagination.page * pagination.per_page %}
            {% if end_item > pagination.total %}{% set end_item = pagination.total %}{% endif %}
            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - {{ end_item }} 条，共 {{ pagination.total }} 条记录
        </div>

        <!-- 分页按钮 -->
        <div style="display: flex; gap: 5px; align-items: center;">
            <!-- 上一页 -->
            {% if pagination.has_prev %}
                <a href="{{ url_for('members_list', page=pagination.prev_num, search=search) }}"
                   style="padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 14px;">
                    ← 上一页
                </a>
            {% else %}
                <span style="padding: 8px 12px; background: #e9ecef; color: #6c757d; border-radius: 5px; font-size: 14px;">
                    ← 上一页
                </span>
            {% endif %}

            <!-- 页码 -->
            {% for page_num in pagination.pages %}
                {% if page_num == pagination.page %}
                    <span style="padding: 8px 12px; background: #007bff; color: white; border-radius: 5px; font-size: 14px; font-weight: bold;">
                        {{ page_num }}
                    </span>
                {% else %}
                    <a href="{{ url_for('members_list', page=page_num, search=search) }}"
                       style="padding: 8px 12px; background: #e9ecef; color: #495057; text-decoration: none; border-radius: 5px; font-size: 14px;">
                        {{ page_num }}
                    </a>
                {% endif %}
            {% endfor %}

            <!-- 下一页 -->
            {% if pagination.has_next %}
                <a href="{{ url_for('members_list', page=pagination.next_num, search=search) }}"
                   style="padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 14px;">
                    下一页 →
                </a>
            {% else %}
                <span style="padding: 8px 12px; background: #e9ecef; color: #6c757d; border-radius: 5px; font-size: 14px;">
                    下一页 →
                </span>
            {% endif %}
        </div>

        <!-- 每页显示数量选择 -->
        <div style="display: flex; align-items: center; gap: 10px;">
            <span style="color: #6c757d; font-size: 14px;">每页显示：</span>
            <select onchange="changePerPage(this.value)" style="padding: 5px 10px; border: 1px solid #dee2e6; border-radius: 5px; font-size: 14px;">
                <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                <option value="20" {% if pagination.per_page == 20 %}selected{% endif %}>20</option>
                <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
            </select>
        </div>
    </div>
    {% endif %}

    <!-- 统计信息 -->
    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">📊 组织架构统计</h3>

        <!-- 总体统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 15px; text-align: center; margin-bottom: 30px;">
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #667eea;">{{ all_members|length }}</div>
                <div style="color: #6c757d;">总成员数</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">{{ all_members|selectattr('main_group', 'equalto', '进攻团')|list|length }}</div>
                <div style="color: #6c757d;">进攻团</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #dc3545;">{{ all_members|selectattr('main_group', 'equalto', '防守团')|list|length }}</div>
                <div style="color: #6c757d;">防守团</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">{{ all_members|selectattr('status', 'in', ['替补', '请假'])|list|length }}</div>
                <div style="color: #6c757d;">替补/请假</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">{{ all_members|selectattr('status', 'equalto', '帮外')|list|length }}</div>
                <div style="color: #6c757d;">帮外</div>
            </div>
        </div>

        <!-- 筛选功能已移除 -->

        <!-- 详细统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <!-- 进攻团统计 -->
            {% set attack_members = all_members|selectattr('main_group', 'equalto', '进攻团')|list %}
            {% if attack_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #28a745;">进攻团 ({{ attack_members|length }}人)</h4>
                {% for sub_team in ['一团', '二团', '三团'] %}
                    {% set sub_members = attack_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                    {% if sub_members %}
                    <div style="margin: 5px 0; font-size: 14px;">
                        <strong>{{ sub_team }}</strong>: {{ sub_members|length }}人
                        <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}

            <!-- 防守团统计 -->
            {% set defense_members = all_members|selectattr('main_group', 'equalto', '防守团')|list %}
            {% if defense_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #dc3545;">防守团 ({{ defense_members|length }}人)</h4>
                {% for sub_team in ['防守团', '防守二团', '防守三团', '防守四团'] %}
                    {% set sub_members = defense_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                    {% if sub_members %}
                    <div style="margin: 5px 0; font-size: 14px;">
                        <strong>{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</strong>: {{ sub_members|length }}人
                        <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}

            <!-- 自定义主分组统计 -->
            {% for main_group in all_main_groups %}
                {% if main_group not in ['进攻团', '防守团', '其他团'] %}
                    {% set custom_members = all_members|selectattr('main_group', 'equalto', main_group)|list %}
                    {% if custom_members %}
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                        <h4 style="margin: 0 0 10px 0; color: #667eea;">{{ main_group }} ({{ custom_members|length }}人)</h4>
                        {% set custom_sub_teams = [] %}
                        {% for member in custom_members %}
                            {% set member_sub_team = member.get('sub_team', main_group) %}
                            {% if member_sub_team not in custom_sub_teams %}
                                {% set _ = custom_sub_teams.append(member_sub_team) %}
                            {% endif %}
                        {% endfor %}
                        {% for sub_team in custom_sub_teams|sort %}
                            {% set sub_members = custom_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                            {% if sub_members %}
                            <div style="margin: 5px 0; font-size: 14px;">
                                <strong>{{ sub_team }}</strong>: {{ sub_members|length }}人
                                <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                                    {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                        {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                        {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    {% endif %}
                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
/* 搜索框样式 */
#searchInput:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}

#searchInput:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 18px rgba(0,0,0,0.12) !important;
}

/* 管理工具样式已移除 */
</style>

<script>
// 获取所有成员行
const memberRows = document.querySelectorAll('.member-row');

// 表格行悬停效果
memberRows.forEach(function(row) {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f8f9fa';
    });

    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});

// 查看成员详情
function viewMemberDetail(memberName) {
    window.location.href = '/member_detail/' + encodeURIComponent(memberName);
}

// 改变每页显示数量
function changePerPage(perPage) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('per_page', perPage);
    urlParams.set('page', '1'); // 重置到第一页
    window.location.href = '/members?' + urlParams.toString();
}

// 搜索功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('成员管理页面加载完成，当前页显示 ' + memberRows.length + ' 名成员');

    // 搜索框延迟提交（避免频繁请求）
    const searchInput = document.getElementById('searchInput');
    const searchForm = document.getElementById('searchForm');
    let searchTimeout;

    if (searchInput && searchForm) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                // 🔒 游客搜索限制检查
                const query = searchInput.value.toLowerCase().trim();
                const isGuest = {{ 'true' if current_user and current_user.role == 'guest' else 'false' }};

                if (isGuest && query) {
                    // 检查是否为职业名搜索
                    const professions = ['素问', '九灵', '潮光', '血河', '神相', '玄机', '铁衣', '龙吟', '碎梦', '沧澜'];
                    const teamKeywords = ['进攻', '防守', '一团', '二团', '三团', '四团', '五团', '替补', '请假'];

                    if (professions.some(p => query.includes(p.toLowerCase())) ||
                        teamKeywords.some(k => query.includes(k))) {
                        // 🔒 不提交搜索，静默拦截
                        showInlineError('只能搜索具体角色名称，不支持职业批量搜索');
                        return;
                    }

                    if (query.length < 2) {
                        // 🔒 不提交搜索，静默拦截
                        showInlineError('搜索词至少需要2个字符');
                        return;
                    }

                    // 清除错误提示
                    clearInlineError();
                }

                // 重置到第一页
                const pageInput = document.createElement('input');
                pageInput.type = 'hidden';
                pageInput.name = 'page';
                pageInput.value = '1';
                searchForm.appendChild(pageInput);

                searchForm.submit();
            }, 1000); // 1000ms延迟，减少输入中断
        });
    }
});

// 显示内联错误提示（在搜索框下方）
function showInlineError(message) {
    // 移除现有的错误提示
    clearInlineError();

    // 找到搜索框容器
    const searchContainer = document.querySelector('form[id="searchForm"] > div');
    if (!searchContainer) return;

    // 创建错误提示
    const errorDiv = document.createElement('div');
    errorDiv.id = 'inlineSearchError';
    errorDiv.style.cssText = `
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid rgba(220, 53, 69, 0.3);
        border-radius: 8px;
        padding: 10px 15px;
        margin-top: 10px;
        color: #dc3545;
        font-size: 14px;
        text-align: center;
        animation: fadeIn 0.3s ease;
    `;
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> ${message}
    `;

    searchContainer.appendChild(errorDiv);
}

// 清除内联错误提示
function clearInlineError() {
    const existingError = document.getElementById('inlineSearchError');
    if (existingError) {
        existingError.remove();
    }
}

// 添加淡入动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// 管理工具函数已移除

// 管理工具相关代码已移除

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+F 聚焦搜索框
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }
});
</script>
{% endblock %}
