#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本 - 为现有成员添加三层级组织结构字段
"""

import json
import os

def migrate_member_data():
    """迁移成员数据，添加新的三层级字段"""
    
    # 读取现有数据
    members_file = 'data/guild_members.json'
    if not os.path.exists(members_file):
        print("成员数据文件不存在")
        return
    
    with open(members_file, 'r', encoding='utf-8') as f:
        members = json.load(f)
    
    print(f"开始迁移 {len(members)} 个成员的数据...")
    
    # 团队映射规则
    team_mapping = {
        '1团': {'main_group': '进攻团', 'sub_team': '一团'},
        '2团': {'main_group': '进攻团', 'sub_team': '二团'},
        '3团': {'main_group': '进攻团', 'sub_team': '三团'},
        '4团': {'main_group': '防守团', 'sub_team': '防守团'},
    }
    
    # 小队映射规则
    squad_mapping = {
        '1队': '1队',
        '2队': '2队', 
        '3队': '3队',
        '4队': '4队',
        '5队': '5队',
        '医疗小队': '1队',
        '输出小队': '2队',
        '拆塔小队': '3队',
        '前排小队': '4队',
        '辅助小队': '5队'
    }
    
    updated_count = 0
    
    for member in members:
        # 获取当前团队信息
        current_team = member.get('team', '其他团')
        current_squad = member.get('squad', '1队')
        
        # 设置新的三层级字段
        if current_team in team_mapping:
            member['main_group'] = team_mapping[current_team]['main_group']
            member['sub_team'] = team_mapping[current_team]['sub_team']
        else:
            member['main_group'] = '其他团'
            member['sub_team'] = '一团'
        
        # 映射小队名称
        if current_squad in squad_mapping:
            member['squad'] = squad_mapping[current_squad]
        else:
            member['squad'] = '1队'
        
        # 确保所有必要字段都存在
        if 'status' not in member:
            member['status'] = '主力'
        
        if 'position' not in member:
            # 根据职业推断位置
            profession = member.get('profession', '')
            if profession in ['素问', '潮光']:
                member['position'] = '治疗'
            elif profession in ['铁衣']:
                member['position'] = '坦克'
            elif profession in ['神相', '玄机']:
                member['position'] = '拆塔'
            else:
                member['position'] = '输出'
        
        updated_count += 1
        print(f"✓ {member['name']}: {member['main_group']} -> {member['sub_team']} -> {member['squad']}")
    
    # 备份原文件
    backup_file = members_file + '.backup'
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)
    print(f"原数据已备份到: {backup_file}")
    
    # 保存更新后的数据
    with open(members_file, 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 数据迁移完成！")
    print(f"   - 更新成员数: {updated_count}")
    print(f"   - 备份文件: {backup_file}")
    print(f"   - 新数据文件: {members_file}")
    
    # 显示统计信息
    print("\n📊 迁移后统计:")
    main_groups = {}
    for member in members:
        main_group = member.get('main_group', '其他团')
        sub_team = member.get('sub_team', '一团')
        squad = member.get('squad', '1队')
        
        if main_group not in main_groups:
            main_groups[main_group] = {}
        if sub_team not in main_groups[main_group]:
            main_groups[main_group][sub_team] = {}
        if squad not in main_groups[main_group][sub_team]:
            main_groups[main_group][sub_team][squad] = 0
        
        main_groups[main_group][sub_team][squad] += 1
    
    for main_group, sub_teams in main_groups.items():
        total_in_main = sum(sum(squads.values()) for squads in sub_teams.values())
        print(f"\n{main_group} ({total_in_main}人):")
        for sub_team, squads in sub_teams.items():
            total_in_sub = sum(squads.values())
            print(f"  {sub_team} ({total_in_sub}人):")
            for squad, count in squads.items():
                print(f"    {squad}: {count}人")

if __name__ == '__main__':
    migrate_member_data()
