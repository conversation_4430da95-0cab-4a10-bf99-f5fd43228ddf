import requests
import json

# 测试拖拽排表的联动功能（不涉及position智能推断）
print("=== 测试拖拽排表联动功能 ===")

# 测试1：把成员从进攻团拖到防守团
print("\n1. 测试进攻团 -> 防守团")
response = requests.post(
    'http://localhost:5000/update_member_position',
    json={
        'member_name': '风悔',
        'main_group': '防守团',
        'sub_team': '防守团',
        'squad': '3队'
    },
    headers={'Content-Type': 'application/json'}
)

result = response.json()
print(f'成功: {result.get("success")}')
print(f'更新字段: {result.get("updated_fields")}')
print(f'变更详情: {result.get("changes")}')

# 测试2：在同一主团内换小队
print("\n2. 测试同团内换小队")
response2 = requests.post(
    'http://localhost:5000/update_member_position',
    json={
        'member_name': '风悔',
        'main_group': '防守团',
        'sub_team': '防守团',
        'squad': '5队'
    },
    headers={'Content-Type': 'application/json'}
)

result2 = response2.json()
print(f'成功: {result2.get("success")}')
print(f'更新字段: {result2.get("updated_fields")}')
print(f'变更详情: {result2.get("changes")}')

# 测试3：拖回进攻团
print("\n3. 测试拖回进攻团")
response3 = requests.post(
    'http://localhost:5000/update_member_position',
    json={
        'member_name': '风悔',
        'main_group': '进攻团',
        'sub_team': '一团',
        'squad': '1队'
    },
    headers={'Content-Type': 'application/json'}
)

result3 = response3.json()
print(f'成功: {result3.get("success")}')
print(f'更新字段: {result3.get("updated_fields")}')
print(f'变更详情: {result3.get("changes")}')

print("\n=== 联动功能说明 ===")
print("✅ 拖拽排表会自动联动更新以下字段:")
print("   - main_group: 主要团队 (进攻团/防守团/其他团)")
print("   - sub_team: 子团队 (一团/二团/三团/防守团)")
print("   - squad: 小队编号 (1队/2队/3队/4队/5队)")
print("   - team: 兼容字段 (1团/2团/3团/4团/其他团)")
print("❌ 不会自动更新position字段，因为小队编号与职能位置无关")
