{% extends "base.html" %}

{% block title %}{{ guild.name }} - 成员管理{% endblock %}

{% block content %}
<style>
    .members-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .members-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .guild-title {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .guild-title h1 {
        color: #2c3e50;
        margin: 0;
        font-size: 2em;
    }

    .guild-id {
        background: #e9ecef;
        color: #495057;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
    }

    .back-btn {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        display: inline-block;
        transition: background 0.3s ease;
    }

    .back-btn:hover {
        background: #5a6268;
        color: white;
    }

    .info-section {
        background: #e7f3ff;
        border-radius: 12px;
        padding: 25px;
        border-left: 5px solid #007bff;
        margin-bottom: 30px;
        text-align: center;
    }

    .info-section h3 {
        color: #004085;
        margin: 0 0 15px 0;
        font-size: 1.5em;
    }

    .info-section p {
        color: #004085;
        margin: 5px 0;
        line-height: 1.5;
        font-size: 1.1em;
    }

    .feature-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .feature-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 25px;
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .feature-card h4 {
        color: #2c3e50;
        margin: 0 0 15px 0;
        font-size: 1.3em;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .feature-card p {
        color: #666;
        margin: 0;
        line-height: 1.5;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }

    .action-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        transition: background 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .action-btn:hover {
        background: #5a6fd8;
        color: white;
    }

    .action-btn.secondary {
        background: #6c757d;
    }

    .action-btn.secondary:hover {
        background: #5a6268;
    }

    .coming-soon {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-align: center;
        padding: 40px;
        border-radius: 15px;
        margin-top: 30px;
    }

    .coming-soon h3 {
        margin: 0 0 15px 0;
        font-size: 2em;
    }

    .coming-soon p {
        margin: 0;
        font-size: 1.2em;
        opacity: 0.9;
    }
</style>

<div class="members-container">
    <div class="members-header">
        <div class="guild-title">
            <h1>👥 {{ guild.name }} - 成员管理</h1>
            <span class="guild-id">{{ guild_id }}</span>
        </div>
        <a href="{{ url_for('admin_guild_detail', guild_id=guild_id) }}" class="back-btn">
            ← 返回帮会详情
        </a>
    </div>

    <!-- 统计信息 -->
    <div class="info-section">
        <h3>📊 成员统计</h3>
        <p>游戏成员: {{ game_members|length }} 人 | 注册用户: {{ guild_users|length }} 人</p>
        <p>总计: {{ (game_members|length) + (guild_users|length) }} 人</p>
    </div>

    <!-- 注册用户列表 -->
    {% if guild_users %}
    <div style="margin-bottom: 40px;">
        <h2 style="color: #2c3e50; font-size: 1.5em; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #667eea;">
            👤 注册用户 ({{ guild_users|length }})
        </h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">
            {% for user in guild_users %}
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #28a745;">
                <div style="font-weight: bold; color: #2c3e50; margin-bottom: 10px; font-size: 1.1em;">
                    {{ user.name or user.username }}
                </div>
                <div style="margin-bottom: 8px;">
                    <strong>用户名:</strong> {{ user.username }}
                </div>
                {% if user.email %}
                <div style="margin-bottom: 8px;">
                    <strong>邮箱:</strong> {{ user.email }}
                </div>
                {% endif %}
                <div style="margin-bottom: 8px;">
                    <strong>角色:</strong>
                    <span style="padding: 2px 6px; border-radius: 8px; font-size: 11px; color: white;
                          background: {% if user.role == 'guild_leader' %}#ff6b6b{% elif user.role == 'super_admin' %}#dc3545{% else %}#4ecdc4{% endif %};">
                        {% if user.role == 'guild_leader' %}大当家
                        {% elif user.role == 'super_admin' %}超级管理员
                        {% else %}普通用户{% endif %}
                    </span>
                </div>
                <div style="margin-bottom: 8px;">
                    <strong>状态:</strong>
                    <span style="padding: 2px 6px; border-radius: 8px; font-size: 11px; color: white;
                          background: {% if user.status == 'active' %}#28a745{% else %}#dc3545{% endif %};">
                        {% if user.status == 'active' %}正常{% else %}封禁{% endif %}
                    </span>
                </div>
                <div style="font-size: 12px; color: #666;">
                    注册时间: {{ user.created_time[:10] if user.created_time else '未知' }}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- 游戏成员列表 -->
    {% if game_members %}
    <div style="margin-bottom: 40px;">
        <h2 style="color: #2c3e50; font-size: 1.5em; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #667eea;">
            🎮 游戏成员 ({{ game_members|length }})
        </h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 15px;">
            {% for member in game_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
                <div style="font-weight: bold; color: #2c3e50; margin-bottom: 8px;">
                    {{ member.name }}
                </div>
                <div style="margin-bottom: 5px; font-size: 14px;">
                    <strong>职业:</strong>
                    <span style="color: {{ get_profession_color(member.profession) }}; font-weight: bold;">
                        {{ member.profession }}
                    </span>
                </div>
                {% if member.main_group %}
                <div style="margin-bottom: 5px; font-size: 14px;">
                    <strong>团队:</strong> {{ member.main_group }}
                    {% if member.sub_team %} - {{ member.sub_team }}{% endif %}
                    {% if member.squad %} - {{ member.squad }}{% endif %}
                </div>
                {% endif %}
                {% if member.position %}
                <div style="margin-bottom: 5px; font-size: 14px;">
                    <strong>职责:</strong> {{ member.position }}
                </div>
                {% endif %}
                <div style="font-size: 12px;">
                    <strong>状态:</strong>
                    <span style="padding: 2px 6px; border-radius: 8px; font-size: 11px; color: white;
                          background: {% if member.status == '主力' %}#28a745{% elif member.status == '替补' %}#ffc107{% elif member.status == '帮外' %}#6c757d{% else %}#dc3545{% endif %};">
                        {{ member.status }}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if not guild_users and not game_members %}
    <div class="info-section">
        <h3>👤 暂无成员</h3>
        <p>该帮会还没有任何成员数据</p>
    </div>
    {% endif %}

    <div class="feature-list">
        <div class="feature-card">
            <h4>📊 成员统计</h4>
            <p>查看帮会成员的详细统计信息，包括活跃度、贡献度、在线时间等数据分析。</p>
        </div>

        <div class="feature-card">
            <h4>👤 成员档案</h4>
            <p>管理每个成员的详细档案信息，包括个人资料、游戏数据、历史记录等。</p>
        </div>

        <div class="feature-card">
            <h4>🎯 权限管理</h4>
            <p>设置成员在帮会中的权限级别，管理不同角色的访问权限和操作权限。</p>
        </div>

        <div class="feature-card">
            <h4>📈 活跃度监控</h4>
            <p>监控成员的活跃度情况，自动统计登录频率、参与度等关键指标。</p>
        </div>

        <div class="feature-card">
            <h4>💬 沟通工具</h4>
            <p>内置的成员沟通工具，支持群发消息、公告发布、活动通知等功能。</p>
        </div>

        <div class="feature-card">
            <h4>📋 批量操作</h4>
            <p>支持批量管理成员，包括批量邀请、批量权限设置、批量数据导出等。</p>
        </div>
    </div>

    <div class="action-buttons">
        <a href="{{ url_for('admin_guild_detail', guild_id=guild_id) }}" class="action-btn secondary">
            📊 查看帮会详情
        </a>
        <a href="{{ url_for('super_admin_dashboard') }}" class="action-btn">
            🏛️ 返回管理平台
        </a>
    </div>

    <div class="coming-soon">
        <h3>🔮 敬请期待</h3>
        <p>更多强大的成员管理功能正在紧锣密鼓地开发中</p>
        <p>我们将为您带来最专业的帮会成员管理体验</p>
    </div>
</div>
{% endblock %}
