import json

# 验证数据状态
with open('data/guild_members.json', 'r', encoding='utf-8') as f:
    members = json.load(f)

print('=== 数据验证 ===')
print(f'总成员数: {len(members)}')

# 检查前几个成员的数据
print('\n前5个成员的排表信息:')
for i, member in enumerate(members[:5]):
    name = member['name']
    team = member.get('team', '无')
    squad = member.get('squad', '无')
    main_group = member.get('main_group', '无')
    sub_team = member.get('sub_team', '无')
    
    print(f'{i+1}. {name}:')
    print(f'   团队: {team}')
    print(f'   小队: {squad}')
    print(f'   主团: {main_group}')
    print(f'   子团: {sub_team}')
    print()

# 统计小队分布
squad_stats = {}
for member in members:
    squad = member.get('squad', '未分配')
    squad_stats[squad] = squad_stats.get(squad, 0) + 1

print('小队分布统计:')
for squad, count in sorted(squad_stats.items()):
    print(f'  {squad}: {count}人')
