{% extends "base.html" %}

{% block title %}{% if readonly_mode %}组织架构 - 纸落云烟{% else %}组织架构图 - 纸落云烟帮会管理系统{% endif %}{% endblock %}

{% block content %}
{% if readonly_mode %}
<!-- 只读模式横幅 -->
<div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; text-align: center; margin-bottom: 20px; border-radius: 10px;">
    <h2 style="margin: 0; font-size: 20px;"><i class="fas fa-sitemap"></i> 组织架构 - 只读模式</h2>
    <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 14px;">📖 查看公会组织结构，与管理后台数据实时同步</p>
</div>
{% endif %}
<style>
    .org-chart {
        margin: 10px;
        max-width: 100%;
    }

    .battle-formation {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 10px;
        box-sizing: border-box;
        align-items: start;
        justify-content: flex-start;
    }

    .formation-column {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(255,255,255,0.3);
        display: flex;
        flex-direction: column;
        backdrop-filter: blur(10px);
        min-width: 200px;
        max-width: 250px;
        flex: 1;
    }

    .column-header {
        margin: 0 0 15px 0;
        font-size: 1.1em;
        color: white;
        text-align: center;
        font-weight: 600;
        padding: 12px;
        border-radius: 8px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .column-header.attack { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); }
    .column-header.defense { background: linear-gradient(135deg, #e17055 0%, #fd79a8 100%); }
    .column-header.special { background: linear-gradient(135deg, #636e72 0%, #2d3436 100%); }

    .squad-section {
        margin-bottom: 12px;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.8) 100%);
        border-radius: 10px;
        padding: 12px;
        border-left: 4px solid #667eea;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        transition: all 0.2s ease;
    }

    .squad-section:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }

    .squad-title {
        font-size: 0.8em;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        text-align: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(240,242,247,0.95) 100%);
        padding: 6px 10px;
        border-radius: 6px;
        flex-shrink: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .squad-members {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        align-content: flex-start;
        justify-content: flex-start;
        min-height: 40px;
    }

    .member-card {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        padding: 8px 10px;
        border-radius: 10px;
        font-size: 0.75em;
        color: #495057;
        font-weight: 500;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        text-align: center;
        min-width: 75px;
        max-width: 95px;
        flex-shrink: 0;
        border: 1px solid rgba(255,255,255,0.5);
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        min-height: 70px;
        padding-bottom: 12px;
    }

    .member-name {
        font-weight: 600;
        color: inherit;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
    }

    .member-position {
        font-size: 0.85em;
        opacity: 0.8;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
    }

    .member-card:hover {
        transform: translateY(-2px) scale(1.02);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        background: linear-gradient(135deg, #dee2e6 0%, #ced4da 100%);
    }

    /* 特殊区域 */
    .special-area {
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.8) 100%);
        border-radius: 10px;
        padding: 12px;
        margin-bottom: 12px;
        border-left: 4px solid #6c757d;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        transition: all 0.2s ease;
    }

    .special-area:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }

    .special-title {
        font-size: 0.8em;
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        text-align: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(240,242,247,0.95) 100%);
        padding: 6px 10px;
        border-radius: 6px;
        flex-shrink: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
        
    /* 职业颜色 */
    .member-card[data-profession="素问"] { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333; }
    .member-card[data-profession="九灵"] { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); color: #333; }
    .member-card[data-profession="潮光"] { background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%); color: white; }
    .member-card[data-profession="血河"] { background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%); color: white; }
    .member-card[data-profession="神相"] { background: linear-gradient(135deg, #3f51b5 0%, #303f9f 100%); color: white; }
    .member-card[data-profession="玄机"] { background: linear-gradient(135deg, #ffeb3b 0%, #fbc02d 100%); color: #333; }
    .member-card[data-profession="铁衣"] { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: #333; }
    .member-card[data-profession="龙吟"] { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; }
    .member-card[data-profession="碎梦"] { background: linear-gradient(135deg, #00695c 0%, #004d40 100%); color: white; }
    .member-card[data-profession="沧澜"] { background: linear-gradient(135deg, #8bc34a 0%, #689f38 100%); color: #333; }

    /* 技能标签样式 - 改进版 */
    .member-skills {
        margin-top: 6px;
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
        justify-content: center;
        max-height: 40px;
        overflow: hidden;
    }

    .skill-tag {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        font-size: 10px;
        padding: 2px 5px;
        border-radius: 4px;
        font-weight: 600;
        white-space: nowrap;
        max-width: 50px;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.3;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        text-shadow: 0 1px 1px rgba(0,0,0,0.3);
    }

    /* 技能标签分类颜色 */
    .skill-tag.attack {
        background: linear-gradient(135deg, #FF5722 0%, #D32F2F 100%);
    }
    .skill-tag.defense {
        background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    }
    .skill-tag.support {
        background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
    }
    .skill-tag.control {
        background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
    }
    .skill-tag.demolition {
        background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    }
</style>

<div class="org-chart">
    <div class="battle-formation" id="battle-formation">
        <!-- 动态生成所有团队 -->

        <!-- 进攻团 -->
        {% for sub_team_name, sub_team_data in organization.get('进攻团', {}).items() %}
            <div class="formation-column" data-main-group="进攻团" data-sub-team="{{ sub_team_name }}">
                <div class="column-header attack">
                    {% if sub_team_name == '一团' %}一团 (进攻)
                    {% elif sub_team_name == '二团' %}二团 (进攻)
                    {% elif sub_team_name == '三团' %}三团 (进攻)
                    {% elif sub_team_name == '四团' %}四团 (进攻)
                    {% else %}{{ sub_team_name }} (进攻){% endif %}
                </div>
                {% if sub_team_data.get('squads') %}
                    {% for squad_name, squad_data in sub_team_data['squads'].items() %}
                        <div class="squad-section">
                            <div class="squad-title">{{ squad_name }}</div>
                            <div class="squad-members">
                                {% for member in squad_data.members %}
                                    <div class="member-card" data-profession="{{ member.profession }}">
                                        <div class="member-name">{{ member.name }}</div>
                                        <div class="member-position">{{ member.get('position', '拆塔') }}</div>
                                        {% if member.get('skills') %}
                                        <div class="member-skills">
                                            {% for skill in member.get('skills', []) %}
                                                <span class="skill-tag">{{ skill }}</span>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        {% endfor %}

        <!-- 防守团 -->
        {% for sub_team_name, sub_team_data in organization.get('防守团', {}).items() %}
            <div class="formation-column" data-main-group="防守团" data-sub-team="{{ sub_team_name }}">
                <div class="column-header defense">
                    {% if sub_team_name == '防守团' %}一团 (防守)
                    {% elif sub_team_name == '防守二团' %}二团 (防守)
                    {% else %}{{ sub_team_name }} (防守){% endif %}
                </div>
                {% if sub_team_data.get('squads') %}
                    {% for squad_name, squad_data in sub_team_data['squads'].items() %}
                        <div class="squad-section">
                            <div class="squad-title">{{ squad_name }}</div>
                            <div class="squad-members">
                                {% for member in squad_data.members %}
                                    <div class="member-card" data-profession="{{ member.profession }}">
                                        <div class="member-name">{{ member.name }}</div>
                                        <div class="member-position">{{ member.get('position', '拆塔') }}</div>
                                        {% if member.get('skills') %}
                                        <div class="member-skills">
                                            {% for skill in member.get('skills', []) %}
                                                <span class="skill-tag">{{ skill }}</span>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        {% endfor %}

        <!-- 自定义主分组 -->
        {% for main_group in all_main_groups %}
            {% if main_group not in ['进攻团', '防守团', '其他团'] %}
                {% for sub_team_name, sub_team_data in organization.get(main_group, {}).items() %}
                    <div class="formation-column" data-main-group="{{ main_group }}" data-sub-team="{{ sub_team_name }}">
                        <div class="column-header special">
                            {% if sub_team_name == main_group %}{{ main_group }}
                            {% else %}{{ sub_team_name }} ({{ main_group }}){% endif %}
                        </div>
                        {% if sub_team_data.get('squads') %}
                            {% for squad_name, squad_data in sub_team_data['squads'].items() %}
                                <div class="squad-section">
                                    <div class="squad-title">{{ squad_name }}</div>
                                    <div class="squad-members">
                                        {% for member in squad_data.members %}
                                            <div class="member-card" data-profession="{{ member.profession }}">
                                                <div class="member-name">{{ member.name }}</div>
                                                <div class="member-position">{{ member.get('position', '拆塔') }}</div>
                                                {% if member.get('skills') %}
                                                <div class="member-skills">
                                                    {% for skill in member.get('skills', []) %}
                                                        <span class="skill-tag">{{ skill }}</span>
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                {% endfor %}
            {% endif %}
        {% endfor %}

        <!-- 其他团（替补、请假、帮外） -->
        <div class="formation-column" data-main-group="其他团">
            <div class="column-header special">其他</div>

            <!-- 替补 -->
            {% if organization.get('其他团', {}).get('替补', {}).get('squads') %}
                <div class="special-area">
                    <div class="special-title">替补</div>
                    <div class="squad-members">
                        {% for squad_name, squad_data in organization['其他团']['替补']['squads'].items() %}
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">
                                    <div class="member-name">{{ member.name }}</div>
                                    <div class="member-position">{{ member.get('position', '拆塔') }}</div>
                                    {% if member.get('skills') %}
                                    <div class="member-skills">
                                        {% for skill in member.get('skills', []) %}
                                            <span class="skill-tag">{{ skill }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- 请假 -->
            {% if organization.get('其他团', {}).get('请假', {}).get('squads') %}
                <div class="special-area">
                    <div class="special-title">请假</div>
                    <div class="squad-members">
                        {% for squad_name, squad_data in organization['其他团']['请假']['squads'].items() %}
                            {% for member in squad_data.members %}
                                <div class="member-card" data-profession="{{ member.profession }}">
                                    <div class="member-name">{{ member.name }}</div>
                                    <div class="member-position">{{ member.get('position', '拆塔') }}</div>
                                    {% if member.get('skills') %}
                                    <div class="member-skills">
                                        {% for skill in member.get('skills', []) %}
                                            <span class="skill-tag">{{ skill }}</span>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- 帮外成员不显示（会很多很多） -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 技能分类函数
    function getSkillCategory(skill) {
        // 攻击类技能
        const attackSkills = ['击杀', '九天', '三绝', '剑破', '点杀', '百步飞剑', '大蛇', '星火', '炉子', '岳飞', '花萦', '花海', '炮', '人伤击杀', '打架'];
        // 防守类技能
        const defenseSkills = ['保活', '无敌帧', '腾龙', '稳住', '沧溟', '猴棍', '老鹰', '资源', '统战'];
        // 辅助类技能
        const supportSkills = ['清泉', 'QTE', '纯肉辅', '新绝', '攻潮', '本家', '奶绝', '山盟', '辅绝', '萌冲'];
        // 控制类技能
        const controlSkills = ['冰墙', '太极', '铁壁', '小车', '降速', '约定', '约'];
        // 拆塔类技能
        const demolitionSkills = ['拆塔', '拆', '金木太剑', '猴棍拆'];

        const skillLower = skill.toLowerCase();

        if (attackSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'attack';
        if (defenseSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'defense';
        if (supportSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'support';
        if (controlSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'control';
        if (demolitionSkills.some(s => skillLower.includes(s.toLowerCase()))) return 'demolition';

        return 'support'; // 默认分类
    }

    // 实时更新组织架构数据
    function refreshOrganizationData() {
        console.log('🔄 刷新组织架构数据...');

        fetch('/api/organization_data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ 获取到最新组织架构数据');
                    updateOrganizationDisplay(data.organization);
                } else {
                    console.error('❌ 获取组织架构数据失败:', data.error);
                }
            })
            .catch(error => {
                console.error('❌ 刷新组织架构数据异常:', error);
            });
    }

    function updateOrganizationDisplay(organization) {
        console.log('🎨 更新组织架构显示', organization);

        // 清空现有显示
        const battleFormation = document.getElementById('battle-formation');
        battleFormation.innerHTML = '';

        // 动态创建进攻团（按指定顺序）
        const attackTeams = organization['进攻团'] || {};
        const attackOrder = ['一团', '二团', '三团', '四团', '五团', '六团'];
        attackOrder.forEach(subTeamName => {
            if (attackTeams[subTeamName]) {
                const teamData = attackTeams[subTeamName];
                const teamColumn = createTeamColumn('进攻团', subTeamName, teamData);
                battleFormation.appendChild(teamColumn);
            }
        });

        // 添加其他进攻团（不在预定义顺序中的）
        Object.keys(attackTeams).forEach(subTeamName => {
            if (!attackOrder.includes(subTeamName)) {
                const teamData = attackTeams[subTeamName];
                const teamColumn = createTeamColumn('进攻团', subTeamName, teamData);
                battleFormation.appendChild(teamColumn);
            }
        });

        // 动态创建防守团（按指定顺序）
        const defenseTeams = organization['防守团'] || {};
        const defenseOrder = ['防守团', '防守二团', '防守三团', '防守四团'];
        defenseOrder.forEach(subTeamName => {
            if (defenseTeams[subTeamName]) {
                const teamData = defenseTeams[subTeamName];
                const teamColumn = createTeamColumn('防守团', subTeamName, teamData);
                battleFormation.appendChild(teamColumn);
            }
        });

        // 添加其他防守团（不在预定义顺序中的）
        Object.keys(defenseTeams).forEach(subTeamName => {
            if (!defenseOrder.includes(subTeamName)) {
                const teamData = defenseTeams[subTeamName];
                const teamColumn = createTeamColumn('防守团', subTeamName, teamData);
                battleFormation.appendChild(teamColumn);
            }
        });

        // 动态创建自定义主分组
        Object.keys(organization).forEach(mainGroup => {
            if (!['进攻团', '防守团', '其他团'].includes(mainGroup)) {
                const customGroupTeams = organization[mainGroup] || {};
                Object.keys(customGroupTeams).forEach(subTeamName => {
                    const teamData = customGroupTeams[subTeamName];
                    const teamColumn = createCustomTeamColumn(mainGroup, subTeamName, teamData);
                    battleFormation.appendChild(teamColumn);
                });
            }
        });

        // 创建其他团（替补、请假、帮外）
        const otherColumn = createOtherColumn(organization['其他团'] || {});
        battleFormation.appendChild(otherColumn);

        console.log('✅ 组织架构显示更新完成');
    }

    function createTeamColumn(mainGroup, subTeamName, teamData) {
        const column = document.createElement('div');
        column.className = 'formation-column';
        column.setAttribute('data-main-group', mainGroup);
        column.setAttribute('data-sub-team', subTeamName);

        // 创建团队头部
        const header = document.createElement('div');
        header.className = `column-header ${mainGroup === '进攻团' ? 'attack' : 'defense'}`;

        // 设置显示名称
        let displayName = subTeamName;
        if (mainGroup === '进攻团') {
            if (subTeamName === '一团') displayName = '一团 (进攻)';
            else if (subTeamName === '二团') displayName = '二团 (进攻)';
            else if (subTeamName === '三团') displayName = '三团 (进攻)';
            else if (subTeamName === '四团') displayName = '四团 (进攻)';
            else displayName = `${subTeamName} (进攻)`;
        } else if (mainGroup === '防守团') {
            if (subTeamName === '防守团') displayName = '一团 (防守)';
            else if (subTeamName === '防守二团') displayName = '二团 (防守)';
            else displayName = `${subTeamName} (防守)`;
        }

        header.textContent = displayName;
        column.appendChild(header);

        // 创建小队
        const squads = teamData.squads || {};
        Object.keys(squads).sort().forEach(squadName => {
            const squadData = squads[squadName];
            const squadElement = createSquadElement(squadName, squadData);
            column.appendChild(squadElement);
        });

        return column;
    }

    function createCustomTeamColumn(mainGroup, subTeamName, teamData) {
        const column = document.createElement('div');
        column.className = 'formation-column';
        column.setAttribute('data-main-group', mainGroup);
        column.setAttribute('data-sub-team', subTeamName);

        // 创建团队头部
        const header = document.createElement('div');
        header.className = 'column-header special';

        // 设置显示名称
        let displayName = subTeamName === mainGroup ? mainGroup : `${subTeamName} (${mainGroup})`;
        header.textContent = displayName;
        column.appendChild(header);

        // 创建小队
        const squads = teamData.squads || {};
        Object.keys(squads).sort().forEach(squadName => {
            const squadData = squads[squadName];
            const squadElement = createSquadElement(squadName, squadData);
            column.appendChild(squadElement);
        });

        return column;
    }

    function createOtherColumn(otherData) {
        const column = document.createElement('div');
        column.className = 'formation-column';
        column.setAttribute('data-main-group', '其他团');

        // 创建头部
        const header = document.createElement('div');
        header.className = 'column-header special';
        header.textContent = '其他';
        column.appendChild(header);

        // 创建替补区域
        if (otherData['替补'] && otherData['替补'].squads) {
            const substituteArea = createSpecialArea('替补', otherData['替补']);
            column.appendChild(substituteArea);
        }

        // 创建请假区域
        if (otherData['请假'] && otherData['请假'].squads) {
            const leaveArea = createSpecialArea('请假', otherData['请假']);
            column.appendChild(leaveArea);
        }

        // 帮外成员不显示（会很多很多）

        return column;
    }

    function createSquadElement(squadName, squadData) {
        const squadDiv = document.createElement('div');
        squadDiv.className = 'squad-section';

        const titleDiv = document.createElement('div');
        titleDiv.className = 'squad-title';
        titleDiv.textContent = squadName;

        const membersDiv = document.createElement('div');
        membersDiv.className = 'squad-members';

        // 添加成员
        (squadData.members || []).forEach(member => {
            const memberCard = document.createElement('div');
            memberCard.className = 'member-card';
            memberCard.setAttribute('data-profession', member.profession);

            const nameDiv = document.createElement('div');
            nameDiv.className = 'member-name';
            nameDiv.textContent = member.name;

            const positionDiv = document.createElement('div');
            positionDiv.className = 'member-position';
            positionDiv.textContent = member.position || '拆塔';

            memberCard.appendChild(nameDiv);
            memberCard.appendChild(positionDiv);

            // 添加技能标签
            if (member.skills && member.skills.length > 0) {
                const skillsDiv = document.createElement('div');
                skillsDiv.className = 'member-skills';

                member.skills.forEach(skill => {
                    const skillTag = document.createElement('span');
                    skillTag.className = 'skill-tag ' + getSkillCategory(skill);
                    skillTag.textContent = skill;
                    skillTag.title = skill; // 添加完整技能名称的提示
                    skillsDiv.appendChild(skillTag);
                });

                memberCard.appendChild(skillsDiv);
            }

            membersDiv.appendChild(memberCard);
        });

        squadDiv.appendChild(titleDiv);
        squadDiv.appendChild(membersDiv);

        return squadDiv;
    }



    function createSpecialArea(title, teamData) {
        const areaDiv = document.createElement('div');
        areaDiv.className = 'special-area';

        const titleDiv = document.createElement('div');
        titleDiv.className = 'special-title';
        titleDiv.textContent = title;

        const membersDiv = document.createElement('div');
        membersDiv.className = 'squad-members';

        // 添加所有小队的成员
        const squads = teamData.squads || {};
        Object.values(squads).forEach(squadData => {
            (squadData.members || []).forEach(member => {
                const memberCard = document.createElement('div');
                memberCard.className = 'member-card';
                memberCard.setAttribute('data-profession', member.profession);

                const nameDiv = document.createElement('div');
                nameDiv.className = 'member-name';
                nameDiv.textContent = member.name;

                const positionDiv = document.createElement('div');
                positionDiv.className = 'member-position';
                positionDiv.textContent = member.position || '拆塔';

                memberCard.appendChild(nameDiv);
                memberCard.appendChild(positionDiv);

                // 添加技能标签
                if (member.skills && member.skills.length > 0) {
                    const skillsDiv = document.createElement('div');
                    skillsDiv.className = 'member-skills';

                    member.skills.forEach(skill => {
                        const skillTag = document.createElement('span');
                        skillTag.className = 'skill-tag ' + getSkillCategory(skill);
                        skillTag.textContent = skill;
                        skillTag.title = skill; // 添加完整技能名称的提示
                        skillsDiv.appendChild(skillTag);
                    });

                    memberCard.appendChild(skillsDiv);
                }

                membersDiv.appendChild(memberCard);
            });
        });

        areaDiv.appendChild(titleDiv);
        areaDiv.appendChild(membersDiv);

        return areaDiv;
    }

    // 为现有的技能标签应用分类样式
    function applySkillCategoriesToExistingTags() {
        const skillTags = document.querySelectorAll('.skill-tag');
        skillTags.forEach(tag => {
            const skillText = tag.textContent.trim();
            const category = getSkillCategory(skillText);
            tag.className = 'skill-tag ' + category;
            tag.title = skillText; // 添加完整技能名称的提示
        });
    }

    // 页面加载完成后开始定时刷新
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📋 组织架构页面加载完成，开始实时更新');

        // 为现有技能标签应用分类样式
        applySkillCategoriesToExistingTags();

        // 立即刷新一次
        refreshOrganizationData();

        // 每5秒刷新一次
        setInterval(refreshOrganizationData, 5000);
    });
</script>
{% endblock %}
