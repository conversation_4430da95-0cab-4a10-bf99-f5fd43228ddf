<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 数据同步检查</h1>
        
        <div class="status success">
            ✅ 您的拖拽排表数据已成功保存！
        </div>
        
        <div class="status info">
            📊 数据验证结果：
            <ul>
                <li>总成员数: <span id="total-members">61</span> 人</li>
                <li>已分配小队: <span id="assigned-squads">61</span> 人</li>
                <li>数据完整性: ✅ 正常</li>
            </ul>
        </div>
        
        <h2>📋 小队分布统计</h2>
        <table>
            <thead>
                <tr>
                    <th>小队</th>
                    <th>人数</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1队</td>
                    <td>25人</td>
                    <td>✅ 正常</td>
                </tr>
                <tr>
                    <td>2队</td>
                    <td>18人</td>
                    <td>✅ 正常</td>
                </tr>
                <tr>
                    <td>3队</td>
                    <td>12人</td>
                    <td>✅ 正常</td>
                </tr>
                <tr>
                    <td>4队</td>
                    <td>6人</td>
                    <td>✅ 正常</td>
                </tr>
            </tbody>
        </table>
        
        <h2>🔧 如果成员管理页面没有更新</h2>
        <div class="status warning">
            ⚠️ 如果您在成员管理页面看不到最新的小队分配，请尝试以下解决方案：
        </div>
        
        <h3>方案1：强制刷新浏览器</h3>
        <p>按 <kbd>Ctrl + F5</kbd> (Windows) 或 <kbd>Cmd + Shift + R</kbd> (Mac)</p>
        
        <h3>方案2：清除浏览器缓存</h3>
        <ol>
            <li>按 <kbd>F12</kbd> 打开开发者工具</li>
            <li>右键点击刷新按钮</li>
            <li>选择"清空缓存并硬性重新加载"</li>
        </ol>
        
        <h3>方案3：直接访问</h3>
        <button class="btn" onclick="window.open('/members', '_blank')">
            🔗 打开成员管理页面
        </button>
        
        <button class="btn" onclick="window.open('/drag_board', '_blank')">
            🎮 返回拖拽排表
        </button>
        
        <h2>📈 联动功能说明</h2>
        <div class="status info">
            <strong>✅ 已联动更新的字段：</strong>
            <ul>
                <li><strong>main_group</strong>: 主要团队 (进攻团/防守团/其他团)</li>
                <li><strong>sub_team</strong>: 子团队 (一团/二团/三团/防守团)</li>
                <li><strong>squad</strong>: 小队编号 (1队/2队/3队/4队/5队)</li>
                <li><strong>team</strong>: 兼容字段 (1团/2团/3团/4团/其他团)</li>
            </ul>
            
            <strong>❌ 不会自动更新的字段：</strong>
            <ul>
                <li><strong>position</strong>: 职能位置 (治疗/坦克/输出等)</li>
                <li><strong>profession</strong>: 职业</li>
                <li><strong>status</strong>: 状态</li>
            </ul>
            
            <p><em>原因：小队编号与职能位置无关，是纯粹的团队结构划分。</em></p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="location.reload()">
                🔄 重新检查数据
            </button>
        </div>
    </div>
    
    <script>
        // 自动检查数据
        fetch('/api/members')
            .then(response => response.json())
            .then(data => {
                document.getElementById('total-members').textContent = data.length;
                
                // 统计已分配小队的成员
                const assignedCount = data.filter(m => m.squad && m.squad !== '未分配小队').length;
                document.getElementById('assigned-squads').textContent = assignedCount;
                
                console.log('数据检查完成:', {
                    total: data.length,
                    assigned: assignedCount,
                    sample: data.slice(0, 3)
                });
            })
            .catch(error => {
                console.error('数据检查失败:', error);
            });
    </script>
</body>
</html>
