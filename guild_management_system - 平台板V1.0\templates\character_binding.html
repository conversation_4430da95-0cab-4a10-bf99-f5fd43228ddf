{% extends "base.html" %}

{% block title %}角色绑定 - 逆水寒帮会辅助管理系统{% endblock %}

{% block content %}
<style>
    .binding-container {
        max-width: 800px;
        margin: 0 auto;
    }
    
    .binding-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .binding-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        font-size: 16px;
    }
    
    .form-input {
        width: 100%;
        padding: 12px 15px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.95);
        transition: all 0.3s ease;
    }
    
    .form-input:focus {
        outline: none;
        background: white;
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    }
    
    .submit-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .submit-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-pending {
        background: #ffc107;
        color: #856404;
    }
    
    .status-approved {
        background: #28a745;
        color: white;
    }
    
    .status-rejected {
        background: #dc3545;
        color: white;
    }
    
    .verification-info {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .verification-code {
        font-size: 24px;
        font-weight: bold;
        color: #1976d2;
        text-align: center;
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        letter-spacing: 3px;
    }
    
    .binding-history {
        margin-top: 30px;
    }
    
    .history-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        border-left: 4px solid #dee2e6;
    }
    
    .history-item.pending {
        border-left-color: #ffc107;
    }
    
    .history-item.approved {
        border-left-color: #28a745;
    }
    
    .history-item.rejected {
        border-left-color: #dc3545;
    }
    
    .character-name {
        font-size: 18px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 8px;
    }
    
    .binding-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #6c757d;
    }
    
    .help-text {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 14px;
        color: #856404;
    }
</style>

<div class="binding-container">
    <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
        <i class="fas fa-link"></i> 角色绑定
    </h2>
    
    <!-- 绑定申请表单 -->
    <div class="binding-form">
        <h3 style="margin: 0 0 20px 0; font-size: 24px;">🎮 绑定游戏角色</h3>
        <p style="margin: 0 0 25px 0; opacity: 0.9; font-size: 16px;">
            绑定您的游戏角色后，可以查看个人战斗数据分析和表现评估
        </p>
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label" for="character_name">
                    <i class="fas fa-user"></i> 角色名称
                </label>
                <input type="text" 
                       id="character_name" 
                       name="character_name" 
                       class="form-input"
                       placeholder="请输入您的游戏角色名称"
                       required>
            </div>
            
            <button type="submit" class="submit-btn">
                <i class="fas fa-paper-plane"></i> 提交绑定申请
            </button>
        </form>
        
        <div class="help-text">
            <strong>📋 绑定流程说明：</strong><br>
            1. 输入您的游戏角色名称并提交申请<br>
            2. 系统会生成验证码，请记录验证码<br>
            3. 等待帮会管理员审核通过<br>
            4. 绑定成功后即可查看个人数据分析
        </div>
    </div>
    
    <!-- 绑定历史 -->
    {% if bindings %}
    <div class="binding-card">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50;">
            <i class="fas fa-history"></i> 绑定记录
        </h3>
        
        <div class="binding-history">
            {% for binding in bindings %}
            <div class="history-item {{ binding.status }}">
                <div class="character-name">{{ binding.character_name }}</div>
                <div class="binding-meta">
                    <div>
                        <span class="status-badge status-{{ binding.status }}">
                            {% if binding.status == 'pending' %}
                                ⏳ 待审核
                            {% elif binding.status == 'approved' %}
                                ✅ 已通过
                            {% elif binding.status == 'rejected' %}
                                ❌ 已拒绝
                            {% endif %}
                        </span>
                        {% if binding.status == 'pending' and binding.verification_code %}
                        <span style="margin-left: 15px; color: #1976d2; font-weight: bold;">
                            验证码: {{ binding.verification_code }}
                        </span>
                        {% endif %}
                    </div>
                    <div>
                        申请时间: {{ binding.created_at.strftime('%Y-%m-%d %H:%M') if binding.created_at else '未知' }}
                    </div>
                </div>
                
                {% if binding.status == 'pending' %}
                <div class="verification-info">
                    <div style="text-align: center;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">
                            <i class="fas fa-key"></i> 验证码
                        </h4>
                        <div class="verification-code">{{ binding.verification_code }}</div>
                        <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                            请将此验证码提供给帮会管理员进行验证
                        </p>
                    </div>
                </div>
                {% elif binding.status == 'approved' %}
                <div style="margin-top: 15px; padding: 10px; background: #d4edda; border-radius: 5px; color: #155724;">
                    <i class="fas fa-check-circle"></i> 
                    角色绑定成功！现在您可以查看个人战斗数据分析了。
                    {% if binding.approved_at %}
                    <br><small>审核时间: {{ binding.approved_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% endif %}
                </div>
                {% elif binding.status == 'rejected' %}
                <div style="margin-top: 15px; padding: 10px; background: #f8d7da; border-radius: 5px; color: #721c24;">
                    <i class="fas fa-times-circle"></i> 
                    绑定申请被拒绝，请联系帮会管理员了解详情。
                    {% if binding.approved_at %}
                    <br><small>处理时间: {{ binding.approved_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- 功能说明 -->
    <div class="binding-card">
        <h3 style="margin: 0 0 20px 0; color: #2c3e50;">
            <i class="fas fa-info-circle"></i> 绑定后可享受的功能
        </h3>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <div style="font-size: 40px; margin-bottom: 10px; color: #667eea;">📊</div>
                <h5 style="margin: 0 0 8px 0;">个人数据分析</h5>
                <p style="margin: 0; font-size: 14px; color: #6c757d;">详细的战斗表现分析</p>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <div style="font-size: 40px; margin-bottom: 10px; color: #667eea;">🎯</div>
                <h5 style="margin: 0 0 8px 0;">能力雷达图</h5>
                <p style="margin: 0; font-size: 14px; color: #6c757d;">多维度能力评估</p>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <div style="font-size: 40px; margin-bottom: 10px; color: #667eea;">📈</div>
                <h5 style="margin: 0 0 8px 0;">成长趋势</h5>
                <p style="margin: 0; font-size: 14px; color: #6c757d;">历史表现趋势分析</p>
            </div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                <div style="font-size: 40px; margin-bottom: 10px; color: #667eea;">🏆</div>
                <h5 style="margin: 0 0 8px 0;">光荣墙展示</h5>
                <p style="margin: 0; font-size: 14px; color: #6c757d;">优秀表现公开展示</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
