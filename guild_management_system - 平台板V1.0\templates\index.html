{% extends "base.html" %}

{% block title %}指挥中心 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<style>
    .dashboard-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
    }

    .dashboard-header h1 {
        margin: 0 0 10px 0;
        font-size: 2.2em;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .dashboard-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 1.1em;
    }

    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .quick-stat-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #667eea;
        transition: transform 0.2s ease;
    }

    .quick-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }

    .quick-stat-number {
        font-size: 2.5em;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }

    .quick-stat-label {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 10px;
    }

    .quick-stat-detail {
        font-size: 0.8em;
        color: #999;
    }

    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .action-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
    }

    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.15);
    }

    .action-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .action-icon {
        font-size: 2em;
        margin-right: 15px;
    }

    .action-title {
        font-size: 1.3em;
        font-weight: bold;
        color: #333;
    }

    .action-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.5;
    }

    .action-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 1em;
        transition: all 0.2s ease;
        width: 100%;
    }

    .action-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    }

    .recent-activity {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .recent-activity h3 {
        margin: 0 0 20px 0;
        color: #333;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 1.2em;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 3px;
    }

    .activity-time {
        font-size: 0.85em;
        color: #999;
    }

    .battle-icon { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; }
    .member-icon { background: linear-gradient(135deg, #4834d4, #686de0); color: white; }
    .team-icon { background: linear-gradient(135deg, #00d2d3, #54a0ff); color: white; }

    .guild-overview {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 25px;
        margin-bottom: 30px;
    }

    .organization-preview {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .organization-preview h3 {
        margin: 0 0 20px 0;
        color: #333;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .team-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }

    .team-summary-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .team-summary-count {
        font-size: 1.8em;
        font-weight: bold;
        color: #667eea;
    }

    .team-summary-label {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
    }
</style>

<!-- 仪表板头部 -->
<div class="dashboard-header">
    <h1>🏰 {% if guild_info %}{{ guild_info.name }}{% else %}逆水寒帮会{% endif %}指挥中心</h1>
    <p>帮会管理 · 战斗分析 · 数据洞察</p>
</div>

<!-- 快速统计 -->
<div class="quick-stats">
    <div class="quick-stat-card">
        <div class="quick-stat-number" id="total-members">{{ total_members }}</div>
        <div class="quick-stat-label">帮会成员</div>
        <div class="quick-stat-detail">
            主力: {{ active_members }}人 | 替补: {{ substitute_members }}人
        </div>
    </div>
    <div class="quick-stat-card">
        <div class="quick-stat-number" id="external-members">{{ external_members }}</div>
        <div class="quick-stat-label">帮外人员</div>
        <div class="quick-stat-detail">
            不计入帮会成员统计
        </div>
    </div>
    <div class="quick-stat-card">
        <div class="quick-stat-number">{{ battle_count }}</div>
        <div class="quick-stat-label">战斗记录</div>
        <div class="quick-stat-detail">
            {% if latest_battle %}最近: {{ latest_battle.enemy_guild }}{% else %}暂无记录{% endif %}
        </div>
    </div>
    <div class="quick-stat-card">
        <div class="quick-stat-number">{{ participated_members }}</div>
        <div class="quick-stat-label">参战成员</div>
        <div class="quick-stat-detail">
            参战率: {{ participation_rate }}%
        </div>
    </div>
    <div class="quick-stat-card">
        <div class="quick-stat-number">{{ avg_score|round(1) }}</div>
        <div class="quick-stat-label">平均评分</div>
        <div class="quick-stat-detail">
            {% if avg_score >= 60 %}表现优秀{% elif avg_score >= 40 %}表现良好{% else %}需要提升{% endif %}
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="action-grid">
    <div class="action-card">
        <div class="action-header">
            <div class="action-icon">👥</div>
            <div class="action-title">成员详情</div>
        </div>
        <div class="action-description">
            查看帮会成员详细信息和数据统计
        </div>
        <button class="action-button" onclick="window.location.href='/members'">
            查看详情
        </button>
    </div>

    <div class="action-card">
        <div class="action-header">
            <div class="action-icon">🎮</div>
            <div class="action-title">拖拽排表</div>
        </div>
        <div class="action-description">
            通过拖拽方式快速调整成员分组，实时更新组织架构
        </div>
        <button class="action-button" onclick="window.location.href='/drag_board'">
            开始排表
        </button>
    </div>

    <div class="action-card">
        <div class="action-header">
            <div class="action-icon">⚔️</div>
            <div class="action-title">战斗分析</div>
        </div>
        <div class="action-description">
            上传战斗数据，分析成员表现，生成详细的战斗报告
        </div>
        <button class="action-button" onclick="window.location.href='/battle_analysis'">
            分析战斗
        </button>
    </div>

    <div class="action-card">
        <div class="action-header">
            <div class="action-icon">🏗️</div>
            <div class="action-title">组织架构</div>
        </div>
        <div class="action-description">
            查看帮会的完整组织结构，了解各团队配置情况
        </div>
        <button class="action-button" onclick="window.location.href='/organization_chart'">
            查看架构
        </button>
    </div>
</div>

<!-- 帮会概览 -->
<div class="guild-overview">
    <div class="organization-preview">
        <h3>🏗️ 组织架构概览</h3>
        <div class="team-summary">
            <div class="team-summary-item">
                <div class="team-summary-count" id="attack-members">{{ organization.get('进攻团', {}).values() | map(attribute='count') | sum }}</div>
                <div class="team-summary-label">进攻团</div>
            </div>
            <div class="team-summary-item">
                <div class="team-summary-count" id="defense-members">{{ organization.get('防守团', {}).values() | map(attribute='count') | sum }}</div>
                <div class="team-summary-label">防守团</div>
            </div>
            <div class="team-summary-item">
                <div class="team-summary-count" id="other-members">{{ organization.get('其他团', {}).values() | map(attribute='count') | sum }}</div>
                <div class="team-summary-label">其他团</div>
            </div>
        </div>
    </div>

    <div class="recent-activity">
        <h3>📈 最近活动</h3>
        {% if recent_activities %}
            {% for activity in recent_activities %}
            <div class="activity-item">
                <div class="activity-icon {{ activity.type }}-icon">
                    {% if activity.type == 'battle' %}⚔️
                    {% elif activity.type == 'member' %}👤
                    {% elif activity.type == 'team' %}🏗️
                    {% endif %}
                </div>
                <div class="activity-content">
                    <div class="activity-title">{{ activity.title }}</div>
                    <div class="activity-time">{{ activity.time }}</div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div style="text-align: center; color: #999; padding: 20px;">
                <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                <div>暂无最近活动</div>
            </div>
        {% endif %}
    </div>
</div>

<!-- 职业分布和战斗表现 -->
<div class="guild-overview">
    <div class="organization-preview">
        <h3>⚡ 职业分布</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px;">
            {% for profession, count in profession_stats.items() %}
            <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                <div style="font-size: 1.5em; font-weight: bold; color: #667eea;">{{ count }}</div>
                <div style="font-size: 0.8em; color: #666;">{{ profession }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="organization-preview">
        <h3>🏆 战斗表现</h3>
        {% if top_performers %}
            {% for performer in top_performers %}
            <div class="activity-item">
                <div class="activity-icon battle-icon">🌟</div>
                <div class="activity-content">
                    <div class="activity-title">{{ performer.name }}</div>
                    <div class="activity-time">平均评分: {{ performer.avg_score|round(1) }}分</div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div style="text-align: center; color: #999; padding: 20px;">
                <div style="font-size: 2em; margin-bottom: 10px;">⚔️</div>
                <div>暂无战斗数据</div>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('指挥中心页面加载完成');

    // 设置当前页面的导航状态
    const overviewTab = document.querySelector('.nav-tab[onclick*="overview"]');
    if (overviewTab) {
        overviewTab.classList.add('active');
    }
});

// 快速跳转函数
function quickJump(page) {
    window.location.href = page;
}
</script>
{% endblock %}
