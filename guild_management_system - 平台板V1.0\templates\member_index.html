{% extends "base.html" %}

{% block title %}纸落云烟 - 成员访问{% endblock %}

{% block content %}
<style>
    .readonly-banner {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px;
        text-align: center;
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .readonly-banner h2 {
        margin: 0;
        font-size: 24px;
    }
    
    .readonly-banner p {
        margin: 5px 0 0 0;
        opacity: 0.9;
        font-size: 14px;
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 2px solid transparent;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        border-color: #28a745;
    }
    
    .feature-icon {
        font-size: 48px;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .feature-title {
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
        color: #333;
    }
    
    .feature-description {
        text-align: center;
        color: #666;
        margin-bottom: 20px;
        line-height: 1.5;
    }
    
    .feature-btn {
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .feature-btn:hover {
        background: linear-gradient(135deg, #218838, #1ea085);
        transform: translateY(-2px);
    }
    
    .stats-overview {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }
    
    .stat-card {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        border-color: #28a745;
        transform: translateY(-3px);
    }
    
    .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .recent-activities {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .activity-item {
        padding: 15px;
        border-left: 4px solid #28a745;
        background: #f8f9fa;
        margin-bottom: 10px;
        border-radius: 0 8px 8px 0;
    }
    
    .activity-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    
    .activity-time {
        font-size: 12px;
        color: #666;
    }
</style>

<!-- 只读访问横幅 -->
<div class="readonly-banner">
    <h2>🛡️ 纸落云烟公会 - 成员访问系统</h2>
    <p>📖 只读模式 | 查看成员信息、组织架构和战斗数据</p>
</div>

<!-- 统计概览 -->
<div class="stats-overview">
    <h3 style="text-align: center; margin-bottom: 20px; color: #333;">
        <i class="fas fa-chart-bar"></i> 公会数据概览
    </h3>
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_members }}</div>
            <div class="stat-label">帮会成员</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ external_members }}</div>
            <div class="stat-label">帮外人员</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_members }}</div>
            <div class="stat-label">主力成员</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ substitute_members }}</div>
            <div class="stat-label">替补成员</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ battle_count }}</div>
            <div class="stat-label">战斗记录</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ participation_rate }}%</div>
            <div class="stat-label">参战率</div>
        </div>
    </div>
</div>

<!-- 功能入口 -->
<div class="feature-grid">
    <div class="feature-card">
        <div class="feature-icon">👥</div>
        <div class="feature-title">成员详情</div>
        <div class="feature-description">
            查看所有公会成员的详细信息，包括职业、职责、团队分配等
        </div>
        <button class="feature-btn" onclick="window.location.href='/members'">
            查看成员详情
        </button>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon">🏛️</div>
        <div class="feature-title">组织架构</div>
        <div class="feature-description">
            查看公会的组织结构，包括进攻团、防守团的人员分配情况
        </div>
        <button class="feature-btn" onclick="window.location.href='/organization'">
            查看组织架构
        </button>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon">⚔️</div>
        <div class="feature-title">战斗分析</div>
        <div class="feature-description">
            查看历史战斗记录和数据分析，了解成员表现和团队战绩
        </div>
        <button class="feature-btn" onclick="window.location.href='/battle_analysis'">
            查看战斗分析
        </button>
    </div>
</div>

<!-- 最近活动 -->
{% if recent_activities %}
<div class="recent-activities">
    <h3 style="margin-bottom: 20px; color: #333;">
        <i class="fas fa-clock"></i> 最近活动
    </h3>
    {% for activity in recent_activities %}
    <div class="activity-item">
        <div class="activity-title">{{ activity.title }}</div>
        <div class="activity-time">{{ activity.time }} | {{ activity.description }}</div>
    </div>
    {% endfor %}
</div>
{% endif %}

<script>
// 禁用所有可能的编辑功能
document.addEventListener('DOMContentLoaded', function() {
    // 禁用右键菜单中的编辑选项
    document.addEventListener('contextmenu', function(e) {
        // 允许右键，但提示这是只读模式
        console.log('只读模式：无编辑权限');
    });
    
    // 添加只读模式提示
    console.log('🛡️ 当前为只读模式，无法进行编辑操作');
});
</script>
{% endblock %}
