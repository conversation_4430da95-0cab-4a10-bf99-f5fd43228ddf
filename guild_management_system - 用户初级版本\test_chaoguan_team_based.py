#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试潮光团队类型考核修正
"""

def test_chaoguan_team_based_assessment():
    """测试潮光团队类型考核修正"""
    print("=" * 80)
    print("🧪 测试潮光团队类型考核修正")
    print("=" * 80)
    
    print("\n1️⃣ 潮光考核规则修正")
    print("=" * 50)
    
    print("\n修正前的错误规则：")
    print("❌ 所有潮光都考核清泉（无论职责）")
    print("❌ 辅助潮光固定考核建筑伤害")
    print("❌ 没有区分进攻团和防守团")
    
    print("\n修正后的正确规则：")
    print("✅ 只有辅助潮光才考核清泉")
    print("✅ 进攻团辅助潮光：清泉 + 塔伤（建筑伤害）")
    print("✅ 防守团辅助潮光：清泉 + 人伤（玩家伤害）")
    print("✅ 非辅助潮光：不考核清泉，只考核对应职责")
    
    print("\n2️⃣ 不同职责的潮光评分")
    print("=" * 50)
    
    print("\n辅助潮光（进攻团）：")
    print("🌊 潮光辅助职责评分 (清泉+团队特定考核)")
    print("✅ 清泉数据优秀: +18.8分 (9/4.0)")
    print("✅ 建筑伤害优秀: +13.3分 (1666万/1000万)  # 使用拆塔专项平均值")
    print("❌ 重伤过多: -3.2分 (7/5.5)")
    print("最终评分: 78.9分")
    
    print("\n辅助潮光（防守团）：")
    print("🌊 潮光辅助职责评分 (清泉+团队特定考核)")
    print("✅ 清泉数据优秀: +18.8分 (9/4.0)")
    print("✅ 玩家伤害优秀: +16.4分 (983万/600万)  # 使用人伤专项平均值")
    print("❌ 重伤过多: -3.2分 (7/5.5)")
    print("最终评分: 82.0分")
    
    print("\n拆塔潮光：")
    print("🌊 潮光拆塔职责评分 (不考核清泉)")
    print("✅ 建筑伤害优秀: +22.5分 (1666万/1000万)")
    print("✅ 击杀表现良好: +0.8分 (16/12.1)  # 次要加分")
    print("❌ 重伤过多: -3.2分 (7/5.5)")
    print("最终评分: 70.1分")
    
    print("\n击杀潮光：")
    print("🌊 潮光击杀职责评分 (不考核清泉)")
    print("✅ 击杀数据优秀: +25.2分 (16/8.1)")
    print("✅ 玩家伤害良好: +1.5分 (983万/820万)  # 次要加分")
    print("❌ 重伤过多: -3.2分 (7/5.5)")
    print("最终评分: 73.5分")
    
    print("\n人伤潮光：")
    print("🌊 潮光人伤职责评分 (不考核清泉)")
    print("✅ 玩家伤害优秀: +18.2分 (983万/600万)")
    print("✅ 击杀表现良好: +1.5分 (16/12.1)  # 次要加分")
    print("❌ 重伤过多: -3.2分 (7/5.5)")
    print("最终评分: 66.5分")
    
    print("\n3️⃣ 团队类型判断逻辑")
    print("=" * 50)
    
    print("\n团队类型来源：")
    print("✅ 从成员的组织架构信息获取")
    print("✅ main_group字段：进攻团/防守团/其他团")
    print("✅ 确保团队类型准确传递到评分函数")
    
    print("\n进攻团辅助潮光考核逻辑：")
    print("1. 必要考核：清泉数据（权重25）")
    print("2. 主要考核：建筑伤害（权重20，使用拆塔专项平均值）")
    print("3. 生存考核：重伤扣分（权重-8）")
    
    print("\n防守团辅助潮光考核逻辑：")
    print("1. 必要考核：清泉数据（权重25）")
    print("2. 主要考核：玩家伤害（权重20，使用人伤专项平均值）")
    print("3. 生存考核：重伤扣分（权重-8）")
    
    print("\n4️⃣ 平均值使用策略")
    print("=" * 50)
    
    print("\n进攻团辅助潮光：")
    print("✅ 清泉：使用潮光专项平均值")
    print("✅ 建筑伤害：使用拆塔职责专项平均值")
    print("✅ 重伤：使用全体平均值")
    
    print("\n防守团辅助潮光：")
    print("✅ 清泉：使用潮光专项平均值")
    print("✅ 玩家伤害：使用人伤职责专项平均值")
    print("✅ 重伤：使用全体平均值")
    
    print("\n非辅助潮光：")
    print("✅ 主要指标：使用对应职责专项平均值")
    print("✅ 次要指标：使用对应职责专项平均值")
    print("✅ 重伤：使用全体平均值")
    print("❌ 不考核清泉数据")
    
    print("\n5️⃣ 调试信息示例")
    print("=" * 50)
    
    print("\n进攻团辅助潮光：")
    print("评分 搅史的棍: 职责=辅助, 职业=潮光, 位置=辅助")
    print("  潮光辅助评分: 清泉值=9, 潮光专项平均值=4.0")
    print("  进攻团潮光辅助建筑伤害评分: 建筑伤害=16663973, 拆塔专项平均值=10000000")
    print("  进攻团潮光辅助建筑伤害计算: 比率=1.67, 得分=13.3")
    
    print("\n防守团辅助潮光：")
    print("评分 搅史的棍: 职责=辅助, 职业=潮光, 位置=辅助")
    print("  潮光辅助评分: 清泉值=9, 潮光专项平均值=4.0")
    print("  防守团潮光辅助人伤评分: 玩家伤害=9836645, 人伤专项平均值=6000000")
    print("  防守团潮光辅助人伤计算: 比率=1.64, 得分=12.8")
    
    print("\n拆塔潮光：")
    print("评分 搅史的棍: 职责=拆塔, 职业=潮光, 位置=拆塔")
    print("🌊 潮光拆塔职责评分 (不考核清泉)")
    print("# 只显示建筑伤害和次要击杀评分，没有清泉")
    
    print("\n6️⃣ 权重平衡验证")
    print("=" * 50)
    print("✅ 辅助潮光总权重：清泉(25) + 主要考核(20) + 重伤(-8) = 37分影响")
    print("✅ 非辅助潮光总权重：主要考核(20-25) + 次要考核(1.5-2.0) + 重伤(-8) = 15-19分影响")
    print("✅ 辅助潮光评分范围更大，体现其双重职责的重要性")
    print("✅ 非辅助潮光专注单一职责，评分更集中")
    
    print("\n🎉 潮光团队类型考核修正完成！")

if __name__ == '__main__':
    test_chaoguan_team_based_assessment()
