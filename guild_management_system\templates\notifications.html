{% extends "base.html" %}

{% block title %}通知中心{% endblock %}

{% block content %}
<style>
    .notifications-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .notifications-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .notifications-header h1 {
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .notification-item {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #3498db;
        transition: all 0.3s ease;
    }
    
    .notification-item:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .notification-item.unread {
        border-left-color: #e74c3c;
        background: linear-gradient(135deg, #fff 0%, #fef9f9 100%);
    }
    
    .notification-item.read {
        border-left-color: #95a5a6;
        opacity: 0.8;
    }
    
    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .notification-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }
    
    .notification-time {
        font-size: 12px;
        color: #7f8c8d;
    }
    
    .notification-message {
        color: #34495e;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .notification-actions {
        display: flex;
        gap: 10px;
    }
    
    .btn {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: #3498db;
        color: white;
    }
    
    .btn-primary:hover {
        background: #2980b9;
    }
    
    .btn-success {
        background: #27ae60;
        color: white;
    }
    
    .btn-success:hover {
        background: #229954;
    }
    
    .notification-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .badge-unread {
        background: #e74c3c;
        color: white;
    }
    
    .badge-read {
        background: #95a5a6;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 20px;
        display: block;
    }
    
    .stats-bar {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-around;
        text-align: center;
    }
    
    .stat-item {
        flex: 1;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
    }
    
    .stat-label {
        font-size: 12px;
        color: #7f8c8d;
        margin-top: 5px;
    }
</style>

<div class="notifications-container">
    <div class="notifications-header">
        <h1>📢 通知中心</h1>
        <p style="color: #6c757d;">查看成员信息修改通知</p>
    </div>
    
    {% if notifications %}
        <!-- 统计信息 -->
        {% set unread_count = notifications | selectattr('is_read', 'equalto', 0) | list | length %}
        {% set total_count = notifications | length %}
        
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number">{{ total_count }}</div>
                <div class="stat-label">总通知</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ unread_count }}</div>
                <div class="stat-label">未读</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_count - unread_count }}</div>
                <div class="stat-label">已读</div>
            </div>
        </div>
        
        <!-- 通知列表 -->
        {% for notification in notifications %}
        <div class="notification-item {% if not notification.is_read %}unread{% else %}read{% endif %}">
            <div class="notification-header">
                <h3 class="notification-title">
                    {{ notification.title }}
                    <span class="notification-badge {% if not notification.is_read %}badge-unread{% else %}badge-read{% endif %}">
                        {% if not notification.is_read %}未读{% else %}已读{% endif %}
                    </span>
                </h3>
                <span class="notification-time">
                    {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else '未知时间' }}
                </span>
            </div>
            
            <div class="notification-message">
                {{ notification.message }}
            </div>
            
            <div class="notification-actions">
                {% if notification.title == "密码重置申请" and not notification.is_read %}
                    {% set username = notification.message.split(' ')[1] %}
                    <a href="{{ url_for('admin_reset_password', username=username) }}"
                       class="btn btn-warning"
                       onclick="return confirm('确定要为用户 {{ username }} 重置密码吗？')">
                        🔑 重置密码
                    </a>
                {% endif %}
                {% if not notification.is_read %}
                    <a href="{{ url_for('mark_notification_read', notification_id=notification.id) }}"
                       class="btn btn-success">
                        ✓ 标记已读
                    </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
        
    {% else %}
        <div class="empty-state">
            <i class="fas fa-bell-slash"></i>
            <h3>暂无通知</h3>
            <p>当成员修改角色信息时，您会在这里收到通知</p>
        </div>
    {% endif %}
    
    <!-- 返回按钮 -->
    <div style="text-align: center; margin-top: 30px;">
        <a href="{{ url_for('index') }}" class="btn btn-primary">返回首页</a>
    </div>
</div>
{% endblock %}
