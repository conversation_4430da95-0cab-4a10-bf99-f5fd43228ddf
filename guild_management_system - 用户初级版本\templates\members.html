{% extends "base.html" %}

{% block title %}成员详情 - 纸落云烟{% endblock %}

{% block content %}

<!-- 搜索和筛选栏 -->
<form method="GET" action="/members" id="filterForm">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 25px; border-radius: 15px; margin-bottom: 25px; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);">
        <div style="display: flex; gap: 15px; flex-wrap: wrap; align-items: center;">
            <!-- 搜索框 -->
            <div style="flex: 1; min-width: 300px; position: relative;">
                <input type="text" name="search" id="searchInput" placeholder="🔍 搜索成员姓名、职业或团队..." value="{{ search }}"
                       style="width: 100%; padding: 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                              font-size: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;
                              backdrop-filter: blur(10px);">
                {% if search %}
                <a href="/members" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%);
                                         color: #6c757d; text-decoration: none; font-size: 18px; font-weight: bold;">✕</a>
                {% endif %}
            </div>

            <!-- 筛选选择框 -->
            <select name="main_group" id="mainGroupFilter"
                    style="padding: 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                           font-size: 16px; min-width: 130px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                           backdrop-filter: blur(10px); cursor: pointer; transition: all 0.3s ease;">
                <option value="all" {% if main_group == 'all' %}selected{% endif %}>🏰 全部主团</option>
                <option value="进攻团" {% if main_group == '进攻团' %}selected{% endif %}>⚔️ 进攻团</option>
                <option value="防守团" {% if main_group == '防守团' %}selected{% endif %}>🛡️ 防守团</option>
                <option value="替补请假" {% if main_group == '替补请假' %}selected{% endif %}>⏸️ 替补/请假</option>
                <option value="帮外" {% if main_group == '帮外' %}selected{% endif %}>🌐 帮外</option>
            </select>

            <select name="sub_team" id="subTeamFilter"
                    style="padding: 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                           font-size: 16px; min-width: 130px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                           backdrop-filter: blur(10px); cursor: pointer; transition: all 0.3s ease;">
                <option value="all" {% if sub_team == 'all' %}selected{% endif %}>👥 全部子团</option>
                <option value="一团" {% if sub_team == '一团' %}selected{% endif %}>1️⃣ 一团</option>
                <option value="二团" {% if sub_team == '二团' %}selected{% endif %}>2️⃣ 二团</option>
                <option value="三团" {% if sub_team == '三团' %}selected{% endif %}>3️⃣ 三团</option>
            </select>

            <select name="squad" id="squadFilter"
                    style="padding: 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                           font-size: 16px; min-width: 130px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                           backdrop-filter: blur(10px); cursor: pointer; transition: all 0.3s ease;">
                <option value="all" {% if squad == 'all' %}selected{% endif %}>🔢 全部小队</option>
                <option value="1队" {% if squad == '1队' %}selected{% endif %}>1️⃣ 1队</option>
                <option value="2队" {% if squad == '2队' %}selected{% endif %}>2️⃣ 2队</option>
                <option value="3队" {% if squad == '3队' %}selected{% endif %}>3️⃣ 3队</option>
                <option value="4队" {% if squad == '4队' %}selected{% endif %}>4️⃣ 4队</option>
                <option value="5队" {% if squad == '5队' %}selected{% endif %}>5️⃣ 5队</option>
            </select>

            <select name="profession" id="professionFilter"
                    style="padding: 15px 20px; border: none; border-radius: 12px; background: rgba(255, 255, 255, 0.95);
                           font-size: 16px; min-width: 130px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                           backdrop-filter: blur(10px); cursor: pointer; transition: all 0.3s ease;">
                <option value="all" {% if profession == 'all' %}selected{% endif %}>⚡ 全部职业</option>
                <option value="素问" {% if profession == '素问' %}selected{% endif %}>💖 素问</option>
                <option value="铁衣" {% if profession == '铁衣' %}selected{% endif %}>🛡️ 铁衣</option>
                <option value="潮光" {% if profession == '潮光' %}selected{% endif %}>💧 潮光</option>
                <option value="九灵" {% if profession == '九灵' %}selected{% endif %}>🔮 九灵</option>
                <option value="龙吟" {% if profession == '龙吟' %}selected{% endif %}>🐉 龙吟</option>
                <option value="血河" {% if profession == '血河' %}selected{% endif %}>🩸 血河</option>
                <option value="碎梦" {% if profession == '碎梦' %}selected{% endif %}>💀 碎梦</option>
                <option value="玄机" {% if profession == '玄机' %}selected{% endif %}>⚙️ 玄机</option>
                <option value="神相" {% if profession == '神相' %}selected{% endif %}>🔯 神相</option>
                <option value="沧澜" {% if profession == '沧澜' %}selected{% endif %}>🌊 沧澜</option>
            </select>
        </div>

        <!-- 快速重置提示 -->
        {% if search or main_group != 'all' or sub_team != 'all' or squad != 'all' or profession != 'all' %}
        <div style="margin-top: 15px; text-align: center;">
            <a href="/members" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; font-size: 14px;
                                     padding: 8px 16px; border-radius: 20px; background: rgba(255, 255, 255, 0.1);
                                     transition: all 0.3s ease; display: inline-block;">
                🔄 清除所有筛选条件
            </a>
        </div>
        {% endif %}
    </div>
</form>

<!-- 成员列表表格 -->
<div class="stat-card">
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">序号</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">成员姓名</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">职业</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">主团</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">子团</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">小队</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">位置</th>
                    <th style="padding: 15px; text-align: left; font-weight: bold; color: #495057;">状态</th>
                    <th style="padding: 15px; text-align: center; font-weight: bold; color: #495057;">详细信息</th>
                </tr>
            </thead>
            <tbody id="memberTableBody">
                {% for member in members %}
                <tr class="member-row" style="border-bottom: 1px solid #dee2e6; transition: background-color 0.3s ease;">
                    <td style="padding: 15px; color: #6c757d;">{{ (pagination.page - 1) * pagination.per_page + loop.index }}</td>
                    <td style="padding: 15px;">
                        <div style="font-weight: bold; color: #2c3e50; font-size: 16px; cursor: pointer;" onclick="viewMemberDetail('{{ member.name }}')">
                            {{ member.name }}
                            <span style="font-size: 12px; color: #007bff; margin-left: 5px;">📊</span>
                        </div>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge profession-{{ member.profession }}">{{ member.profession }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: {% if member.get('main_group') == '进攻团' %}#28a745{% elif member.get('main_group') == '防守团' %}#dc3545{% else %}#6c757d{% endif %}; color: white;">{{ member.get('main_group', '其他团') }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #667eea; color: white;">{% set sub_team = member.get('sub_team', '一团') %}{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #17a2b8; color: white;">{{ member.get('squad', '1队') }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: #f0f0f0; color: #666;">{{ member.position }}</span>
                    </td>
                    <td style="padding: 15px;">
                        <span class="profession-badge" style="background: {% if member.status == '主力' %}#007bff{% elif member.status == '替补' %}#ffc107{% else %}#6c757d{% endif %}; color: white;">{{ member.status or '主力' }}</span>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <button class="detail-btn" onclick="viewMemberDetail('{{ member.name }}')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-size: 14px;">
                            查看详情
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 分页控件 -->
    {% if pagination.total_pages > 1 %}
    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
        <!-- 分页信息 -->
        <div style="color: #6c757d; font-size: 14px;">
            {% set end_item = pagination.page * pagination.per_page %}
            {% if end_item > pagination.total %}{% set end_item = pagination.total %}{% endif %}
            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - {{ end_item }} 条，共 {{ pagination.total }} 条记录
        </div>

        <!-- 分页按钮 -->
        <div style="display: flex; gap: 5px; align-items: center;">
            <!-- 上一页 -->
            {% if pagination.has_prev %}
                <a href="{{ url_for('members_list', page=pagination.prev_num, search=search, main_group=main_group, sub_team=sub_team, squad=squad, profession=profession) }}"
                   style="padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 14px;">
                    ← 上一页
                </a>
            {% else %}
                <span style="padding: 8px 12px; background: #e9ecef; color: #6c757d; border-radius: 5px; font-size: 14px;">
                    ← 上一页
                </span>
            {% endif %}

            <!-- 页码 -->
            {% for page_num in pagination.pages %}
                {% if page_num == pagination.page %}
                    <span style="padding: 8px 12px; background: #007bff; color: white; border-radius: 5px; font-size: 14px; font-weight: bold;">
                        {{ page_num }}
                    </span>
                {% else %}
                    <a href="{{ url_for('members_list', page=page_num, search=search, main_group=main_group, sub_team=sub_team, squad=squad, profession=profession) }}"
                       style="padding: 8px 12px; background: #e9ecef; color: #495057; text-decoration: none; border-radius: 5px; font-size: 14px;">
                        {{ page_num }}
                    </a>
                {% endif %}
            {% endfor %}

            <!-- 下一页 -->
            {% if pagination.has_next %}
                <a href="{{ url_for('members_list', page=pagination.next_num, search=search, main_group=main_group, sub_team=sub_team, squad=squad, profession=profession) }}"
                   style="padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 14px;">
                    下一页 →
                </a>
            {% else %}
                <span style="padding: 8px 12px; background: #e9ecef; color: #6c757d; border-radius: 5px; font-size: 14px;">
                    下一页 →
                </span>
            {% endif %}
        </div>

        <!-- 每页显示数量选择 -->
        <div style="display: flex; align-items: center; gap: 10px;">
            <span style="color: #6c757d; font-size: 14px;">每页显示：</span>
            <select onchange="changePerPage(this.value)" style="padding: 5px 10px; border: 1px solid #dee2e6; border-radius: 5px; font-size: 14px;">
                <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                <option value="20" {% if pagination.per_page == 20 %}selected{% endif %}>20</option>
                <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
            </select>
        </div>
    </div>
    {% endif %}

    <!-- 统计信息 -->
    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6;">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">📊 组织架构统计</h3>

        <!-- 总体统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: 15px; text-align: center; margin-bottom: 30px;">
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #667eea;">{{ all_members|length }}</div>
                <div style="color: #6c757d;">总成员数</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #28a745;">{{ all_members|selectattr('main_group', 'equalto', '进攻团')|list|length }}</div>
                <div style="color: #6c757d;">进攻团</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #dc3545;">{{ all_members|selectattr('main_group', 'equalto', '防守团')|list|length }}</div>
                <div style="color: #6c757d;">防守团</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #ffc107;">{{ all_members|selectattr('status', 'in', ['替补', '请假'])|list|length }}</div>
                <div style="color: #6c757d;">替补/请假</div>
            </div>
            <div>
                <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">{{ all_members|selectattr('status', 'equalto', '帮外')|list|length }}</div>
                <div style="color: #6c757d;">帮外</div>
            </div>
        </div>

        <!-- 筛选结果统计 -->
        {% if search or main_group != 'all' or sub_team != 'all' or squad != 'all' or profession != 'all' %}
        <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
            <div style="color: #1976d2; font-size: 16px; font-weight: bold;">
                📋 当前筛选结果：{{ pagination.total }} 条记录
            </div>
            <div style="color: #424242; font-size: 14px; margin-top: 5px;">
                {% if search %}搜索："{{ search }}" | {% endif %}
                {% if main_group != 'all' %}主团：{{ main_group }} | {% endif %}
                {% if sub_team != 'all' %}子团：{{ sub_team }} | {% endif %}
                {% if squad != 'all' %}小队：{{ squad }} | {% endif %}
                {% if profession != 'all' %}职业：{{ profession }}{% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 详细统计 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
            <!-- 进攻团统计 -->
            {% set attack_members = all_members|selectattr('main_group', 'equalto', '进攻团')|list %}
            {% if attack_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #28a745;">进攻团 ({{ attack_members|length }}人)</h4>
                {% for sub_team in ['一团', '二团', '三团'] %}
                    {% set sub_members = attack_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                    {% if sub_members %}
                    <div style="margin: 5px 0; font-size: 14px;">
                        <strong>{{ sub_team }}</strong>: {{ sub_members|length }}人
                        <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}

            <!-- 防守团统计 -->
            {% set defense_members = all_members|selectattr('main_group', 'equalto', '防守团')|list %}
            {% if defense_members %}
            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #dc3545;">防守团 ({{ defense_members|length }}人)</h4>
                {% for sub_team in ['防守团', '防守二团', '防守三团', '防守四团'] %}
                    {% set sub_members = defense_members|selectattr('sub_team', 'equalto', sub_team)|list %}
                    {% if sub_members %}
                    <div style="margin: 5px 0; font-size: 14px;">
                        <strong>{% if sub_team == '防守团' %}一团{% elif sub_team == '防守二团' %}二团{% elif sub_team == '防守三团' %}三团{% elif sub_team == '防守四团' %}四团{% else %}{{ sub_team }}{% endif %}</strong>: {{ sub_members|length }}人
                        <div style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            {% for squad in ['1队', '2队', '3队', '4队', '5队'] %}
                                {% set squad_members = sub_members|selectattr('squad', 'equalto', squad)|list %}
                                {% if squad_members %}{{ squad }}({{ squad_members|length }}){% if not loop.last %}, {% endif %}{% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
/* 筛选栏悬停效果 */
#searchInput:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}

select:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}

select:focus {
    outline: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
}

/* 重置按钮悬停效果 */
a[href="/members"]:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px);
}

/* 搜索框清除按钮悬停效果 */
.search-clear:hover {
    background: rgba(108, 117, 125, 0.1);
    border-radius: 50%;
}
</style>

<script>
// 获取所有成员行
const memberRows = document.querySelectorAll('.member-row');

// 表格行悬停效果
memberRows.forEach(function(row) {
    row.addEventListener('mouseenter', function() {
        this.style.backgroundColor = '#f8f9fa';
    });

    row.addEventListener('mouseleave', function() {
        this.style.backgroundColor = '';
    });
});

// 查看成员详情
function viewMemberDetail(memberName) {
    window.location.href = '/member_detail/' + encodeURIComponent(memberName);
}

// 改变每页显示数量
function changePerPage(perPage) {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('per_page', perPage);
    urlParams.set('page', '1'); // 重置到第一页
    window.location.href = '/members?' + urlParams.toString();
}

// 自动提交筛选表单（当选择框改变时）
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('filterForm');
    const selects = form.querySelectorAll('select');

    selects.forEach(function(select) {
        select.addEventListener('change', function() {
            // 重置到第一页
            const pageInput = document.createElement('input');
            pageInput.type = 'hidden';
            pageInput.name = 'page';
            pageInput.value = '1';
            form.appendChild(pageInput);

            form.submit();
        });
    });

    // 搜索框延迟提交（避免频繁请求）
    const searchInput = document.getElementById('searchInput');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            // 重置到第一页
            const pageInput = document.createElement('input');
            pageInput.type = 'hidden';
            pageInput.name = 'page';
            pageInput.value = '1';
            form.appendChild(pageInput);

            form.submit();
        }, 500); // 500ms延迟
    });

    console.log('成员管理页面加载完成，当前页显示 ' + memberRows.length + ' 名成员');
});

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+F 聚焦搜索框
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('searchInput').focus();
    }
});
</script>
{% endblock %}
