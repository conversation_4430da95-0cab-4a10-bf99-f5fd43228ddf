#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

print("当前工作目录:", os.getcwd())
print("数据目录存在:", os.path.exists('data'))

if os.path.exists('data/guild_members.json'):
    with open('data/guild_members.json', 'r', encoding='utf-8') as f:
        members = json.load(f)
    print(f"成员数据: {len(members)} 个成员")
    if members:
        print(f"第一个成员: {members[0]['name']}")
else:
    print("成员数据文件不存在")

if os.path.exists('data/battle_records.json'):
    with open('data/battle_records.json', 'r', encoding='utf-8') as f:
        battles = json.load(f)
    print(f"战斗记录: {len(battles)} 条记录")
else:
    print("战斗记录文件不存在")
