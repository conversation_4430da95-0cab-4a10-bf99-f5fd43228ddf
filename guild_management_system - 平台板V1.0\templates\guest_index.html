<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆水寒帮会辅助管理系统 - 角色搜索</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body style="margin: 0; padding: 0; font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        text-align: center;
        margin-bottom: 40px;
    }
    
    .search-container {
        max-width: 600px;
        margin: 0 auto;
        position: relative;
    }
    
    .search-input {
        width: 100%;
        padding: 20px 60px 20px 25px;
        border: none;
        border-radius: 50px;
        font-size: 18px;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .search-input:focus {
        outline: none;
        box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        transform: translateY(-2px);
    }
    
    .search-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        color: white;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .search-btn:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    
    .register-banner {
        background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin: 30px auto;
        max-width: 800px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .register-banner h3 {
        margin: 0 0 10px 0;
        font-size: 24px;
    }
    
    .register-banner p {
        margin: 0 0 15px 0;
        font-size: 16px;
        opacity: 0.9;
    }
    
    .register-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
        display: inline-block;
        margin: 0 10px;
    }
    
    .register-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .search-results {
        margin-top: 40px;
    }
    
    .member-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid rgba(255,255,255,0.8);
        position: relative;
        overflow: hidden;
    }

    .member-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .member-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: rgba(102, 126, 234, 0.4);
    }

    .member-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .profession-badge {
        padding: 8px 14px;
        border-radius: 20px;
        color: white;
        font-weight: bold;
        font-size: 13px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        min-width: 50px;
        text-align: center;
        flex-shrink: 0;
    }

    .member-details {
        flex: 1;
        min-width: 0;
    }

    .member-name {
        font-size: 18px;
        font-weight: bold;
        margin: 0 0 6px 0;
        color: #2c3e50;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .member-team {
        color: #6c757d;
        font-size: 13px;
        line-height: 1.3;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    .guild-tag {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: bold;
        display: inline-block;
        flex-shrink: 0;
    }

    .view-detail-icon {
        color: #667eea;
        font-size: 16px;
        transition: all 0.3s ease;
        flex-shrink: 0;
    }

    .member-card:hover .view-detail-icon {
        color: #764ba2;
        transform: translateX(3px);
    }
    
    .hot-searches {
        margin-top: 30px;
        text-align: center;
    }
    
    .hot-tag {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        margin: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .hot-tag:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }
    
    .feature-preview {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin: 30px auto;
        max-width: 800px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .feature-item {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .feature-icon {
        font-size: 40px;
        margin-bottom: 10px;
        color: #667eea;
    }
</style>

<!-- 英雄区域 -->
<div class="hero-section">
    <div class="container">
        <h1 style="font-size: 48px; margin-bottom: 20px; font-weight: bold;">🔍 角色搜索</h1>
        <p style="font-size: 20px; margin-bottom: 40px; opacity: 0.9;">搜索逆水寒帮会成员信息</p>
        
        <!-- 搜索框 -->
        <div class="search-container">
            <input type="text" id="searchInput" class="search-input" placeholder="输入角色名字搜索..." autocomplete="off">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>
        </div>
        
        <!-- 热门搜索 -->
        <div class="hot-searches">
            <p style="margin-bottom: 15px; opacity: 0.8;">热门搜索：</p>
            <span class="hot-tag" onclick="searchByTag('素问')">素问</span>
            <span class="hot-tag" onclick="searchByTag('铁衣')">铁衣</span>
            <span class="hot-tag" onclick="searchByTag('潮光')">潮光</span>
            <span class="hot-tag" onclick="searchByTag('九灵')">九灵</span>
            <span class="hot-tag" onclick="searchByTag('龙吟')">龙吟</span>
            <span class="hot-tag" onclick="searchByTag('进攻团')">进攻团</span>
            <span class="hot-tag" onclick="searchByTag('防守团')">防守团</span>
        </div>
    </div>
</div>

<!-- 注册引导横幅 -->
<div class="register-banner">
    <h3>🎮 解锁完整功能</h3>
    <p>注册账号即可使用拖拽排表、战斗分析、成员管理等强大功能</p>
    <a href="{{ url_for('register') }}" class="register-btn">
        <i class="fas fa-user-plus"></i> 立即注册
    </a>
    <a href="{{ url_for('login') }}" class="register-btn">
        <i class="fas fa-sign-in-alt"></i> 已有账号
    </a>
</div>

<!-- 搜索结果区域 -->
<div class="container">
    <div id="searchResults" class="search-results" style="display: none;">
        <h3 style="margin-bottom: 20px; color: white; text-align: center; font-size: 24px; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">搜索结果</h3>
        <div id="resultsContainer" style="max-width: 800px; margin: 0 auto;"></div>
    </div>
</div>

<!-- 功能预览 -->
<div class="feature-preview">
    <h3 style="text-align: center; color: #2c3e50; margin-bottom: 10px;">✨ 完整功能预览</h3>
    <p style="text-align: center; color: #7f8c8d; margin-bottom: 20px;">注册后即可使用以下强大功能</p>
    
    <div class="feature-grid">
        <div class="feature-item">
            <div class="feature-icon">📊</div>
            <h5>战斗数据分析</h5>
            <p style="font-size: 14px; color: #7f8c8d;">详细的战斗表现分析和建议</p>
        </div>
        <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <h5>拖拽排表</h5>
            <p style="font-size: 14px; color: #7f8c8d;">可视化团队配置和成员调配</p>
        </div>
        <div class="feature-item">
            <div class="feature-icon">👥</div>
            <h5>成员管理</h5>
            <p style="font-size: 14px; color: #7f8c8d;">完整的帮会成员信息管理</p>
        </div>
        <div class="feature-item">
            <div class="feature-icon">🏆</div>
            <h5>光荣墙</h5>
            <p style="font-size: 14px; color: #7f8c8d;">展示优秀成员和战斗成就</p>
        </div>
    </div>
</div>

<script>
// 职业颜色映射
const professionColors = {
    '素问': '#e91e63',
    '九灵': '#9c27b0', 
    '潮光': '#2196f3',
    '血河': '#f44336',
    '神相': '#3f51b5',
    '玄机': '#ff9800',
    '铁衣': '#ff5722',
    '龙吟': '#4caf50',
    '碎梦': '#009688',
    '沧澜': '#8bc34a'
};

// 搜索功能
function performSearch() {
    const query = document.getElementById('searchInput').value.trim();
    if (!query) {
        alert('请输入搜索关键词');
        return;
    }
    
    searchMembers(query);
}

function searchByTag(tag) {
    document.getElementById('searchInput').value = tag;
    searchMembers(tag);
}

function searchMembers(query) {
    fetch(`/api/search_members?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data, query);
        })
        .catch(error => {
            console.error('搜索失败:', error);
            alert('搜索失败，请稍后重试');
        });
}

function displaySearchResults(members, query) {
    const resultsContainer = document.getElementById('resultsContainer');
    const searchResults = document.getElementById('searchResults');
    
    if (members.length === 0) {
        resultsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h4>未找到相关角色</h4>
                <p>尝试搜索其他关键词，或者检查拼写是否正确</p>
            </div>
        `;
    } else {
        resultsContainer.innerHTML = members.map(member => `
            <div class="member-card" onclick="viewMemberDetail('${member.name}')">
                <div class="member-info">
                    <div class="profession-badge" style="background-color: ${professionColors[member.profession] || '#6c757d'}">
                        ${member.profession}
                    </div>
                    <div class="member-details">
                        <div class="member-name">${member.name}</div>
                        <div class="member-team">
                            ${member.guild_name ? `<span class="guild-tag">${member.guild_name}</span>` : ''}
                            <span style="color: #8e8e93; font-size: 12px;">
                                ${member.main_group && member.main_group !== '未分配' ? member.main_group.replace('团', '') : ''}
                                ${member.sub_team && member.sub_team !== '未分配' ? ` · ${member.sub_team}` : ''}
                            </span>
                        </div>
                    </div>
                    <div class="view-detail-icon">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    searchResults.style.display = 'block';
    searchResults.scrollIntoView({ behavior: 'smooth' });
}

function viewMemberDetail(memberName) {
    window.location.href = `/member_detail/${encodeURIComponent(memberName)}`;
}

// 回车搜索
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// 自动聚焦搜索框
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').focus();
});
</script>
</body>
</html>
