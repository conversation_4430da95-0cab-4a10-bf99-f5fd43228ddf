#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正小队编号为数字格式：1队、2队、3队、4队、5队
"""

import json
import os

def load_members():
    """加载成员数据"""
    try:
        with open('data/guild_members.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("成员数据文件不存在")
        return []

def save_members(members):
    """保存成员数据"""
    os.makedirs('data', exist_ok=True)
    with open('data/guild_members.json', 'w', encoding='utf-8') as f:
        json.dump(members, f, ensure_ascii=False, indent=2)

def fix_squad_numbers():
    """修正小队编号"""
    print("=" * 60)
    print("🔧 修正小队编号为数字格式")
    print("=" * 60)
    
    members = load_members()
    print(f"加载了 {len(members)} 个成员")
    
    # 统计当前小队分布
    current_squads = {}
    for member in members:
        squad = member.get('squad', '未分配小队')
        current_squads[squad] = current_squads.get(squad, 0) + 1
    
    print("\n当前小队分布:")
    for squad, count in sorted(current_squads.items()):
        print(f"  {squad}: {count}人")
    
    # 按团队分配小队编号
    team_squad_counters = {}
    
    for member in members:
        team = member.get('team', '未分配')
        
        # 初始化团队的小队计数器
        if team not in team_squad_counters:
            team_squad_counters[team] = 1
        
        # 根据团队分配小队
        if team == '1团':
            # 1团只有1个队伍，所有人都是1队
            member['squad'] = '1队'
        elif team == '2团':
            # 2团有2个队伍，平均分配
            squad_num = ((team_squad_counters[team] - 1) % 2) + 1
            member['squad'] = f'{squad_num}队'
            team_squad_counters[team] += 1
        elif team == '3团':
            # 3团有3个队伍，平均分配
            squad_num = ((team_squad_counters[team] - 1) % 3) + 1
            member['squad'] = f'{squad_num}队'
            team_squad_counters[team] += 1
        elif team == '4团':
            # 4团（防守团）按5人一队分配
            squad_num = ((team_squad_counters[team] - 1) // 5) + 1
            member['squad'] = f'{squad_num}队'
            team_squad_counters[team] += 1
        else:
            # 其他情况
            member['squad'] = '1队'
    
    # 统计修改后的小队分布
    new_squads = {}
    team_squads = {}
    
    for member in members:
        squad = member.get('squad', '未分配小队')
        team = member.get('team', '未分配')
        
        new_squads[squad] = new_squads.get(squad, 0) + 1
        
        if team not in team_squads:
            team_squads[team] = {}
        team_squads[team][squad] = team_squads[team].get(squad, 0) + 1
    
    print("\n修改后小队分布:")
    for squad, count in sorted(new_squads.items()):
        print(f"  {squad}: {count}人")
    
    print("\n各团队小队分布:")
    for team in ['1团', '2团', '3团', '4团']:
        if team in team_squads:
            print(f"  {team}:")
            for squad, count in sorted(team_squads[team].items()):
                print(f"    {squad}: {count}人")
        else:
            print(f"  {team}: 无成员")
    
    # 保存修改
    save_members(members)
    print(f"\n✅ 小队编号已修正并保存")

def main():
    """主函数"""
    print("🔧 小队编号修正工具")
    
    fix_squad_numbers()
    
    print("\n" + "=" * 60)
    print("✅ 小队编号修正完成！")
    print("=" * 60)
    print("小队编号规则:")
    print("  1团: 1个队伍 -> 全部1队")
    print("  2团: 2个队伍 -> 1队、2队平均分配")
    print("  3团: 3个队伍 -> 1队、2队、3队平均分配")
    print("  4团: 防守团 -> 按5人一队分配（1队、2队、3队、4队、5队）")

if __name__ == "__main__":
    main()
