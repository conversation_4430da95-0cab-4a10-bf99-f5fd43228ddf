#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试工具
"""

import time
import requests
import statistics

def test_page_performance(url, page_name, num_requests=5):
    """测试页面性能"""
    print(f"\n测试 {page_name} ({url})")
    print("-" * 40)
    
    response_times = []
    
    for i in range(num_requests):
        start_time = time.time()
        try:
            response = requests.get(url, timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            response_times.append(response_time)
            
            status = "✅" if response.status_code == 200 else "❌"
            print(f"请求 {i+1}: {response_time:.2f}ms {status}")
            
        except Exception as e:
            print(f"请求 {i+1}: 失败 - {e}")
            response_times.append(10000)  # 10秒超时
    
    if response_times:
        avg_time = statistics.mean(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"\n📊 性能统计:")
        print(f"平均响应时间: {avg_time:.2f}ms")
        print(f"最快响应时间: {min_time:.2f}ms")
        print(f"最慢响应时间: {max_time:.2f}ms")
        
        # 性能评级
        if avg_time < 100:
            grade = "🚀 优秀"
        elif avg_time < 300:
            grade = "✅ 良好"
        elif avg_time < 1000:
            grade = "⚠️ 一般"
        else:
            grade = "❌ 需要优化"
        
        print(f"性能评级: {grade}")
        
        return avg_time
    
    return None

def main():
    """主测试函数"""
    base_url = "http://localhost:5888"
    
    print("🔍 开始性能测试...")
    print("=" * 50)
    
    # 测试页面列表
    test_pages = [
        ("/", "首页"),
        ("/members", "成员详情"),
        ("/organization_chart", "组织架构"),
        ("/battle_analysis", "战斗分析"),
    ]
    
    total_times = []
    
    for path, name in test_pages:
        url = base_url + path
        avg_time = test_page_performance(url, name)
        if avg_time:
            total_times.append(avg_time)
        time.sleep(1)  # 间隔1秒
    
    # 总体性能评估
    if total_times:
        overall_avg = statistics.mean(total_times)
        print("\n" + "=" * 50)
        print("📈 总体性能评估")
        print("=" * 50)
        print(f"整体平均响应时间: {overall_avg:.2f}ms")
        
        if overall_avg < 200:
            print("🎉 系统性能优秀！用户体验流畅。")
        elif overall_avg < 500:
            print("✅ 系统性能良好，用户体验较好。")
        elif overall_avg < 1000:
            print("⚠️ 系统性能一般，建议进一步优化。")
        else:
            print("❌ 系统性能较差，需要立即优化。")
        
        # 优化建议
        print("\n💡 优化建议:")
        if overall_avg > 500:
            print("- 启用缓存机制")
            print("- 优化数据库查询")
            print("- 使用CDN加速静态资源")
        if overall_avg > 200:
            print("- 压缩CSS/JS文件")
            print("- 启用Gzip压缩")
        print("- 使用生产级WSGI服务器（如Gunicorn）")

if __name__ == '__main__':
    main()
