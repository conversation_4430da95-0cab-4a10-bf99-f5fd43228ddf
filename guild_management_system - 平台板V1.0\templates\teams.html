{% extends "base.html" %}

{% block title %}团队管理 - 纸落云烟帮会管理系统{% endblock %}

{% block content %}
<!-- 团队详细信息 -->
<div class="team-grid">
    {% for team_name, team_members in teams.items() %}
    <div class="team-card team-{{ team_name[0] }}">
        <div class="team-header">
            {{ team_name }} ({{ team_members|length }}人)
        </div>
        <div class="team-content">
            <!-- 职业分布 -->
            {% set profession_count = {} %}
            {% for member in team_members %}
                {% if profession_count.update({member.profession: profession_count.get(member.profession, 0) + 1}) %}{% endif %}
            {% endfor %}

            <div style="margin-bottom: 15px;">
                {% for prof, count in profession_count.items() %}
                <span class="profession-badge profession-{{ prof }}">{{ prof }}({{ count }}人)</span>
                {% endfor %}
            </div>

            <!-- 成员列表 -->
            <div class="member-list">
                {% for member in team_members %}
                <div class="member-item">
                    <div class="member-info">
                        <div class="member-name">{{ member.name }}</div>
                        <div class="member-details">
                            <span class="profession-badge profession-{{ member.profession }}">{{ member.profession }}</span>
                            <span class="profession-badge" style="background: #f0f0f0; color: #666;">{{ member.position }}</span>
                        </div>
                    </div>
                    <button class="edit-btn" onclick="editMember('{{ member.name }}')">编辑</button>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<script>
// 编辑成员
function editMember(memberName) {
    window.location.href = '/edit_member/' + encodeURIComponent(memberName);
}
</script>
{% endblock %}
