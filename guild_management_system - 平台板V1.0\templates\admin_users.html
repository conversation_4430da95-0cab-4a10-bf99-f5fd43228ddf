{% extends "base.html" %}

{% block title %}用户管理 - 超级管理员{% endblock %}

{% block content %}
<style>
    .users-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .users-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .users-header h2 {
        color: #2c3e50;
        font-size: 2em;
        margin: 0;
    }

    .filters-section {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
    }

    .filter-group label {
        font-weight: bold;
        margin-bottom: 5px;
        color: #555;
    }

    .filter-group input,
    .filter-group select {
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
    }

    .filter-group input:focus,
    .filter-group select:focus {
        outline: none;
        border-color: #667eea;
    }

    .users-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .users-table th,
    .users-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }

    .users-table th {
        background: #f8f9fa;
        font-weight: bold;
        color: #2c3e50;
    }

    .users-table tr:hover {
        background: #f8f9fa;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-info {
        display: flex;
        align-items: center;
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }

    .user-name {
        font-weight: bold;
        color: #2c3e50;
    }

    .user-email {
        font-size: 12px;
        color: #666;
    }

    .role-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        color: white;
    }

    .role-super_admin {
        background: #dc3545;
    }

    .role-guild_leader {
        background: #ff6b6b;
    }

    .role-user {
        background: #4ecdc4;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        color: white;
    }

    .status-active {
        background: #28a745;
    }

    .status-banned {
        background: #dc3545;
    }

    .status-inactive {
        background: #6c757d;
    }

    .guild-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        background: #e9ecef;
        color: #495057;
    }

    .actions-dropdown {
        position: relative;
        display: inline-block;
    }

    .actions-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
    }

    .actions-btn:hover {
        background: #5a6fd8;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        background: white;
        min-width: 160px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        border-radius: 6px;
        z-index: 1;
    }

    .dropdown-content.show {
        display: block;
    }

    .dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;
    }

    .dropdown-item:hover {
        background: #f8f9fa;
    }

    .dropdown-item:last-child {
        border-bottom: none;
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
    }

    .pagination a,
    .pagination span {
        padding: 8px 12px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        text-decoration: none;
        color: #495057;
    }

    .pagination a:hover {
        background: #e9ecef;
    }

    .pagination .current {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .no-users {
        text-align: center;
        padding: 40px;
        color: #7f8c8d;
    }

    .flash-messages {
        margin-bottom: 20px;
    }

    .flash-message {
        padding: 12px 20px;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .flash-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .flash-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>

<!-- Flash消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div class="flash-messages">
            {% for category, message in messages %}
                <div class="flash-message flash-{{ category }}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}

<div class="users-container">
    <div class="users-header">
        <h2>👥 用户管理</h2>
        <div>
            <span style="color: #666;">总计 {{ pagination.total }} 个用户</span>
        </div>
    </div>

    <!-- 筛选器 -->
    <form method="GET" class="filters-section">
        <div class="filter-group">
            <label for="search">搜索用户</label>
            <input type="text" id="search" name="search" value="{{ search }}" 
                   placeholder="用户名、姓名或邮箱">
        </div>
        <div class="filter-group">
            <label for="role">角色筛选</label>
            <select id="role" name="role">
                <option value="all" {{ 'selected' if role_filter == 'all' else '' }}>所有角色</option>
                <option value="super_admin" {{ 'selected' if role_filter == 'super_admin' else '' }}>超级管理员</option>
                <option value="guild_leader" {{ 'selected' if role_filter == 'guild_leader' else '' }}>帮会大当家</option>
                <option value="user" {{ 'selected' if role_filter == 'user' else '' }}>普通用户</option>
            </select>
        </div>
        <div class="filter-group">
            <label for="guild">帮会筛选</label>
            <select id="guild" name="guild">
                <option value="all" {{ 'selected' if guild_filter == 'all' else '' }}>所有帮会</option>
                <option value="none" {{ 'selected' if guild_filter == 'none' else '' }}>无帮会</option>
                {% for guild_id, guild in guilds.items() %}
                <option value="{{ guild_id }}" {{ 'selected' if guild_filter == guild_id else '' }}>
                    {{ guild.name }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="filter-group" style="justify-content: end; align-items: end;">
            <button type="submit" style="padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                🔍 筛选
            </button>
        </div>
    </form>

    <!-- 用户表格 -->
    {% if users %}
        <table class="users-table">
            <thead>
                <tr>
                    <th>用户</th>
                    <th>角色</th>
                    <th>帮会</th>
                    <th>状态</th>
                    <th>注册时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">
                                {{ user.name[0] if user.name else user.username[0] }}
                            </div>
                            <div class="user-details">
                                <div class="user-name">{{ user.name or user.username }}</div>
                                <div class="user-email">{{ user.username }}</div>
                                {% if user.email %}
                                <div class="user-email">{{ user.email }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="role-badge role-{{ user.role }}">
                            {% if user.role == 'super_admin' %}超级管理员
                            {% elif user.role == 'guild_leader' %}大当家
                            {% else %}普通用户{% endif %}
                        </span>
                    </td>
                    <td>
                        {% if user.guild_id %}
                            <span class="guild-badge">{{ guilds.get(user.guild_id, {}).get('name', user.guild_id) }}</span>
                        {% else %}
                            <span style="color: #999;">无帮会</span>
                        {% endif %}
                    </td>
                    <td>
                        <span class="status-badge status-{{ user.status }}">
                            {% if user.status == 'active' %}正常
                            {% elif user.status == 'banned' %}封禁
                            {% else %}停用{% endif %}
                        </span>
                    </td>
                    <td>
                        {{ user.created_time[:10] if user.created_time else '未知' }}
                    </td>
                    <td>
                        <div class="actions-dropdown">
                            <button class="actions-btn" onclick="toggleDropdown(event, '{{ user.username }}')">
                                ⚙️ 操作
                            </button>
                            <div class="dropdown-content" id="dropdown-{{ user.username }}">
                                <div class="dropdown-item" onclick="changeRole('{{ user.username }}')">
                                    🔄 更改角色
                                </div>
                                <div class="dropdown-item" onclick="changeGuild('{{ user.username }}')">
                                    🏰 转移帮会
                                </div>
                                {% if user.status == 'active' %}
                                <div class="dropdown-item" onclick="banUser('{{ user.username }}')">
                                    🚫 封禁用户
                                </div>
                                {% else %}
                                <div class="dropdown-item" onclick="unbanUser('{{ user.username }}')">
                                    ✅ 解除封禁
                                </div>
                                {% endif %}
                                <div class="dropdown-item" onclick="deleteUser('{{ user.username }}')" style="color: #dc3545;">
                                    🗑️ 删除用户
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- 分页 -->
        {% if pagination.total_pages > 1 %}
        <div class="pagination">
            {% if pagination.has_prev %}
                <a href="?page={{ pagination.prev_num }}&search={{ search }}&role={{ role_filter }}&guild={{ guild_filter }}">« 上一页</a>
            {% endif %}
            
            {% for page_num in pagination.pages %}
                {% if page_num == pagination.page %}
                    <span class="current">{{ page_num }}</span>
                {% else %}
                    <a href="?page={{ page_num }}&search={{ search }}&role={{ role_filter }}&guild={{ guild_filter }}">{{ page_num }}</a>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
                <a href="?page={{ pagination.next_num }}&search={{ search }}&role={{ role_filter }}&guild={{ guild_filter }}">下一页 »</a>
            {% endif %}
        </div>
        {% endif %}
    {% else %}
        <div class="no-users">
            <h3>👤 暂无用户</h3>
            <p>没有找到符合条件的用户</p>
        </div>
    {% endif %}
</div>

<script>
    function toggleDropdown(event, username) {
        event.stopPropagation();
        
        // 关闭所有其他下拉菜单
        document.querySelectorAll('.dropdown-content').forEach(dropdown => {
            if (dropdown.id !== `dropdown-${username}`) {
                dropdown.classList.remove('show');
            }
        });
        
        // 切换当前下拉菜单
        const dropdown = document.getElementById(`dropdown-${username}`);
        dropdown.classList.toggle('show');
    }

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-content').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    });

    function changeRole(username) {
        const newRole = prompt('请选择新角色:\n1. user (普通用户)\n2. guild_leader (帮会大当家)\n3. super_admin (超级管理员)', 'user');
        if (newRole && ['user', 'guild_leader', 'super_admin'].includes(newRole)) {
            performUserAction('change_role', username, { new_role: newRole });
        }
    }

    function changeGuild(username) {
        const guilds = {{ guilds | tojson }};
        let options = 'none (无帮会)\n';
        for (const [id, guild] of Object.entries(guilds)) {
            options += `${id} (${guild.name})\n`;
        }
        
        const newGuildId = prompt('请选择新帮会:\n' + options, 'none');
        if (newGuildId !== null) {
            performUserAction('change_guild', username, { new_guild_id: newGuildId });
        }
    }

    function banUser(username) {
        if (confirm(`确定要封禁用户 ${username} 吗？`)) {
            performUserAction('ban_user', username);
        }
    }

    function unbanUser(username) {
        if (confirm(`确定要解除用户 ${username} 的封禁吗？`)) {
            performUserAction('unban_user', username);
        }
    }

    function deleteUser(username) {
        if (confirm(`⚠️ 警告：确定要删除用户 ${username} 吗？此操作不可恢复！`)) {
            if (confirm('请再次确认删除操作！')) {
                performUserAction('delete_user', username);
            }
        }
    }

    function performUserAction(action, username, extraData = {}) {
        const data = {
            action: action,
            username: username,
            ...extraData
        };

        fetch('/admin/user_action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('操作失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请稍后重试');
        });
    }
</script>
{% endblock %}
